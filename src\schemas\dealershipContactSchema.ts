import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';

export const dealershipContactSchema = Yup.object().shape({
  nameSeller: Yup.string()
    .min(3, 'Ingresar nombre valido!')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),
  phoneSeller: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  // agencyName: createSelectInputValidator('Selecciona la agencia'),
  agencyName: Yup.string().min(1, 'Ingresar nombre valido!').required('Campo obligatorio!'),
  name: Yup.string()
    .min(3, 'Ingresar nombre completo')
    .max(50, 'Demasiados')
    .trim()
    .required('Campo obligatorio!'),
  email: Yup.string().matches(
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    'Email invalido'
  ),
  phone: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  message: Yup.string(),
  city: createSelectInputValidator('Selecciona tu ciudad'),
});

export const dealershipContactUSSchema = Yup.object().shape({
  nameSeller: Yup.string().min(3, 'Invalid name').max(45, 'To large!').trim().required('Name required'),
  phoneSeller: Yup.string()
    .min(10, 'At least 10 characters')
    .max(10, 'No more than 10 characters')
    .required('Phone required'),
  agencyName: Yup.string().min(1, 'Invalid name').required('Name required'),
  name: Yup.string().min(3, 'Invalid name').max(45, 'To large!').trim().required('Name required'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Invalid email'
    )
    .required('Email required'),
  phone: Yup.string()
    .min(10, 'At least 10 characters')
    .max(10, 'No more than 10 characters')
    .required('Phone required'),
  // state: createSelectInputValidator('Choose a state'),
  // city: createSelectInputValidator('Choose a city'),
  state: Yup.string().required('Choose a state'),
  city: Yup.string().required('Choose a city'),
});
