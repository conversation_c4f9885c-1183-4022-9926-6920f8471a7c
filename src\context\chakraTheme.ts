import { extendTheme } from '@chakra-ui/theme-utils';

// let mode = localStorage.getItem('chakra-ui-color-mode')
export const theme = extendTheme({
  components: {
    Drawer: {
      sizes: {
        custom: {
          dialog: { maxWidth: '250px' },
        },
      },
      parts: ['dialog', 'header', 'body'],
      variants: {
        primary: {
          secondary: {
            dialog: {
              maxW: '230px',
              w: '200px',
            },
          },
        },
      },
    },
  },
  colors: {
    // bg: mode === 'light' ? "#FAFAFF" : "#1A202C",
    bgColor: '#FAFAFF',
    purple: {
      strong: '#6210FF',
      soft: '#742BFA',
      opaque: '#9E8EFF',
      button: 'linear-gradient(95.48deg, #6210FF 9.55%, #A74DF9 128.92%)',
    },
    plan: {
      personal: {
        bgColor: 'linear-gradient(107.77deg, #456FE8 15.95%, #0078D2 100%)',
        circles: 'linear-gradient(180deg, rgba(25, 175, 255, 0.42) 0%, rgba(255, 248, 240, 0) 100%)',
        color: '#028CF3',
      },
      platform: {
        bgColor: 'linear-gradient(107.77deg, #6210FF 15.95%, #A74DF9 100%)',
        circles: 'linear-gradient(180deg, rgba(10, 9, 12, 0.44) 0%, rgba(255, 248, 240, 0) 100%)',
        color: 'purple.strong',
      },
    },
    lineBgColor: '#9E8EFF',
    card: {
      title: '#464E5F',
      subtitle: '#464E5F',
      body: '#818181',
      tagText: '#FFFFFF',
    },
    text: {
      title: '#1A202C',
      subtitle: '#6F6C90',
      description: '#464E5F',
      main: '#464E5F',
    },
    footer: {
      main: '#6210FF',
      personal: 'linear-gradient(275.28deg, #00D2FF 0%, #028CF3 72.36%)',
      platform: 'linear-gradient(95.31deg, #6210FF 35.27%, #A74DF9 100%)',
      electrics: '#1A1A1A',
    },
  },
  breakpoints: {
    sm: '550px',
    md: '768px',
    l: '1000px',
    cards: '1180px',
    lg: '1350px',
    xl: '1400px',
    xxl: '1536px',
    '3xl': '1650px',
  },
});
