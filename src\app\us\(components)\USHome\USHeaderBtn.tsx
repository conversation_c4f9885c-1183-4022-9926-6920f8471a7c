'use client';
import useHandleResize from '@/components/useHandleResize';
import ReserveNowBtn from '../ReserveNowBtn';
import { handleSmoothScroll } from '../USNav/USNav';

export function ReserveMobBtn() {
  return <ReserveNowBtn onClick={(e) => handleSmoothScroll(e, 'reserve-now')} />;
}

export function ReserveDeskBtn() {
  const isMobile = useHandleResize({ breakpoint: 1000 });

  return <>{!isMobile && <ReserveNowBtn onClick={(e) => handleSmoothScroll(e, 'reserve-now')} />}</>;
}
