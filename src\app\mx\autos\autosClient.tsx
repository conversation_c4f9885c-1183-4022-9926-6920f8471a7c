'use client';
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect } from 'react';
import { Divider, Flex, TabPanel, TabPanels, Tabs, Text } from '@chakra-ui/react';
import CarGrid from '@/components/CarCards/CarGrid';
// import PlanTabs from '@/components/CarCards/PlanTabs';
import useTabPlanSelected from '@/store/zustand/planSelected';

export default function Catalogue({ personal, platform }: { personal: any; platform: any }) {
  const planTabSelected = useTabPlanSelected();

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Flex as="main" w="100%" justifyContent="center" h="100%" minH="100vh" pb="150px" bgColor="#FAFAFF">
        <Flex w="100%" flexDir="column" alignItems="center">
          <Flex
            w="90%"
            py="50px"
            justifyContent="center"
            flexDir="column"
            sx={{
              '> *:not(:last-child)': { mb: 30 },
            }}
            fontFamily="Plus-Jakarta-Sans"
            color="text.main"
          >
            <Text fontSize="30px" fontWeight={700}>
              Catalago de autos
            </Text>

            <Divider w="42px" border={'2px solid #9E8EFF'} borderRadius="3px" />
          </Flex>

          <Flex w={{ base: '100%', l: '85%', cards: '100%', lg: '80%', xxl: '70%' }} justifyContent="center">
            <Flex w={{ base: '100%', md: '90%', lg: '100%' }} justifyContent="center">
              <Tabs
                display="flex"
                flexDir="column"
                alignItems="center"
                w="100%"
                defaultIndex={planTabSelected.planSelected}
              >
                {/* Aqui esta la seleccion de plan personal y plan plataformas */}
                {/* <PlanTabs /> */}

                <TabPanels>
                  <TabPanel justifyContent="center" p={0}>
                    {/* Aqui se renderizan las cards de los coches plataforma  */}
                    <CarGrid plan={platform} />
                  </TabPanel>

                  <TabPanel justifyContent="center" p={0}>
                    {/* Aqui se renderizan las cards de los coches personales  */}
                    <CarGrid plan={personal} planOptions />
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
}
