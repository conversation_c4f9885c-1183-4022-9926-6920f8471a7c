import Image from 'next/image';
import descubre from '@/assets/companyLogos/descubre.png';
import economista from '@/assets/companyLogos/economista.png';
import insiderMexico from '@/assets/companyLogos/insiderMexico.png';
import mundoStartups from '@/assets/companyLogos/mundoStartups.png';
import techla from '@/assets/companyLogos/techla.png';

const about = [
  { logo: economista },
  { logo: descubre },
  { logo: insiderMexico },
  { logo: techla },
  { logo: mundoStartups },
];

function LogoCompanies() {
  return (
    <div
      className="
      w-full 
      h-max-content 
      min-h-[180px] 
      px-2 md:px-5 
      bg-purple-soft 
      flex 
      flex-col md:flex-row 
      items-center 
      justify-center 
      py-2 md:py-0
      [&>*:not(:last-child)]:mb-[3px] md:[&>*:not(:last-child)]:mb-0
    "
    >
      {about.map((e, i) => (
        <div
          key={i}
          className="
            w-[200px] md:w-[250px]
            h-[90px]
            flex
            items-center
            justify-center
          "
        >
          <Image
            src={e.logo}
            alt="Company logo"
            width={150}
            height={60}
            className="w-auto h-auto object-contain"
            loading="lazy"
          />
        </div>
      ))}
    </div>
  );
}

export default LogoCompanies;
