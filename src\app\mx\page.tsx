import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import {
  CarPanels,
  Steps,
  WhyUs,
  ContactUs,
  TestimonialCarousel,
  Companies,
} from '@/components/HomeComponents/index';
import HeaderV2 from '@/components/HomeComponents/HeaderV2';
import { headers } from 'next/headers';

export default async function Home() {
  const headerList = headers();

  const country = (headerList.get('x-vercel-ip-country')?.toString() || 'MX') as 'MX' | 'US' | 'BR';

  return (
    <>
      <Navbar country={country} />
      {/* Container header del contenido home */}
      <main>
        <HeaderV2 country={country} />

        {/* Container main del contenido home */}
        <Steps />
        <CarPanels />
        {/* Por qué elegirnos? */}
        <WhyUs />
        {/* Qué dicen de nosotros? */}
        <Companies />
        {/* Carousel de opiniones */}
        <TestimonialCarousel />
        {/* Estrena un auto nuevo */}
        <ContactUs />
      </main>
      <Footer footerBgColor="footer.main" />
    </>
  );
}
