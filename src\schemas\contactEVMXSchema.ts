import * as Yup from 'yup';

export const contactEVMXSchema = Yup.object().shape({
  fullName: Yup.string()
    .min(3, 'Minimo 3 caracteres')
    .max(45, 'Demasiados caracteres!')
    .trim()
    .required('Nombres requeridos'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Email invalido'
    )
    .required('Email requerido'),
  phone: Yup.string()
    .min(10, 'Deben ser 10 numeros')
    .max(10, 'Deben ser 10 numeros')
    .required('Telefono requerido'),
  model: Yup.object().shape({
    value: Yup.string().required('Selecciona una opción'),
  }),
});
