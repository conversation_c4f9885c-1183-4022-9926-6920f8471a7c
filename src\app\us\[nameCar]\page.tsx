import PageNotFound from '@/app/not-found';
/* eslint-disable @typescript-eslint/no-use-before-define */
import getVehicleData from './data/data';
import Description from './detailComponents/Description';
import HowQualify from './detailComponents/HowQualify';
import MainFeatures from './detailComponents/MainFeatures';
import Benefits from './detailComponents/Benefits';
import Images from './detailComponents/images';
import Payments from './detailComponents/payment';
import Calculator from './detailComponents/Calculator';
import { headers } from 'next/headers';

interface USVehicleDetailProps {
  params: {
    nameCar: string;
  };
}

export function generateMetadata({ params: { nameCar } }: USVehicleDetailProps) {
  const data = getVehicleData(nameCar);

  if (!data)
    return {
      title: 'Not found',
    };

  const baseUrl = process.env.PROJECT_BASE_URL;
  const headersList = headers();

  let currentUrlWithPath = '';
  let openGraphImg = '';
  const host = headersList.get('host');
  const protocol = headersList.get('x-forwarded-proto');
  const url = headersList.get('next-url');
  const existsUrl = url ? url : '';

  if (baseUrl) {
    currentUrlWithPath = baseUrl + '/us/' + data.url;
    openGraphImg = baseUrl + data.images[0].src;
  } else {
    currentUrlWithPath = (protocol as string) + '://' + (host as string) + existsUrl;
    openGraphImg = (protocol as string) + '://' + (host as string) + data.images[0].src;
  }

  // const currentUrlWithPath2 = (protocol as string) + '://' + (host as string) + existsUrl;
  return {
    title: 'OCN | ' + data.name,
    description: data.description,
    openGraph: {
      images: [openGraphImg],
    },
    url: currentUrlWithPath,
  };
}

export default function USVehicleDetail({ params: { nameCar } }: USVehicleDetailProps) {
  const data = getVehicleData(nameCar);

  if (!data) return <PageNotFound />;

  const vehicle = {
    ...data,
    name: data.name,
    carYear: '2025',
    cardImage: data.cardImage,
    price: data.carPrice,
    features: {
      ...data.features,
    },
  };

  return (
    <>
      <div className="bg-[#FAFAFF]">
        <Images images={data.images} />
        <div className="w-full grid grid-cols-1 xl:grid-cols-2 px-6 md:px-10 xl:px-[75px] pt-[20px] md:pt-[35px] 2xl:pt-[40px] pb-[50px] font-[Plus-Jakarta-Sans] gap-[40px] xl:gap-[25px] 2xl:gap-0 ">
          <div id="left" className="w-full grid gap-[40px] text-[#464E5F] ">
            <Description carName={data.name} description={data.description} features={data.features} />
            <Payments isMobile payments={data.payments} payment={data.payment} vehicle={vehicle} />
            <MainFeatures mainFeatures={data.mainFeatures} />
            <Benefits />
          </div>
          <div id="right" className="flex flex-col gap-[30px] xl:mt-[10px] ">
            <Payments payments={data.payments} payment={data.payment} vehicle={vehicle} />
            <HowQualify />
          </div>
        </div>
        <Calculator carName={data.name} imageCar={data.imageCar} />
      </div>
    </>
  );
}
