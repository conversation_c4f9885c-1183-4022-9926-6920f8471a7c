'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  // <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  useDisclosure,
  Button,
} from '@chakra-ui/react';
import ReserveNowBtn from '../ReserveNowBtn';
import { Formik, FormikValues } from 'formik';
import { contactUSSchema } from '@/schemas/contactUSSchema';
import CustomInputUS from '../Inputs/CustomInputUS';
import SelectInputUS from '../Inputs/SelectInputUS';
import { useMemo } from 'react';
import Swal from 'sweetalert2';
import {
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  US_DEFAULT_STATE_OPTIONS,
  US_STATES_DEFAULT_CITIES,
  USSTATES,
  vehiclesFormOptions,
} from '../../[nameCar]/data/data';
import { sendToHubspotUS } from '../../actions/submit-form';
// import getVehicleData from '../../[nameCar]/data/data';

interface ContactModalProps {
  price: number;
  vehicleName: string;
  comingSoon?: boolean;
}

export default function ContactModal({ price, vehicleName, comingSoon }: ContactModalProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  // console.log('vehicleName', vehicleName, vehiclesFormOptions[0].label);
  const options = useMemo(() => {
    const first = vehiclesFormOptions.find((el) => el.label === vehicleName);
    const others = vehiclesFormOptions.filter((el) => el.label !== vehicleName);
    // console.log(first, others);
    return [first, ...others] as typeof vehiclesFormOptions;
  }, [vehicleName]);
  // console.log(options);
  // console.log([first, ...others]);
  // const currentVehicle = getVehicleData(params.nameCar as string);
  // const onSubmit = (values: any) => {
  //   console.log(values);
  // };
  async function onSubmit(form: FormikValues) {
    try {
      await sendToHubspotUS(form);

      onClose();
      return await Swal.fire({
        title: 'Information sent',
        text: 'We will contact you as soon as possible',
        icon: 'success',
        confirmButtonText: 'Close',
      });
      // setSending(false);
      // resetForm();
    } catch (e: any) {
      // setSending(false);
      // resetForm();
      onClose();
      if (e.response.data.message.includes('Ya has enviado tu solicitud'))
        return Swal.fire({
          title: 'We will contact you soon 😁',
          // text: e.response.data.message,
          text: 'You have already sent your request, please wait for us to contact you',
          icon: 'info',
          confirmButtonText: 'Close',
        });
      return await Swal.fire({
        title: 'Something went wrong',
        text: 'Try again, if the problem persists please wait for us to fix it',
        icon: 'error',
        confirmButtonText: 'Close',
      });
    }
    // return response;
  }
  return (
    <>
      {/* <Button onClick={onOpen}>Open Modal</Button> */}
      <ReserveNowBtn
        /* text="Register Interest" */ text={comingSoon ? 'Register Interest' : 'Reserve Now'}
        full
        onClick={() => onOpen()}
      />

      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader
            pt="30px"
            px={{ base: '24px', sm: '30px' }}
            className=" text-[22px] sm:text-[28px] font-bold font-[Plus-Jakarta-Sans]"
          >
            Your personalized plan is
          </ModalHeader>
          <ModalCloseButton className="top-[30px] sm:right-[30px] " />
          <ModalBody pb="40px" className=" font-[Plus-Jakarta-Sans] ">
            <p className=" text-purple-strong font-bold sm:text-[20px] ">
              ${price} /week<span className="text-[#3F404C] text-[14px] sm:text-[16px] "> Based</span> on 48
              months
            </p>
            <Formik
              initialValues={{
                fullName: '',
                phone: '',
                email: '',
                electric_car_usa: options[0].value,
                state: US_DEFAULT_STATE_OPTIONS[0].value,
                city: US_STATES_DEFAULT_CITIES[USSTATES.Florida][0].value,
                postalCode: '',
              }}
              onSubmit={onSubmit}
              validationSchema={contactUSSchema}
            >
              {({ errors, touched, handleSubmit, isSubmitting, values, setFieldValue }) => (
                <form
                  onSubmit={handleSubmit}
                  className="flex flex-col gap-[30px] sm:gap-[50px] xl:gap-[30px] xl:justify-between mt-[30px] font-[600] "
                >
                  <div className="flex flex-col sm:flex-row gap-[30px] w-full">
                    <CustomInputUS name="fullName" label="Full Name" errors={errors} touched={touched} />

                    <CustomInputUS
                      name="phone"
                      label="Cell phone"
                      errors={errors}
                      touched={touched}
                      type="number"
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row gap-[30px] w-full">
                    <CustomInputUS name="email" label="Email" errors={errors} touched={touched} />
                    <SelectInputUS
                      name="electric_car_usa"
                      label="Choose Your EV"
                      errors={errors}
                      touched={touched}
                      options={options}
                      onChange={(e) => {
                        setFieldValue('electric_car_usa', e.target.value);
                      }}
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row gap-[30px]">
                    <SelectInputUS
                      name="state"
                      label="Choose Your State"
                      errors={errors}
                      touched={touched}
                      options={getUSStatesOptions()}
                      onChange={(e) => {
                        const selectedState = e.target.value;
                        const city = getUSCitiesBasedOnState(selectedState)[0].value;
                        setFieldValue('state', selectedState);
                        setFieldValue('city', city);
                      }}
                    />
                    <SelectInputUS
                      name="city"
                      label="Choose Your City"
                      errors={errors}
                      touched={touched}
                      options={values.state ? getUSCitiesBasedOnState(values.state) : []}
                      onChange={(e) => {
                        setFieldValue('city', e.target.value);
                      }}
                    />
                  </div>
                  <div>
                    <CustomInputUS
                      name="postalCode"
                      label="Postal Code"
                      errors={errors}
                      touched={touched}
                      type="text"
                    />
                  </div>
                  <div className="flex justify-end sm:mr-[28px] gap-4">
                    <Button className="bg-[#E7E7E7] !h-[50px] px-[30px] rounded-[8px]" onClick={onClose}>
                      Cancel
                    </Button>
                    <ReserveNowBtn
                      text="Submit"
                      type="submit"
                      isDefault={false}
                      isSubmitting={isSubmitting}
                      className="w-[max-content] px-10"
                    />
                  </div>
                </form>
              )}
            </Formik>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}

// import {
//   Modal,
//   ModalOverlay,
//   ModalContent,
//   ModalHeader,
//   ModalFooter,
//   ModalBody,
//   ModalCloseButton,
//   useDisclosure,
//   Button,
// } from '@chakra-ui/react';
// import ReserveNowBtn from '../ReserveNowBtn';
// import { Formik } from 'formik';
// import { contactUSSchema } from '@/schemas/contactUSSchema';
// import CustomInputUS from '../Inputs/CustomInputUS';
// import SelectInputUS from '../Inputs/SelectInputUS';

// export default function ContactModal() {
//   const { isOpen, onOpen, onClose } = useDisclosure();
//   const onSubmit = (values: any) => {
//     console.log(values);
//   };
//   return (
//     <>
//       {/* <Button onClick={onOpen}>Open Modal</Button> */}
//       <ReserveNowBtn text="Send offer" full onClick={() => onOpen()} />

//       <Modal isOpen={isOpen} onClose={onClose} size="2xl">
//         <ModalOverlay />
//         <ModalContent>
//           <ModalHeader>Your personalized plan is</ModalHeader>
//           <ModalCloseButton />
//           <ModalBody className="w-full">
//             <p className=" text-purple-strong ">
//               $600 /month<span className="text-[#3F404C]"> Based</span> on 48 months
//             </p>
//             <Formik
//               initialValues={{ fullName: '', phone: '', email: '', vehicle: 'Tesla Model Y' }}
//               onSubmit={onSubmit}
//               validationSchema={contactUSSchema}
//             >
//               {({ errors, touched, handleSubmit, isSubmitting }) => (
//                 <form
//                   onSubmit={handleSubmit}
//                   className="flex flex-col sm:grid gap-[20px] sm:gap-[50px] xl:gap-[30px] xl:justify-between mt-[30px] font-[600] "
//                 >
//                   <div className="flex flex-col sm:flex-row gap-[50px]">
//                     <CustomInputUS name="fullName" label="Full Name" errors={errors} touched={touched} />

//                     <CustomInputUS name="phone" label="Cell phone" errors={errors} touched={touched} />

//                     {/* <div className="hidden sm:block xl:w-[270px] mt-2 ">
//                       <ReserveNowBtn full text="Submit" type="submit" />
//                     </div> */}
//                   </div>
//                   <div className="flex flex-col sm:flex-row gap-[50px]">
//                     <CustomInputUS name="email" label="Email" errors={errors} touched={touched} />
//                     <SelectInputUS name="vehicle" label="Choose Your EV" errors={errors} touched={touched} />
//                   </div>
//                   <div className="flex justify-end">
//                     <ReserveNowBtn
//                       text="Submit"
//                       type="submit"
//                       className="w-[max-content] px-5 sm:px-0 sm:w-[200px] "
//                     />
//                   </div>
//                   {/* <div className="sm:hidden md:w-[270px] justify-self-end self-end ">
//                     <ReserveNowBtn full text="Submit" type="submit" isSubmitting={isSubmitting} />
//                   </div> */}
//                 </form>
//               )}
//             </Formik>
//           </ModalBody>

//           <ModalFooter>
//             <Button colorScheme="blue" mr={3} onClick={onClose}>
//               Close
//             </Button>
//             <Button variant="ghost">Secondary Action</Button>
//           </ModalFooter>
//         </ModalContent>
//       </Modal>
//     </>
//   );
// }
