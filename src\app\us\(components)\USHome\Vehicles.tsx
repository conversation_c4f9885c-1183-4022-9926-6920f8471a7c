import VehicleCard from '../VehicleCard';
import { vehicleCards } from '../../[nameCar]/data/data';

export default function Vehicles() {
  // console.log('vehicleCards', vehicleCards);
  return (
    <section
      id="vehicles"
      className="w-full h-[max-content] min-h-[812px] py-[150px] px-6 lg:px-8 bg-[#F5F5FF]"
    >
      <div className="flex flex-col w-full justify-center gap-[50px]">
        <div className="flex flex-col items-center text-[28px] lg:text-[48px] font-[Plus-Jakarta-Sans] ">
          <p className="text-[#742BFA] font-bold">Your Next EV</p>
          <p className="text-[16px] lg:text-[22px] text-description">The Best Selection in the Market</p>
        </div>
        <div className="flex justify-center">
          <div
            className={`
                w-[100%]
                ${vehicleCards.length > 2 ? 'sm:w-[100%]' : 'sm:w-[90%]'}
                ${vehicleCards.length > 2 ? 'md:w-[100%]' : 'md:w-[70%]'}
                ${vehicleCards.length > 2 ? '3xl:w-[80%]' : '3xl:w-[55%]'}
                flex
                flex-wrap
                gap-[30px] xl:gap-0
                xl:row-gap-[40px]
                justify-evenly
              `}
          >
            {vehicleCards.map((v, i) => (
              <VehicleCard key={i} {...v} /* title={v.name} image={v.cardImage} price={v.payment} */ />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
