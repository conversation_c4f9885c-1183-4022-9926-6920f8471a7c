import { useState } from 'react';
import Slider from 'react-slick';
import { Box, Flex, Icon, Text, SimpleGrid, ListItem, UnorderedList } from '@chakra-ui/react';
import { FiArrowLeft, FiArrowRight, FiBatteryCharging } from 'react-icons/fi';
import { AiOutlineCloseCircle } from 'react-icons/ai';
import { TbEngine } from 'react-icons/tb';
import { BsSpeedometer2 } from 'react-icons/bs';
import { RiArrowLeftRightFill } from 'react-icons/ri';
import { SiCircle } from 'react-icons/si';
import { ModalInfo } from './vehicleData';
import Image from 'next/image';

const settings = {
  dots: false,
  arrows: false,
  fade: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
};

interface CarouselElectricVehiclesProps {
  shortName: string;
  toggleModal: () => void;
  modalInfo: ModalInfo;
}

export default function CarouselElectricVehicles({ toggleModal, modalInfo }: CarouselElectricVehiclesProps) {
  const [slider, setSlider] = useState<Slider | null>(null);
  const [showMsg, setShowMsg] = useState(true);
  // console.log(slider)
  const bold = modalInfo.description?.split(':')[0];
  const description = modalInfo.description?.split(':')[1];

  const handleSliderChange = (currentSlide: number) => {
    if (currentSlide !== 0) {
      setShowMsg(false);
    } else setShowMsg(true);
  };
  return (
    <>
      <Flex
        position="fixed"
        left={0}
        top={0}
        zIndex={990}
        w="100%"
        h="100vh"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        backgroundImage="linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7))"
        onClick={(event) => {
          if (event.target === event.currentTarget) {
            // Cerrar el modal solo si se hace clic en el fondo blanco
            toggleModal();
          }
        }}
      >
        <Box
          w={{ base: '85%', md: '70%' }}
          bg="transparent"
          position="relative"
          sx={{
            '.slick-slider, .slick-initialized, .slick-track, .slick-slide': {
              alignSelf: 'center !important',
              display: 'flex !important',
              justifyContent: 'center !important',
              alignItems: 'center !important',
            },
          }}
        >
          <link
            rel="stylesheet"
            type="text/css"
            charSet="UTF-8"
            href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
          />
          <link
            rel="stylesheet"
            type="text/css"
            href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
          />
          <Flex
            as="button"
            w={{ base: '30px', md: '50px' }}
            h={{ base: '30px', md: '50px' }}
            justifyContent="center"
            alignItems="center"
            borderRadius="50%"
            border="3px solid white"
            zIndex={10}
            p={0}
            bgColor="transparent"
            position="absolute"
            left={{ base: '37%', md: '-70px' }}
            top={{ base: '125%', md: '44%' }}
            color="white"
            onClick={() => slider?.slickPrev()}
            _hover={{ bgColor: 'transparent' }}
          >
            <Icon as={FiArrowLeft} w={{ base: '20px', md: '30px' }} h={{ base: '20px', md: '30px' }} />
          </Flex>
          <Flex
            as="button"
            w={{ base: '30px', md: '50px' }}
            h={{ base: '30px', md: '50px' }}
            justifyContent="center"
            alignItems="center"
            borderRadius="50%"
            border="3px solid white"
            zIndex={10}
            p="0px"
            cursor="pointer"
            bgColor="transparent"
            position="absolute"
            right={{ base: '37%', md: '-70px' }}
            top={{ base: '125%', md: '44%' }}
            color="white"
            onClick={() => slider?.slickNext()}
            _hover={{ bgColor: 'transparent' }}
          >
            <FiArrowRight size={32} />
          </Flex>

          {showMsg && (
            <Flex
              display={{ base: 'flex', md: 'none' }}
              w="100%"
              justifyContent="center"
              position="absolute"
              color="white"
              bottom="-40px"
            >
              Puedes hacer scroll para más info
            </Flex>
          )}

          {/* SLIDER */}
          <Slider {...settings} ref={(slider2) => setSlider(slider2)} afterChange={handleSliderChange}>
            {/* VEHICLE DATA */}
            <Flex
              bg="white"
              w="80%"
              h={{ base: '330px', md: '100%' }}
              p={{ base: '15px', md: '30px' }}
              gap="100px"
              overflowY={{ base: 'scroll', md: 'visible' }}
              borderRadius="1rem"
              justifyContent="center"
              alignItems="center"
              flexDir="column"
              top={10}
              position="sticky"
            >
              <Box
                zIndex={100}
                position="absolute"
                right={{ base: '15px', md: '30px' }}
                top={{ base: '15px', md: '30px' }}
                color="black"
                onClick={toggleModal}
                cursor="pointer"
              >
                <Icon
                  as={AiOutlineCloseCircle}
                  w={{ base: '25px', md: '40px' }}
                  h={{ base: '25px', md: '40px' }}
                />
              </Box>
              <Text fontWeight={700} fontSize={{ base: '18px', md: '32px' }}>
                {modalInfo.fullName}
              </Text>
              <SimpleGrid
                columns={{ base: 2, md: 3 }}
                mt={{ base: '20px', md: '30px' }}
                rowGap="10px"
                fontWeight={700}
                fontSize={{ base: '12px', md: '16px' }}
              >
                <Flex gap={2} alignItems="center">
                  <Icon
                    as={RiArrowLeftRightFill}
                    w={{ base: '20px', md: '30px' }}
                    h={{ base: '20px', md: '30px' }}
                  />
                  <Box>
                    <Text>RANGO</Text>
                    <Text fontSize={{ base: '16px', md: '18px' }} fontWeight={400}>
                      {modalInfo.range} KM
                    </Text>
                  </Box>
                </Flex>
                <Flex gap={2} alignItems="center">
                  <Icon as={TbEngine} w={{ base: '20px', md: '30px' }} h={{ base: '20px', md: '30px' }} />
                  <Box>
                    <Text>MOTOR</Text>
                    <Text fontSize={{ base: '16px', md: '18px' }} fontWeight={400}>
                      {modalInfo.motor} HP
                    </Text>
                  </Box>
                </Flex>
                <Flex gap={2} alignItems="center">
                  <Icon
                    as={BsSpeedometer2}
                    w={{ base: '20px', md: '30px' }}
                    h={{ base: '20px', md: '30px' }}
                  />
                  <Box>
                    <Text>ACELERACIÓN</Text>
                    <Text fontSize={{ base: '16px', md: '18px' }} fontWeight={400}>
                      {modalInfo.acceleration} s
                    </Text>
                  </Box>
                </Flex>
                <Flex gap={2} alignItems="center">
                  <Icon
                    as={FiBatteryCharging}
                    w={{ base: '20px', md: '30px' }}
                    h={{ base: '20px', md: '30px' }}
                  />
                  <Box>
                    <Text>CARGA RÁPIDA</Text>
                    <Text fontSize={{ base: '16px', md: '18px' }} fontWeight={400}>
                      {modalInfo.fastCharge} H
                    </Text>
                  </Box>
                </Flex>
                <Flex gap={2} alignItems="center">
                  <Icon as={SiCircle} w={{ base: '20px', md: '30px' }} h={{ base: '20px', md: '30px' }} />
                  <Box>
                    <Text>BOLSAS DE AIRE</Text>
                    <Text fontSize={{ base: '16px', md: '18px' }} fontWeight={400}>
                      {modalInfo.airBags}
                    </Text>
                  </Box>
                </Flex>
              </SimpleGrid>
              <Box fontSize={{ base: '14px', md: '18px' }} my={{ base: '15px', md: '50px' }}>
                <Text>
                  <b>{bold}:</b> {description}{' '}
                </Text>
              </Box>
              <Flex flexDir="column">
                <Text fontSize={{ base: '16px', md: '22px' }} fontWeight={700}>
                  Características destacadas
                </Text>
                <UnorderedList
                  mt={{ base: '5px', md: '20px' }}
                  display="flex"
                  flexDir="column"
                  gap={1}
                  fontSize={{ base: '14px', md: '18px' }}
                >
                  <SimpleGrid columns={{ base: 1, md: 2 }}>
                    {modalInfo.features.map((f, i) => (
                      <ListItem key={i} alignSelf="center">
                        {f}
                      </ListItem>
                    ))}
                  </SimpleGrid>
                </UnorderedList>
              </Flex>
            </Flex>

            {/* IMAGES */}

            {modalInfo?.images.map((img, i) => (
              <Flex key={i} position="sticky" top={10} justifyContent="center" alignItems="center">
                <Box
                  zIndex={100}
                  position="absolute"
                  right={{ base: '15px', md: '30px' }}
                  top={{ base: '15px', md: '30px' }}
                  color="black"
                  onClick={toggleModal}
                  cursor="pointer"
                  borderRadius="1rem"
                >
                  <Icon
                    as={AiOutlineCloseCircle}
                    w={{ base: '25px', md: '40px' }}
                    h={{ base: '25px', md: '40px' }}
                  />
                </Box>
                <Image
                  src={img}
                  className="w-[100%] h-[full] justify-self-center rounded-[15px] object-contain"
                  alt="img"
                />
              </Flex>
            ))}
          </Slider>
        </Box>
      </Flex>
    </>
  );
}
