import { create } from 'zustand';

type Option = {
  value: string;
  label: string;
};

interface UseSelectedElectricCarProps {
  option: Option;
  setOption: (num: Option) => void;
}

const useSelectedElectricCar = create<UseSelectedElectricCarProps>((set) => ({
  option: {
    value: 'NETA V',
    label: 'NETA V',
  },
  setOption: (option: Option) => set({ option }),
}));

export default useSelectedElectricCar;
