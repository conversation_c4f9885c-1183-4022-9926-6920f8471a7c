import TypingAnimation from '@/components/TypingAnimation';
import USHeaderImg from './USHeaderImg';
import ReserveNowBtn from '../ReserveNowBtn';

export default function USHeader() {
  return (
    <header className="w-full min-h-[calc(100vh-70px)] relative overflow-hidden flex flex-col justify-center px-6 md:px-8 py-[30px] lg:py-0 ">
      <div className="hidden l:block bg-[#B288FF] absolute w-[588px] xl:w-[688px] h-[588px] xl:h-[688px] rounded-[50%] top-[-354px] xl:top-[-474px] right-[-8%] 2xl:right-[-6%] " />
      <div
        className="hidden l:block w-[588px] xl:w-[688px] h-[588px] xl:h-[688px] absolute sm:top-[-8%] xl:top-[-6%] sm:right-[-354px] xl:right-[-474px] rounded-full "
        style={{ backgroundColor: 'rgba(116, 43, 250, 0.80)' }}
      />
      <div className="w-full flex flex-col l:flex-row gap-3 l:gap-0 lg:gap-3  ">
        <div className="flex flex-col w-full l:w-1/2 lg:w-[55%] h-[80%] l:h-[calc(100vh-70px)] justify-center ml-0 l:ml-[20px] lg:ml-[50px] gap-[30px] lg:gap-[50px] font-[Plus-Jakarta-Sans] ">
          <div>
            <p className="font-bold text-[32px] sm:text-[45px] l:text-[40px] xl:text-[60px] ">
              The easiest way to ...
            </p>
            <p className=" w-[100vw] text-[27px] sm:text-[45px] l:text-[40px] xl:text-[60px] text-purple-strong font-bold ">
              <TypingAnimation
                texts={['Earn more money on Uber', 'Get a New EV']}
                typeSpeed={60}
                deleteSpeed={90}
                delaySpeed={1500}
                loop
              />
            </p>
          </div>
          <p className="w-full sm:w-[80%] md:w-[55%] l:w-[80%] lg:w-[65%] text-description">
            The easiest car subscription with the all-inclusive service that allows you to improve your
            earnings on the ride-sharing app of your preference.
          </p>
          <div className="hidden l:block">
            <ReserveNowBtn />
          </div>
        </div>
        <div className="flex w-[100%] l:w-[45%] h-[150px] l:h-[calc(100vh-70px)] min-h-[200px] justify-center l:justify-end gap-3 my-[50px] lg:my-0 relative ">
          <USHeaderImg />
        </div>
      </div>
      <div className=" flex l:hidden justify-center h-full ">
        <ReserveNowBtn />
      </div>
    </header>
  );
}
