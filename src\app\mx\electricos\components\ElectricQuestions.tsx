'use client';
import { AccordionButton, AccordionItem, AccordionPanel, Box, Text } from '@chakra-ui/react';
import { MdKeyboardArrowUp, MdKeyboardArrowDown } from 'react-icons/md';

const ElectricQuestions = ({
  question,
  answer,
  toggleOpen,
}: {
  question: string;
  answer: string;
  toggleOpen: (index: any) => void;
}) => {
  return (
    <>
      <AccordionItem
        w="100%"
        border="none"
        fontFamily="Plus-Jakarta-Sans"
        color="text.main"
        mt="10px"
        mb="25px"
      >
        {({ isExpanded }) => (
          <>
            <AccordionButton
              w="100%"
              h="70px"
              minH="70px"
              _hover={{ bgColor: 'transparent', borderRadius: '30px' }}
              sx={{ '> *:not(:last-child)': { mr: { base: 2, md: 0 } } }}
              px={{ base: 'none', md: 5 }}
              onClick={toggleOpen}
            >
              <Box
                as="span"
                fontFamily="Plus-Jakarta-Sans"
                flex="1"
                textAlign="left"
                color="#5A00F8"
                fontSize={{ base: '16px', md: '24px' }}
                fontWeight={600}
              >
                {' '}
                {question}{' '}
              </Box>
              {/* Icon/ */}
              {isExpanded ? (
                <MdKeyboardArrowUp color="#5A00F8" size={32} />
              ) : (
                <MdKeyboardArrowDown color="#5A00F8" size={32} />
              )}
            </AccordionButton>

            <AccordionPanel pb={4} sx={{ '> *:not(:last-child)': { mb: '8px' } }}>
              <Text
                fontSize={{ md: '18x', base: '14px' }}
                color="#1A1A1A"
                textAlign="left"
                fontWeight="300px"
                fontFamily="Plus-Jakarta-Sans"
              >
                {' '}
                {answer}{' '}
              </Text>
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
      {/* <Divider w={'100%'} h="1px" bg={'#1A1A1A'} /> */}
      <div className="w-full h-[1px] bg-[#1A1A1A] my-[5px] " />
    </>
  );
};

export default ElectricQuestions;
