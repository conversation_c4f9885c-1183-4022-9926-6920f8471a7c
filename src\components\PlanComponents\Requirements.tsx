import { Flex, Heading, ListItem, UnorderedList } from '@chakra-ui/layout';

const personalRequirements = [
  'INE',
  'CURP',
  'Estado de cuenta',
  'Comprobante de domicilio',
  'Constancia de situación fiscal',
];

const platformRequirements = [
  'Experiencia como conductor de plataformas',
  'Movimientos actuales',
  'INE',
  'CURP',
  'Estado de cuenta',
];

const personalBgColor = 'linear-gradient(92.28deg, #00D2FF 2.67%, #028CF3 78.85%)';
const platformBgColor = 'linear-gradient(92.67deg, #A74DF9 30.58%, #6210FF 76.12%)';

export default function Requirments({ isPersonalPlan }: { isPersonalPlan: boolean }) {
  return (
    <Flex
      w="100%"
      h={{ base: 'max-content', md: '242px' }}
      bg={isPersonalPlan ? personalBgColor : platformBgColor}
      justifyContent="center"
      color="#FAFAFF"
      py="30px"
    >
      <Flex
        w="90%"
        h="100%"
        flexDir={{ base: 'column', md: 'row' }}
        py={{ base: '30px', md: '0px' }}
        alignItems="center"
        sx={{ '> *:not(:last-child)': { mb: 10 } }}
      >
        <Flex w={{ base: '100%', md: '50%' }} justifyContent="center">
          <Heading
            as="h2"
            w={{ base: '90%', lg: '60%' }}
            lineHeight="60px"
            fontSize="32px"
            fontFamily="Plus-Jakarta-Sans"
            fontWeight={800}
          >
            ¿Qué necesitas para estrenar auto nuevo?
          </Heading>
        </Flex>
        <Flex
          w={{ base: '100%', md: '50%' }}
          pl={{ base: '40px', md: 0 }}
          justifyContent={{ base: 'start', md: 'center' }}
        >
          <UnorderedList fontWeight={500} fontSize="18px" fontFamily="Plus-Jakarta-Sans">
            {isPersonalPlan
              ? personalRequirements.map((requirement, i) => {
                  return <ListItem key={i}>{requirement}</ListItem>;
                })
              : platformRequirements.map((requirement, i) => {
                  return <ListItem key={i}>{requirement}</ListItem>;
                })}
          </UnorderedList>
        </Flex>
      </Flex>
    </Flex>
  );
}
