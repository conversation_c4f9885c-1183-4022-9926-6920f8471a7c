import axios from 'axios';
import { API_URL } from '@/constants';
import { cache } from 'react';

async function getPersonal() {
  try {
    const platformVehicles = await axios(API_URL + '/vehicles/get/personal', {
      headers: {
        'vehicles-key': 'EAFSasg.354s',
      },
    });
    return platformVehicles.data.vehicles;
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function getPlatform() {
  try {
    const platformVehicles = await axios(API_URL + '/vehicles/get/platform', {
      headers: {
        'vehicles-key': 'EAFSasg.354s',
      },
    });
    return platformVehicles.data.vehicles;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export const getPlatformCache = cache(getPlatform);
export const getPersonalCache = cache(getPersonal);
