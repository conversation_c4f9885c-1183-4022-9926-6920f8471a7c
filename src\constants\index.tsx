export const COUNTRIES = ['MX', 'US'];

export const API_URL = process.env.API_URL as string;

export const HILOS_URL = process.env.HILOS_URL as string;

export const HILOS_URL_SEND_TEMPLATE = process.env.HILOS_URL_SEND_TEMPLATE as string;

export const HILOS_API_KEY = process.env.HILOS_API_KEY as string;

export const COUNTRY_DATA: Record<'MX' | 'US' | 'BR', any> = {
  MX: {
    navigator: {
      platform: 'Uso plataforma',
      personal: 'Uso personal',
      blog: 'Blog',
      contact: 'Contacto',
      access: 'Acceso a clientes',
    },
    home: {
      hero: {
        title: 'Reinventamos la forma de ',
        purple: 'estrenar un auto',
        subtitle: 'OCN te ofrece un auto nuevo a la medida, 100% online, rapido, seguro y con todo incluido.',
        text: '👍 Plataforma de renting de autos #1 en México',
        button: '¡Quiero estrenar!',
      },
      steps: {
        title: 'Estrena con OCN en 3 pasos',
        subtitle:
          'Adquiere un auto nuevo a largo plazo sin enganches ni depositos en garantia para continuar trabajando en plataforma de movilidad',
        step1: {
          title: '1. Selecciona tu auto',
          subtitle: 'Busca y selecciona el auto de tus sueños y personalizado',
        },
        step2: {
          title: '2. Envía tu formulario',
          subtitle: 'Llena el formulario y envía los documentos solicitados',
        },
        step3: {
          title: '3. Recibe',
          subtitle: 'En un tiempo aproximado de 90 minutos tendrás la respuesta a tu solicitud',
        },
      },
      cars: {
        title: 'Autos destacados',
        subtitle: '!Elige el que más te guste!',
        type: {
          platform: {
            title: 'Uso plataforma',
          },
          personal: {
            title: 'Uso personal',
          },
        },
      },
    },
    cars: {
      type: {
        platform: {
          title: 'Plan plataformas',
          options: [
            {
              tittle: 'MG5 Excite',
              information:
                'Equipado con un motor de rendimiento suave y poderoso, MG5 se convierte en el auto ideal para tus viajes diarios, brindándote una experiencia de conducción ágil y emocionante para ir a donde quieras.',
              price: '$ 3550',
            },
          ],
        },
        personal: {
          title: 'Plan personal',
        },
      },
      includes: [
        'Todo incluido',
        'Sin pago incial',
        'Beneficios por suscripción',
        '36 meses con opción a comprar',
      ],
    },
  },
  US: {
    navigator: {
      uber: 'Uber',
      ourVehicles: `Our EV's`,
      howItWorks: 'How it works',
      // contact: 'Contact',
      reserveNow: 'Reserve Now',
    },
    home: {
      hero: {
        title: 'We reinvent the way to ',
        purple: 'get a new car',
        subtitle: 'OCN offers you a new car tailored to you, 100% online, fast, safe and all inclusive.',
        text: '👍 Car renting platform #1',
        button: 'I want to get a new car!',
      },
    },
  },
  BR: {
    navigator: {
      uber: 'Uber',
      ourVehicles: `Our EV's`,
      howItWorks: 'How it works',
      // contact: 'Contact',
      reserveNow: 'Reserve Now',
    },
    home: {
      hero: {
        // title: 'We reinvent the way to ',
        title: 'Economia ',
        separation: true,
        purple: 'Gig Empoderada',
        subtitle:
          'Com a OCN, você tem um carro novo pra rodar. É 100% online, rápido, seguro e tudo incluso!',
        text: 'Aluguel #1 pra quem roda de app. Chegando no São Paulo!',
        button: 'Quero meu carro novo!',
      },
      steps: {
        title: 'Comece com a OCN em 3 passos simples.',
        subtitle:
          'Tenha um carro novo sem entrada ou caução para continuar rodando nas plataformas de mobilidade.',
        step1: {
          title: '1. Escolha seu carro',
          subtitle: 'Encontre e selecione o carro dos seus sonhos e personalize-o',
        },
        step2: {
          title: '2. Envie seu formulário',
          subtitle: 'Preencha o formulário e anexe os documentos solicitados.',
        },
        step3: {
          title: '3. Receba',
          subtitle: 'Em até 90 minutos, você terá a resposta da sua solicitação.',
        },
      },
    },
  },
};

export const urlOptions = ['https://onecarnow.com', 'https://www.onecarnow.com'];

export const HUBSPOT_URL = 'https://api.hubapi.com/crm/v3/objects/contacts';

export const HUBSPOT_TOKEN = process.env.HUBSPOT_TOKEN!;

export const cityLeadOptions = [
  { value: 'CDMX/EDOMEX', label: 'Ciudad de México / Estado México' },
  { value: 'Aguascalientes', label: 'Aguascalientes' },
  { value: 'Chihuahua', label: 'Chihuahua' },
  { value: 'Ciudad Juárez', label: 'Ciudad Juárez' },
  { value: 'Cuernavaca', label: 'Cuernavaca' },
  { value: 'Durango', label: 'Durango' },
  { value: 'Guadalajara', label: 'Guadalajara' },
  { value: 'Hermosillo', label: 'Hermosillo' },
  { value: 'León', label: 'León' },
  { value: 'Mérida', label: 'Mérida' },
  { value: 'Mexicali', label: 'Mexicali' },
  { value: 'Monterrey', label: 'Monterrey' },
  { value: 'Morelia', label: 'Morelia' },
  { value: 'Puebla', label: 'Puebla' },
  { value: 'Puerto Vallarta', label: 'Puerto Vallarta' },
  { value: 'Querétaro', label: 'Querétaro' },
  { value: 'Saltillo', label: 'Saltillo' },
  { value: 'San Luis Potosí', label: 'San Luis Potosí' },
  { value: 'Tepic', label: 'Tepic' },
  { value: 'Tijuana', label: 'Tijuana' },
  { value: 'Toluca', label: 'Toluca' },
  { value: 'Torreón', label: 'Torreón' },
  { value: 'Otro', label: 'Otro' },
];

export const agenciesOptions: { [key: string]: { label: string; value: string }[] } = {
  Aguascalientes: [{ label: 'MG Aguascalientes', value: 'MG Aguascalientes' }],
  'CDMX/EDOMEX': [
    { label: 'MG Azcapotzalco', value: 'MG Azcapotzalco' },
    { label: 'MG Coacalco', value: 'MG Coacalco' },
    { label: 'MG Coyoacán', value: 'MG Coyoacán' },
    { label: 'MG Miramontes', value: 'MG Miramontes' },
    { label: 'MG Pedregal', value: 'MG Pedregal' },
    { label: 'MG Santa Fe', value: 'MG Santa Fe' },
    { label: 'MG Del Valle', value: 'MG Del Valle' },
    { label: 'MG Cuautitlán', value: 'MG Cuautitlán' },
    { label: 'MG Ecatepec', value: 'MG Ecatepec' },
    { label: 'MG Interlomas', value: 'MG Interlomas' },
    { label: 'MG Ixtapaluca', value: 'MG Ixtapaluca' },
    { label: 'MG La Villa', value: 'MG La Villa' },
    { label: 'MG Iztapalapa', value: 'MG Iztapalapa' },
    { label: 'MG Polanco', value: 'MG Polanco' },
    { label: 'MG Lomas Verdes', value: 'MG Lomas Verdes' },
    { label: 'MG Texcoco', value: 'MG Texcoco' },
    { label: 'MG Tláhuac', value: 'MG Tláhuac' },
    { label: 'MG Tlalnepantla', value: 'MG Tlalnepantla' },
    { label: 'MG Aeropuerto', value: 'MG Aeropuerto' },
  ],
  Celaya: [{ label: 'MG Celaya', value: 'MG Celaya' }],
  Chihuahua: [{ label: 'MG Chihuahua', value: 'MG Chihuahua' }],
  'Ciudad Juárez': [{ label: 'MG Ciudad Juárez', value: 'MG Ciudad Juárez' }],
  Cuernavaca: [{ label: 'MG Cuernavaca', value: 'MG Cuernavaca' }],
  Durango: [{ label: 'MG Durango', value: 'MG Durango' }],
  Guadalajara: [
    { label: 'MG Country', value: 'MG Country' },
    { label: 'MG Santa Anita', value: 'MG Santa Anita' },
    { label: 'MG Galerías', value: 'MG Galerías' },
    { label: 'MG Lopez Mateos', value: 'MG Lopez Mateos' },
    { label: 'MG Tonala', value: 'MG Tonala' },
  ],
  Hermosillo: [{ label: 'MG Hermosillo', value: 'MG Hermosillo' }],
  Irapuato: [{ label: 'MG Irapuato', value: 'MG Irapuato' }],
  León: [
    { label: 'MG Campestre', value: 'MG Campestre' },
    { label: 'MG Poliforum', value: 'MG Poliforum' },
  ],
  Merida: [
    { label: 'MG Montecristo', value: 'MG Montecristo' },
    { label: 'MG Península', value: 'MG Península' },
  ],
  Mexicali: [{ label: 'MG Mexicali', value: 'MG Mexicali' }],
  Monterrey: [
    { label: 'MG Linda Vista', value: 'MG Linda Vista' },
    { label: 'MG Cumbres', value: 'MG Cumbres' },
    { label: 'MG Fleteros', value: 'MG Fleteros' },
    { label: 'MG Las Torres', value: 'MG Las Torres' },
    { label: 'MG Sendero', value: 'MG Sendero' },
    { label: 'MG Calzada del Valle', value: 'MG Calzada del Valle' },
  ],
  Puebla: [
    { label: 'MG Angelópolis', value: 'MG Angelópolis' },
    { label: 'MG Dorada', value: 'MG Dorada' },
    { label: 'MG Los Fuertes', value: 'MG Los Fuertes' },
  ],
  'Puerto Vallarta': [{ label: 'MG Puerto Vallarta', value: 'MG Puerto Vallarta' }],
  Querétaro: [
    { label: 'MG Corregidora', value: 'MG Corregidora' },
    { label: 'MG Juriquilla', value: 'MG Juriquilla' },
  ],
  Saltillo: [{ label: 'MG Saltillo', value: 'MG Saltillo' }],
  'San Luis Potosí': [
    { label: 'MG Carretera 57', value: 'MG Carretera 57' },
    { label: 'MG Lomas', value: 'MG Lomas' },
  ],
  Tepic: [{ label: 'MG Tepic', value: 'MG Tepic' }],
  Tijuana: [
    { label: 'MG Matriz', value: 'MG Matriz' },
    { label: 'MG Salinas', value: 'MG Salinas' },
  ],
  Toluca: [
    { label: 'MG Metepec', value: 'MG Metepec' },
    { label: 'MG Toluca', value: 'MG Toluca' },
  ],
  Torreón: [{ label: 'MG Laguna', value: 'MG Laguna' }],
};

export const REQUEST_URL = process.env.NEXT_PUBLIC_REQUEST_URL as string;
