import { Box, Flex, Grid, Heading, Text } from '@chakra-ui/react';
import CustomButton from '@/components/CustomButton';
import Link from 'next/link';

export default function ContactUs({ isPersonalPlan }: { isPersonalPlan: boolean }) {
  return (
    <Flex
      as="section"
      w="100%"
      h={{ base: 'max-content', md: '700px' }}
      justifyContent="center"
      overflow="hidden"
      bgColor="bgColor"
      py="80px"
    >
      <Flex
        w="90%"
        h="100%"
        pb="50px"
        justifyContent="center"
        flexDir={{ base: 'column-reverse', md: 'row' }}
        sx={{ '> *:not(:last-child)': { mr: 5, mt: { base: 5, md: 0 } } }}
      >
        <Flex
          w={{ base: '100%', md: '50%' }}
          h="100%"
          justifyContent={{ base: 'start', md: 'center' }}
          alignItems="center"
        >
          <Box w={{ base: '320px', md: '405px' }} h="520px" position="relative">
            <Box
              w={{ base: '115px', md: '165px' }}
              h={{ base: '163px', md: '203px' }}
              // dots
              backgroundImage={
                isPersonalPlan
                  ? 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fpersonal%2Fdots.png?alt=media&token=529b7551-c720-40d5-9ce7-6cc707c16408'
                  : 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fplatform%2FdotsH.png?alt=media&token=fc6714d8-b8d5-43dd-9bc5-509e1141eb50'
              }
              backgroundRepeat="no-repeat"
              backgroundSize="contain"
              backgroundPosition="center"
              position="absolute"
              left={{ base: '-15px', md: '-25px' }}
              top={{ base: '25px', md: '-25px' }}
              zIndex={2}
            />
            <Box
              w="100%"
              h="100%"
              backgroundImage="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fcar2.png?alt=media&token=ae9db2cc-9555-4268-a15e-d001a96ae9e5"
              backgroundRepeat="no-repeat"
              backgroundSize="contain"
              backgroundPosition="center"
              position="sticky"
              zIndex={1}
            />
            <Box
              w={{ base: '100px', md: '177px' }}
              h={{ base: '100px', md: '177px' }}
              bgColor={isPersonalPlan ? 'plan.personal.color' : 'purple.strong'}
              borderRadius="50%"
              position="absolute"
              zIndex={0}
              right={{ base: '-40px', md: '-75px' }}
              bottom={{ base: '15px', md: '-75px' }}
            />
          </Box>
        </Flex>
        <Flex
          w={{ base: '100%', md: '50%' }}
          h="100%"
          alignItems="center"
          justifyContent={{ base: 'start', md: 'end', lg: 'center' }}
        >
          <Grid w={{ base: '90%', md: '70%', lg: '60%' }} sx={{ '> *:not(:last-child)': { mb: 5 } }}>
            <Heading
              color={isPersonalPlan ? 'plan.personal.color' : 'purple.strong'}
              fontSize="32px"
              fontFamily="Plus-Jakarta-Sans"
              fontWeight={700}
            >
              ¿Aún con dudas?
            </Heading>
            <Text lineHeight="30px" fontSize="15px" fontFamily="Plus-Jakarta-Sans" fontWeight={500}>
              Te contamos todo sobre nuestro Plan
              {isPersonalPlan ? ' Personal' : ' Plataformas'}, déjanos tus datos y nos pondremos en contacto
              contigo.
            </Text>
            <Link href="/mx/contacto">
              <CustomButton message="Contáctanos" className={isPersonalPlan ? 'btn-blue' : 'btn-green'} />
            </Link>
          </Grid>
        </Flex>
      </Flex>
    </Flex>
  );
}
