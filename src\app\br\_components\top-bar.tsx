'use client';

import Dollar from '@/assets/br/dollar.png';
import LogoSvg from '@/assets/br/top flag.svg';
import WhiteCarSvg from '@/assets/br/white car.png';
import BlackCarSvg from '@/assets/br/black car.png';
import RocketIcon from '@/assets/br/rocket icon.png';
import Image from 'next/image';
import { Marquee } from '@/components/magicui/marquee';

export function TopBar() {
  return (
    <>
      <div className="bg-[#E3D7FF]">
        <Marquee pauseOnHover className="[--duration:20s]">
          <div className="flex items-center space-x-12">
            <span className="text-sm md:text-base pl-10">Grandes novidades para o Brazil!</span>
            <span className="flex-shrink-0">
              <Image
                src={LogoSvg}
                alt="top flag"
                width={33}
                height={40}
                className="min-w-[20px] min-h-[24px] object-contain"
              />
            </span>
            <span className="text-sm md:text-base">Veículos 100% elétricos</span>
            <span className="flex-shrink-0">
              <Image
                src={WhiteCarSvg}
                alt="white car"
                width={33}
                height={40}
                className="min-w-[20px] min-h-[24px] object-contain"
              />
            </span>
            <span className="text-sm md:text-base">Maiores ganhos para motoristas</span>
            <span className="flex-shrink-0">
              <Image
                src={Dollar}
                alt="dollar"
                width={20}
                height={20}
                className="min-w-[16px] min-h-[16px] object-contain"
              />
            </span>
            <span className="text-sm md:text-base">A OCN te oferece um carro novo</span>
            <span className="flex-shrink-0">
              <Image
                src={BlackCarSvg}
                alt="black car"
                width={33}
                height={40}
                className="min-w-[20px] min-h-[24px] object-contain"
              />
            </span>
            <span className="text-sm md:text-base">Ecológico e econômico</span>
            <span className="flex-shrink-0">
              <Image
                src={RocketIcon}
                alt="rocket"
                width={18}
                height={20}
                className="min-w-[14px] min-h-[16px] object-contain"
              />
            </span>
          </div>
        </Marquee>
      </div>
    </>
  );
}
