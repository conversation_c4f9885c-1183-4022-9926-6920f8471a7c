import { FormControl, FormErrorMessage, FormLabel, Input } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikErrors, FormikValues } from 'formik';
import { Dispatch, useRef } from 'react';

interface FieldProps {
  errors: FormikErrors<{
    name: string;
    email: string;
    city: string;
    file: string;
    [key: string]: string | undefined;
  }>;
  touched: any;
  name: string;
  label: string;
  type: 'file';
  placeholder: string;
  nameFile: string;
  setNameFile: Dispatch<React.SetStateAction<string>>;
}

export default function FileInput({
  errors,
  touched,
  name,
  label,
  type,
  placeholder,
  nameFile,
  setNameFile,
}: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <Field name={name}>
      {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl
          position="relative"
          isInvalid={Boolean(errors ? errors[name] : '') && touched && touched[name]}
        >
          <FormLabel>{label}</FormLabel>
          <Input
            h="45px"
            placeholder={nameFile || placeholder}
            userSelect="none"
            color="purple.400"
            borderColor="purple.400"
            border="2px solid"
            sx={{
              '&::placeholder': {
                color: 'purple.400',
              },
              _hover: {
                borderColor: 'purple.400',
              },
            }}
            cursor="pointer"
            readOnly
            onClick={handleButtonClick}
          />

          <Input
            h="45px"
            display="none"
            name={name}
            ref={fileInputRef}
            cursor="pointer"
            accept="application/pdf"
            type={type}
            onChange={(event) => {
              const file = event.currentTarget.files ? event.currentTarget.files[0] : undefined;
              if (!file) return;
              form.setFieldValue(name, file);
              setNameFile(file.name);
              // if (file.size < 1048576) {
              //   console.log(file, file.size < 1048576)
              // }
              // if (file.size > 1048576) {
              //   console.log(form)
              //   form.setFieldError(name, "XD")
              // }
            }}
            placeholder={placeholder}
          />

          <FormErrorMessage fontSize="13px" fontWeight={600} letterSpacing=".03rem">
            {errors[name]}
          </FormErrorMessage>
        </FormControl>
      )}
    </Field>
  );
}
