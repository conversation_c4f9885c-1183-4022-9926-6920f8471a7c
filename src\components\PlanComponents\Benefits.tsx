import { Box, Flex, Heading, List, ListIcon, ListItem, Text } from '@chakra-ui/react';
import { BiCheckCircle } from 'react-icons/bi';

const personalBenefits = [
  'Auto nuevo cada 4 años',
  'Pagos mensuales y quincenales',
  'Mantenimientos',
  'Seguro de auto',
  'Opción a compra',
];
const platformBenefits = [
  'Auto nuevo cada 3 años',
  'Pagos semanales',
  'Mantenimientos',
  'Seguro de auto',
  'Opción a compra',
];

export default function Benefits({ isPersonalPlan }: { isPersonalPlan: boolean }) {
  return (
    <Flex
      w="100%"
      h={{ base: 'max-content', md: '800px' }}
      py="50px"
      justifyContent="center"
      overflow="hidden"
      bgColor="bgColor"
    >
      <Flex
        w="90%"
        justifyContent="center"
        flexDir={{ base: 'column', md: 'row' }}
        sx={{ '> *:not(:last-child)': { mb: 5 } }}
        color="text.main"
      >
        <Flex
          w={{ base: '100%', md: '50%' }}
          h="100%"
          flexDir="column"
          justifyContent="center"
          sx={{ '> *:not(:last-child)': { mb: 10 } }}
        >
          <Heading
            as="h3"
            color={isPersonalPlan ? '#028CF3' : 'purple.strong'}
            lineHeight="60px"
            fontSize="40px"
            fontFamily="Plus-Jakarta-Sans"
            fontWeight={700}
          >
            {isPersonalPlan ? <>Beneficios Plan Personal</> : <>Beneficios Plan Plataformas</>}
          </Heading>
          <Text
            maxW="470px"
            lineHeight="35px"
            fontSize="18px"
            fontFamily="Plus-Jakarta-Sans"
            fontWeight={400}
          >
            Elije el auto qué más te guste y nosotros nos encargamos de todo el proceso
          </Text>
          <List spacing={5} ml="20px">
            {isPersonalPlan
              ? personalBenefits.map((benefit, i) => (
                  <ListItem key={i} fontSize="16px" fontFamily="Plus-Jakarta-Sans" fontWeight={500}>
                    <ListIcon as={BiCheckCircle} w="20px" h="20px" color="#00DE53" />
                    {benefit}
                  </ListItem>
                ))
              : platformBenefits.map((benefit, i) => (
                  <ListItem key={i} fontSize="16px" fontFamily="Plus-Jakarta-Sans" fontWeight={500}>
                    <ListIcon as={BiCheckCircle} w="20px" h="20px" color="#00DE53" />
                    {benefit}
                  </ListItem>
                ))}
          </List>
        </Flex>
        <Flex
          w={{ base: '100%', md: '50%' }}
          h="100%"
          justifyContent="center"
          alignItems="center"
          position="relative"
        >
          <Flex
            w={{ base: '350px', md: '426px', lg: '606px' }}
            h={{ base: '366px', md: '426px', lg: '606px' }}
            alignItems="center"
            position="relative"
          >
            <Box
              w={{ base: '366px', md: '606px' }}
              h={{ base: '266px', md: '507px' }}
              backgroundImage={
                isPersonalPlan
                  ? 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fpersonal%2Fcar.png?alt=media&token=a38c25d2-b104-476b-9fe3-dbb51ff9c62e'
                  : 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fplatform%2Fcar.png?alt=media&token=a72f2ecc-4466-428f-9016-e3c8cba4e914'
              }
              backgroundSize="contain"
              backgroundRepeat="no-repeat"
              backgroundPosition="center"
              zIndex={1}
            />
            <Box
              w={{ base: '304px', md: '484px', lg: '584px' }}
              h={{ base: '245px', md: '325px', lg: '425px' }}
              top={{ base: '100px', lg: '150px' }}
              right={{ base: '0px', md: '-70px', lg: '-45px' }}
              backgroundImage={
                isPersonalPlan
                  ? 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fpersonal%2Fform.png?alt=media&token=e7ac82c5-08c6-4931-8a5b-8601b7514b07'
                  : 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fplatform%2Fform.png?alt=media&token=14d4bba6-188a-4e4b-a94d-c7bec5764c7b'
              }
              backgroundSize="contain"
              backgroundRepeat="no-repeat"
              backgroundPosition="center"
              position="absolute"
              zIndex={0}
            />
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
}
