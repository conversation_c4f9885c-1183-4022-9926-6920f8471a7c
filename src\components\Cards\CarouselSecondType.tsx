'use client';
/* eslint-disable @typescript-eslint/no-use-before-define */
import { useState } from 'react';

// import "https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
// import "https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"

// Here we have used react-icons package for the icons
// And react-slick as our Carousel Lib
import Slider from 'react-slick';
import { AiFillStar } from 'react-icons/ai';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';
import testimonial1 from '@/assets/home/<USER>/testimonio1.png';
import testimonial2 from '@/assets/home/<USER>/testimonio2.png';
import testimonial3 from '@/assets/home/<USER>/testimonio3.png';
import testimonial4 from '@/assets/home/<USER>/testimonio4.jpg';
import Image from 'next/image';

// Settings for the slider
const settings = {
  dots: false,
  arrows: false,
  fade: false,
  autoplay: true,
  autoplaySpeed: 6000,
  infinite: true,
  speed: 800,
  slidesToShow: 3,
  slidesToScroll: 1,
  responsive: [
    {
      breakpoint: 1180,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 960,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 440,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
  ],
};
interface TestimonialProps {
  name: string;
  car: string;
  description: string;
  image: string;
  stars: number[];
}

const testimonials: TestimonialProps[] = [
  {
    name: 'David',
    car: 'MG5 Excite Sedán',
    image: testimonial1.src,
    description:
      'El proceso fue sencillo, tardé aproximadamente una semana desde que mandé mi primer mensaje pidiendo informes hasta hoy que me están entregando mi auto. Todo ha sido muy fácil y los chicos de atención al cliente fueron muy amables. ',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: 'Alonso',
    car: 'Chevrolet Onix Sedán',
    image: testimonial2.src,
    description:
      'Mi proceso fue un poco tardado porque se atravesó el puente y tuve lista de espera, pero fuera de eso ha sido una gran oportunidad. Es mi primer carro del año, y la verdad no podría estar más contento.',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: 'Mariela',
    car: 'Kia Rio Sedán',
    image: testimonial3.src,
    description:
      '¡Adoro mi KIA rio color plata!, es uno de los autos más bonitos que he tenido, y el primer nuevo que estreno. Alex de atención a clientes fue una maravilla de persona, me guió durante mi proceso y pude estrenar muy pronto. Mucho más que en la agencia.',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: 'César',
    car: 'Kia Rio Sedán',
    image: testimonial4.src,
    description:
      'La experiencia ha sido increíble, me llevo el Kia Rio nuevecito a chambear desde hoy. El proceso tardó 3 días desde que entregué mis papeles. Todos fueron muy amables, en especial Alex que fue quien me atendió desde el principio.',
    stars: [1, 1, 1, 1, 1],
  },
];

// export default function CarouselHome() {
//   const [slider, setSlider] = useState<Slider | null>(null);
//   // These are the images used in the slide

//   const [opinions] = useState(testimonials);
//   return (
//     <>
//       <link
//         rel="stylesheet"
//         type="text/css"
//         charSet="UTF-8"
//         href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css"
//       />
//       <link
//         rel="stylesheet"
//         type="text/css"
//         href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css"
//       />
//       <Flex
//         w={{ base: '100%', md: '80%', cards: '100%', lg: '90%', xxl: '75%' }}
//         h="100%"
//         my={'20px'}
//         position="relative"
//         bgColor="bgColor"
//         justifyContent="center"
//       >
//         <Box
//           w={'100%'}
//           sx={{
//             '.slick-dots': {
//               transform: 'translateY(1.7em)',
//             },
//             '.slick-dots li button': {
//               _before: {
//                 transition: '0.2s',
//                 content: "''",
//                 borderRadius: '100%',
//                 background: '#7B61FF',
//               },
//             },
//             '.slick-arrow': {
//               backgroundColor: 'cyan.500',
//               color: 'white',
//               w: '30px',
//               h: '50px',
//               transition: '0.2s',
//               _hover: {
//                 backgroundColor: '#7B61FF',
//                 color: 'white',
//               },
//               _focus: {
//                 backgroundColor: 'cyan.500',
//                 color: 'white',
//               },
//               _before: {
//                 transition: '0.2s',
//               },
//             },
//           }}
//         >
//           <Slider {...settings} ref={(sliders) => setSlider(sliders)}>
//             {opinions.map((e, i) => {
//               return (
//                 <SingleCard
//                   key={i}
//                   name={e.name}
//                   image={e.image}
//                   description={e.description}
//                   car={e.car}
//                   stars={e.stars}
//                 />
//               );
//             })}
//           </Slider>
//           <Center /* pag={6} */ mt="50px" display="none">
//             <Button
//               w="50px"
//               h="50px"
//               borderRadius="50%"
//               bgColor="purple.strong"
//               color="white"
//               onClick={() => slider?.slickPrev()}
//               _hover={{ bgColor: '#7521FE' }}
//             >
//               <Icon as={FiArrowLeft} w="24px" h="24px" />
//             </Button>
//             <Button
//               w="50px"
//               h="50px"
//               borderRadius="50%"
//               bgColor="purple.strong"
//               color="white"
//               onClick={() => slider?.slickNext()}
//               _hover={{ bgColor: '#7521FE' }}
//             >
//               <Icon as={FiArrowRight} w="24px" h="24px" />
//             </Button>
//           </Center>
//         </Box>
//       </Flex>
//     </>
//   );
// }

export default function CarouselHome() {
  const [slider, setSlider] = useState<Slider | null>(null);
  const [opinions] = useState(testimonials);

  return (
    <>
      <link
        rel="stylesheet"
        type="text/css"
        charSet="UTF-8"
        href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css"
      />
      <link
        rel="stylesheet"
        type="text/css"
        href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css"
      />

      <div
        className="
        w-full md:w-[80%] cards:w-full lg:w-[90%] xxl:w-[75%]
        h-full 
        my-[20px]
        relative 
        bg-bgColor 
        flex justify-center
      "
      >
        <div className="w-full [&_.slick-dots]:translate-y-[1.7em]">
          <style jsx>{`
            :global(.slick-dots li button:before) {
              transition: 0.2s;
              content: '';
              border-radius: 100%;
              background: #7b61ff;
            }
            :global(.slick-arrow) {
              background-color: var(--cyan-500);
              color: white;
              width: 30px;
              height: 50px;
              transition: 0.2s;
            }
            :global(.slick-arrow:hover) {
              background-color: #7b61ff;
              color: white;
            }
            :global(.slick-arrow:focus) {
              background-color: var(--cyan-500);
              color: white;
            }
            :global(.slick-arrow:before) {
              transition: 0.2s;
            }
          `}</style>

          <Slider {...settings} ref={(sliders) => setSlider(sliders)}>
            {opinions.map((e, i) => (
              <SingleCard
                key={i}
                name={e.name}
                image={e.image}
                description={e.description}
                car={e.car}
                stars={e.stars}
              />
            ))}
          </Slider>

          <div className="hidden mt-[50px] justify-center">
            <button
              className="
                w-[50px] 
                h-[50px] 
                rounded-full 
                bg-purple-strong 
                text-white
                hover:bg-[#7521FE]
                mr-2
              "
              onClick={() => slider?.slickPrev()}
            >
              <FiArrowLeft className="w-6 h-6 mx-auto" />
            </button>
            <button
              className="
                w-[50px] 
                h-[50px] 
                rounded-full 
                bg-purple-strong 
                text-white
                hover:bg-[#7521FE]
              "
              onClick={() => slider?.slickNext()}
            >
              <FiArrowRight className="w-6 h-6 mx-auto" />
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

function SingleCard({ name, image, description, car, stars }: TestimonialProps) {
  return (
    <div className="flex items-center justify-center h-[330px]">
      <div
        className="
        w-[350px] md:w-[370px]
        h-[300px]
        p-[35px]
        select-none
        relative
        rounded-[20px] md:rounded-[35px]
        shadow-[0px_10px_12px_rgba(195,198,255,0.2)]
        text-text-main
        bg-white
      "
      >
        <div className="w-full p-0 mb-3 font-[Plus-Jakarta-Sans] font-bold">
          <div className="w-full font-[Plus-Jakarta-Sans]">
            <div className="flex items-center w-full h-full flex-wrap gap-4">
              <Image
                src={image}
                alt={'testimonial-' + name}
                width={60}
                height={60}
                className="rounded-full h-16 w-16 object-cover"
                loading="lazy"
              />
              <div>
                <h2
                  className="
                  text-[#742BFA]
                  text-[20px]
                  font-bold
                  mb-1
                  font-[Plus-Jakarta-Sans]
                "
                >
                  {name}
                </h2>
                <p className="text-purple-strong font-normal">{car}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col mt-[8px]">
          <p
            className="
            text-justify 
            font-[Plus-Jakarta-Sans] 
            text-[13px] 
            font-medium
            text-description
          "
          >
            {description}
          </p>
        </div>

        <div className="absolute bottom-[20px]">
          {stars.map((_, i) => (
            <AiFillStar key={i} className="inline-block w-[20px] h-[20px] text-[#FFB422]" />
          ))}
        </div>
      </div>
    </div>
  );
}
