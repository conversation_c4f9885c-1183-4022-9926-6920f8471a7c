/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-use-before-define */
// import ReserveNowBtn from '../../usComponents/ReserveNowBtn';
// import { useState } from 'react';
import { Payments as IPayments } from '../data/data';
import ContactModal from '../../(components)/Modals/ContactModal';
// import CarPriceCalculatorModal from '../../(components)/USHome/car-price-calculator-modal';
// import { Button } from '@/components/ui/button';
import { IAtributes } from './Description';

interface PaymentsProps {
  payments: IPayments;
  payment: number;
  isMobile?: boolean;
  vehicle: {
    name: string;
    cardImage: string;
    price: number;
    features: IAtributes;
    comingSoon?: boolean;
  };
}

export default function Payments({ isMobile, payment, vehicle }: PaymentsProps) {
  // const carData = {
  //   carModel: vehicle.name,
  //   carYear: '2025',
  //   carImage: vehicle.cardImage,
  //   carSpecs: {
  //     gearBox: vehicle.features.engine,
  //     fuel: vehicle.features.fuel,
  //     doors: vehicle.features.doors,
  //     airConditioner: vehicle.features.airConditioner,
  //     seats: vehicle.features.seats,
  //     distance: vehicle.features.distance,
  //   },
  //   carPrice: vehicle.price,
  //   trigger: (
  //     <>
  //       <Button
  //         className="border-[#5A00F8] border-2 text-[#5A00F8] hover:text-[#5a0be3] font-semibold h-[50px] w-full mt-3"
  //         variant="outline"
  //       >
  //         Lease Calculator
  //       </Button>
  //     </>
  //   ),
  // };

  return (
    <div
      className={`
        w-full h-[max-content] 
        ${isMobile ? 'flex xl:hidden' : 'hidden xl:flex'}
        justify-center 
        lx:justify-end 
        font-[Plus-Jakarta-Sans]

      `}
    >
      <div
        // className="w-[100%] max-w-[600px] h-[300px] md:h-[400px] rounded flex-col items-center p-[30px] bg-[#FFFFFF] text-[#464E5F]"
        className="w-[100%] max-w-[600px] h-[max-content] rounded flex-col items-center p-[30px] bg-[#FFFFFF] text-[#464E5F] gap-2
          shadow-[0,10px,30px,0,rgba(195,198,255,0.20)]
        "
      >
        {/* <p className="text-[12px] md:text-[28px] text-[#742BFA] font-bold ">48 months contract</p> */}
        <div className="flex flex-col items-center mb-2">
          {vehicle.comingSoon ? (
            <p className="text-3xl text-[#5A00F8] font-[600] my-5">Coming Soon</p>
          ) : (
              <p className="text-[28px] md:text-[64px] text-[#3F404C] font-bold ">
                ${payment}
                <span className=" italic text-[12px] md:text-[18px] font-normal ">/week*</span>
              </p>
          )}
          {/* <p className="font-[12px]">*Taxes included</p> */}
        </div>
        <ContactModal price={payment} vehicleName={vehicle.name} comingSoon={vehicle.comingSoon} />
        {/* <CarPriceCalculatorModal {...carData} /> */}
      </div>
    </div>
  );
}
