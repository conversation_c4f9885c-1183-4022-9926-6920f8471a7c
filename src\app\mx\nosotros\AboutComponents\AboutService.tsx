import { Box, Flex, Text } from '@chakra-ui/react';
import React from 'react';

const AboutService = () => {
  return (
    <>
      <Flex
        w={{ base: '80%', l: '100%' }}
        p="50px"
        h="100%"
        minH={'295px'}
        justify="center"
        align={'center'}
        bgColor="rgba(195, 198, 255, 0.2)"
        borderRadius={'20px'}
      >
        <Flex
          sx={{ '> *:not(:last-child)': { mb: { base: '4vw', l: 0 }, mr: { base: 0, l: '4vw' } } }}
          flexDir={{ base: 'column', l: 'row' }}
          fontFamily="Plus-Jakarta-Sans"
          justifyContent="space-evenly"
          width="100%"
        >
          <Box>
            <Text textAlign={'center'} fontSize={'48px'} color="purple.strong" fontWeight={700}>
              {' '}
              +1k{' '}
            </Text>
            <Text textAlign={'center'} fontSize={'16px'} color="text.description" fontWeight={600}>
              {' '}
              Autos entergados{' '}
            </Text>
          </Box>

          <Box>
            <Text textAlign={'center'} fontSize={'48px'} color="purple.strong" fontWeight={700}>
              {' '}
              +2M{' '}
            </Text>
            <Text textAlign={'center'} fontSize={'16px'} color="text.description" fontWeight={600}>
              {' '}
              Viajes realizados{' '}
            </Text>
          </Box>

          <Box>
            <Text textAlign={'center'} fontSize={'48px'} color="purple.strong" fontWeight={700}>
              {' '}
              +21{' '}
            </Text>
            <Text textAlign={'center'} fontSize={'16px'} color="text.description" fontWeight={600}>
              {' '}
              Ciudades en operación{' '}
            </Text>
          </Box>

          <Box>
            <Text textAlign={'center'} fontSize={'48px'} color="purple.strong" fontWeight={700}>
              {' '}
              #1{' '}
            </Text>
            <Text textAlign={'center'} fontSize={'16px'} color="text.description" fontWeight={600}>
              {' '}
              En renting de México{' '}
            </Text>
          </Box>
        </Flex>
      </Flex>
    </>
  );
};

export default AboutService;
