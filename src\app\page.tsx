import { redirect } from 'next/navigation';
import { headers } from 'next/headers';

export default async function Home() {
  const headersList = headers();
  const country: string =
    headersList.get('x-country')?.toString() || headersList.get('x-vercel-ip-country') || 'MX';
  // console.log('headerList: ', headersList);
  if (country === 'US') redirect('/us');
  if (country === 'BR') redirect('/br');
  else return redirect('/mx');
}
