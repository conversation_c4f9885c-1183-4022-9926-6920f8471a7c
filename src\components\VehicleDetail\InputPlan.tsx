'use client';
import { Box, Flex, Text } from '@chakra-ui/react';
import { Dispatch, SetStateAction } from 'react';

interface InputPlanProps {
  selectedPlan: number;
  setSelectedPlan: Dispatch<SetStateAction<number>>;
  setNamePlan: Dispatch<SetStateAction<string>>;
  data: {
    biweeklyPay: number;
    deposit: number;
    label: string | null;
  };
  index: number;
  namePlan: string;
}

export default function InputPlan({
  selectedPlan,
  setSelectedPlan,
  data,
  index,
  setNamePlan,
  namePlan,
}: InputPlanProps) {
  return (
    <Flex
      w="235px"
      h="max-content"
      mb="15px"
      p="10px"
      border="2px solid"
      borderColor={selectedPlan === index ? '#028CF3' : 'text.main'}
      flexDir="column"
      borderRadius="20px"
      cursor="pointer"
      position="relative"
      color={selectedPlan === index ? '#028CF3' : 'text.main'}
      onClick={() => {
        setSelectedPlan(index);
        setNamePlan(namePlan);
      }}
      fontFamily="Plus-Jakarta-Sans"
    >
      <>
        <Box
          h="20px"
          w="20px"
          borderRadius="50%"
          position="absolute"
          top="13px"
          left="13px"
          border="1.4px solid"
          borderColor={selectedPlan === index ? '#028CF3' : 'text.main'}
          p="5px"
        />

        <Box
          w="10px"
          h="10px"
          position="absolute"
          top="18px"
          left="18px"
          bgColor={selectedPlan === index ? '#028CF3' : 'transparent'}
          borderRadius="50px"
        />

        <Text mt="2px" w="100%" pl="30px" fontSize="12px" fontWeight={600}>
          {data.label}
        </Text>
      </>

      <Flex w="100%" h="100%" mt="5px" px="3px" flexDir="column">
        <Flex alignItems="end" fontWeight={700}>
          <Text fontSize="30px">${data.biweeklyPay.toLocaleString()}</Text>
          <Text fontSize="14px" mb="7px">
            / quincenal
          </Text>
        </Flex>
        <Flex alignItems="center">
          <Text fontSize="14px" mt="3px">
            Con pago inicial de
          </Text>

          <Text fontWeight={700} ml="3px">
            ${data.deposit.toLocaleString()}
          </Text>
        </Flex>
      </Flex>
    </Flex>
  );
}
