'use client';

import Testimonials from '@/components/Cards/CarouselSecondType';

export default function TestimonialCarousel() {
  return (
    <section
      id="testimonials"
      className="
        w-full 
        h-full 
        flex 
        flex-col 
        bg-[#FAFAFF]
        justify-center 
        items-center 
        pt-[100px]
        pb-[50px]
        [&>*:not(:last-child)]:mb-10
      "
    >
      <div
        className="
          h-max-content 
          flex 
          flex-col 
          items-center 
          justify-center 
          [&>*:not(:last-child)]:mb-5
          text-center 
          px-[10px]
          text-text-main
        "
      >
        <h2 className="font-[Plus-Jakarta-Sans] font-[800] text-[30px]">Testimonios</h2>

        <p className="font-[Manrope] font-medium">
          Estas son algunas de las grandes experiencias que nuestros clientes han vivido con OCN
        </p>
      </div>

      <Testimonials />
    </section>
  );
}
