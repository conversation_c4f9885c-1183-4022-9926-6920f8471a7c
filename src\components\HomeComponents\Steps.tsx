'use client';

import { FaCarSide, FaClipboardList } from 'react-icons/fa';
import { HiOutlineMailOpen } from 'react-icons/hi';
import { IconType } from 'react-icons/lib';
import Image from 'next/image';
import { COUNTRY_DATA } from '@/constants';
import LineImg from '@/assets/mx/line.png';

type StepProps = {
  step: string;
  description: string;
  icon: IconType;
};

function getStepsTranslation(country: keyof typeof COUNTRY_DATA) {
  return [
    {
      step: COUNTRY_DATA[country].home.steps.step1.title,
      description: COUNTRY_DATA[country].home.steps.step1.subtitle,
      icon: FaCarSide,
    },
    {
      step: COUNTRY_DATA[country].home.steps.step2.title,
      description: COUNTRY_DATA[country].home.steps.step2.subtitle,
      icon: FaClipboardList,
    },
    {
      step: COUNTRY_DATA[country].home.steps.step3.title,
      description: COUNTRY_DATA[country].home.steps.step3.subtitle,
      icon: HiOutlineMailOpen,
    },
  ];
}

function Step({ props }: { props: StepProps }) {
  return (
    <li className="w-[320px] m-0 flex flex-col text-center items-center [&>*:not(:last-child)]:mb-1">
      <div className="w-[100px] h-[100px] bg-purple-soft rounded-tr-[25px] rounded-tl-[25px] rounded-br-[25px] flex justify-center items-center">
        <props.icon className="text-white w-[50px] h-[50px]" />
      </div>
      <p className="text-purple-soft font-bold mt-[10px] font-[Plus-Jakarta-Sans]">{props.step}</p>
      <p className="font-[Manrope]">{props.description}</p>
    </li>
  );
}

interface StepsProps {
  country?: 'MX' | 'US' | 'BR';
}

export default function Steps({ country = 'MX' }: StepsProps) {
  const stepsTranslated = getStepsTranslation(country);
  return (
    <div className="h-max-content  pt-[30px] pb-[50px] w-full flex items-center flex-col text-text-main">
      <div className="w-[90%] md:w-[60%] lg:w-[50%] text-center flex items-center [&>*:not(:last-child)]:mb-6 flex-col">
        <h2 className="text-[28px] md:text-[32px] text-purple-strong font-[Plus-Jakarta-Sans] font-bold">
          {/* Estrena con OCN en 3 pasos */}
          {COUNTRY_DATA[country].home.steps.title}
        </h2>

        <p className="font-[Manrope] font-medium">
          {/* Adquiere un auto nuevo a largo plazo sin enganches ni depositos en garantia para continuar */}
          {/* trabajando en plataforma de movilidad */}
          {COUNTRY_DATA[country].home.steps.subtitle}
        </p>
      </div>

      <div className="w-[85%] p-0 justify-evenly flex-wrap mt-[70px] ml-0 relative flex">
        <div className="w-[75%] justify-evenly top-[100px] absolute hidden lg:flex">
          <Image
            width={200}
            height={25}
            className="w-[20%] h-[25px] object-contain transform matrix-[1,0,0,1,5,0]"
            src={LineImg}
            alt="line-1"
            loading="lazy"
          />

          <Image
            width={200}
            height={25}
            className="w-[20%] h-[25px] object-contain transform scale-y-[-1] mt-4"
            src={LineImg}
            alt="line-2"
            loading="lazy"
          />
        </div>
      </div>

      <ul
        className="w-[85%] p-0 justify-evenly flex-wrap mt-[70px] ml-0 relative flex 
        [&>*:not(:last-child)]:mb-16 md:[&>*:not(:last-child)]:mb-7 lg:[&>*:not(:last-child)]:mb-0"
      >
        {stepsTranslated.map((step, i) => (
          <Step key={i} props={step} />
        ))}
      </ul>
    </div>
  );
}
