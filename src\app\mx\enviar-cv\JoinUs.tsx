'use client';
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Divider, Flex, Heading, Spinner, Text } from '@chakra-ui/react';
import { cvSchema } from '@/schemas/contactSchema';
import axios from 'axios';
import { Formik } from 'formik';
import { Dispatch, useEffect, useState } from 'react';
import Swal from 'sweetalert2';
import { State } from 'country-state-city';
import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import CustomField from '@/components/Form/FieldForm';
import SelectForm from '@/components/Form/SelectForm';
import FileInput from '@/components/Form/FileInput';
import CustomButton from '@/components/CustomButton';

const bgPlataform = 'plan.platform.bgColor';
const bgCirclePlatform = 'plan.platform.circles';

const countries = [
  { label: 'México', key: 'MX' },
  { label: 'Estados Unidos', key: 'US' },
  { label: 'Colombia', key: 'CO' },
  { label: 'Chile', key: 'CL' },
  { label: 'Brasil', key: 'BR' },
];

export default function JoinUs() {
  const [sending, setSending] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Navbar country="mx" />
      <Flex w="100%" minH="94vh" h="100%" flexDir="column" alignItems="center">
        <Flex
          w="100%"
          height="360px"
          position="relative"
          justifyContent="center"
          bg={bgPlataform}
          overflow="hidden"
        >
          <Flex
            w={{ base: '100%', md: '50%', lg: '30%' }}
            px={{ base: '20px', md: 0 }}
            textAlign="center"
            flexDir="column"
            gap={5}
            color="white"
            justifyContent="center"
            alignItems="center"
            fontFamily="Plus-Jakarta-Sans"
            zIndex="2"
          >
            <Heading as="h2">Únete al equipo</Heading>
            <Divider w="42px" border={'2px solid'} borderColor="#43D17D" borderRadius="3px" />
            <Text>
              En OCN buscamos al talento con el más alto potencial para seguir innovando en la forma de
              estrenar autos en mexico
            </Text>
          </Flex>

          <Box
            sx={{
              w: '650px',
              h: '650px',
              position: 'absolute',
              left: '50%',
              transform: 'translateX(-24%)',
              top: '-480px',
              bg: bgCirclePlatform,
              borderRadius: '50%',
            }}
          />

          <Box
            sx={{
              w: '650px',
              h: '650px',
              position: 'absolute',
              left: '-130px',
              bottom: '-480px',
              bg: bgCirclePlatform,
              borderRadius: '50%',
            }}
          />

          <Box
            sx={{
              w: '550px',
              h: '550px',
              position: 'absolute',
              right: '-230px',
              bottom: '-330px',
              bg: bgCirclePlatform,
              borderRadius: '50%',
            }}
          />
        </Flex>
        <Flex
          w="100%"
          minH="500px"
          position="relative"
          flexDir="column"
          alignItems="center"
          mt="60px"
          mb="100px"
        >
          {sending ? (
            <>
              <Flex
                w="100%"
                h="100%"
                justifyContent="center"
                alignItems="center"
                position="absolute"
                zIndex={3}
              >
                <>
                  <Spinner
                    thickness="15px"
                    speed="0.65s"
                    emptyColor="gray.200"
                    color="gray.400"
                    w="200px"
                    h="200px"
                  />
                </>
              </Flex>
            </>
          ) : null}
          <Text mb="60px" fontSize="30px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
            Envíanos tu CV
          </Text>
          <Flex w={{ base: '80%', md: '60%', xxl: '40%' }}>
            <FormContact setSending={setSending} sending={sending} />
          </Flex>
        </Flex>
      </Flex>
      <Footer footerBgColor="footer.main" />
    </>
  );
}

type StateProps = {
  key: string;
  label: string;
}[];

function FormContact({
  sending,
  setSending,
}: {
  sending: boolean;
  setSending: Dispatch<React.SetStateAction<boolean>>;
}) {
  const [countrySelected, setCountrySelected] = useState('');
  const [states, setStates] = useState<StateProps>([]);

  // const [citySelected, setCitySelected] = useState('');
  // const [cities, setCities] = useState<StateProps>([]);

  /* const allStateOfCountry = State.getStatesOfCountry("MX")
  console.log(allStateOfCountry) */

  useEffect(() => {
    // setStates(allStateOfCountry);

    const allStateOfCountry = State.getStatesOfCountry(countrySelected);
    const stateObjects = allStateOfCountry.map((state) => {
      const obj = {
        key: state.isoCode,
        label: state.name,
      };
      return obj;
    });

    setStates(stateObjects);
  }, [countrySelected]);

  async function sendData(form: any) {
    let res = await axios.post('https://api.onecarnow.com/candidate/send-cv', form, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return res;
  }

  const handleResetForm = (resetForm: any) => {
    const fileInput = document.querySelector('input[name="cv"]') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
    resetForm();
    setNameFile('');
  };

  const [nameFile, setNameFile] = useState('');

  return (
    <Formik
      initialValues={{
        name: '',
        birthday: '',
        email: '',
        country: '',
        state: '',
        city: '',
        cv: '',
      }}
      validationSchema={cvSchema}
      onSubmit={async (values, { resetForm }) => {
        try {
          setSending(true);

          const state = states.find((s) => s.key === values.state);
          const stateName = state?.label;
          values.state = stateName as string;
          await sendData(values);

          Swal.fire({
            title: 'Información enviada',
            text: 'Te contactáremos lo más pronto posible',
            icon: 'success',
            confirmButtonText: 'Cerrar',
          });
          setSending(false);

          handleResetForm(resetForm);
        } catch (e: any) {
          // console.log(e)
          Swal.fire({
            title: 'Algo salió mal',
            text: e.response.data.message,
            icon: 'error',
            confirmButtonText: 'Cerrar',
          });
          setSending(false);
          handleResetForm(resetForm);
        }
      }}
    >
      {({ errors, touched, handleSubmit }) => (
        <form
          onSubmit={handleSubmit}
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Flex
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: 3, l: 5 } } }}
            w={{ base: '90%', l: '100%' }}
            alignItems="center"
            fontFamily="Plus-Jakarta-Sans"
          >
            <CustomField
              name="name"
              label="Nombre completo"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="text"
              placeholder="Nombres..."
            />

            <CustomField
              name="birthday"
              label="Fecha de nacimiento"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="text"
              placeholder="Ejemplo: dd/mm/yyyy"
            />

            <CustomField
              name="email"
              label="Correo electrónico"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="email"
              placeholder="<EMAIL>"
            />

            <SelectForm
              name="country"
              label="País"
              errors={errors}
              touched={touched}
              firstOptionDisabled="Ingresa tu país"
              setCountrySelected={setCountrySelected}
              optionFields={countries}
            />

            <SelectForm
              name="state"
              label="Estado"
              errors={errors}
              touched={touched}
              firstOptionDisabled="Selecciona el estado donde vives"
              optionFields={states}
            />

            <CustomField
              name="city"
              placeholder="Ejemplo: Ciudad de mexico..."
              label="Ciudad, municipio o residencia"
              type="text"
              errors={errors}
              touched={touched}
              // firstOptionDisabled="Selecciona la ciudad donde vives"
              // optionFields={[{ label: "Estado de mexico", key: "df" }]}
            />

            <FileInput
              name="cv"
              label="Agrega tu CV"
              touched={touched}
              errors={errors}
              type="file"
              setNameFile={setNameFile}
              nameFile={nameFile}
              placeholder="Subir cv en formato .pdf menor a 1mb"
            />

            <CustomButton message="Enviar" className="btn-purple" type="submit" disabled={!!sending} />
          </Flex>
        </form>
      )}
    </Formik>
  );
}
