// NETA V
import ocnVMain from '@/assets/electricos/vehicles/ocnVimages/NETA-V-main.png';
import ocnV1 from '@/assets/electricos/vehicles/ocnVimages/V01.webp';
import ocnV2 from '@/assets/electricos/vehicles/ocnVimages/V02.webp';
import ocnV3 from '@/assets/electricos/vehicles/ocnVimages/V03.webp';
import ocnV4 from '@/assets/electricos/vehicles/ocnVimages/V04.webp';
// import oncv04 from '@/assets/electricos/vehicles'

// NETA U PRO
import ocnUPROMain from '@/assets/electricos/vehicles/ocnUPROimages/NETA-UPRO-main.png';
import ocnUPRO1 from '@/assets/electricos/vehicles/ocnUPROimages/UPro01.webp';
import ocnUPRO2 from '@/assets/electricos/vehicles/ocnUPROimages/UPro02.webp';
import ocnUPRO3 from '@/assets/electricos/vehicles/ocnUPROimages/UPro03.webp';
import ocnUPRO4 from '@/assets/electricos/vehicles/ocnUPROimages/UPro04.webp';

// NETA GT

import ocnGTMain from '@/assets/electricos/vehicles/ocnGTimages/NETA-GT-main.png';
import ocnGT1 from '@/assets/electricos/vehicles/ocnGTimages/GT01.webp';
import ocnGT2 from '@/assets/electricos/vehicles/ocnGTimages/GT02.webp';
import ocnGT3 from '@/assets/electricos/vehicles/ocnGTimages/GT03.webp';
import ocnGT4 from '@/assets/electricos/vehicles/ocnGTimages/GT04.webp';

// NETA S

import ocnSMain from '@/assets/electricos/vehicles/ocnSimages/NETA-S-main.png';
import ocnS1 from '@/assets/electricos/vehicles/ocnSimages/s01.webp';
import ocnS2 from '@/assets/electricos/vehicles/ocnSimages/s02.webp';
import ocnS3 from '@/assets/electricos/vehicles/ocnSimages/s03.webp';
import ocnS4 from '@/assets/electricos/vehicles/ocnSimages/s04.webp';
import { StaticImageData } from 'next/image';

export type ModalInfo = {
  fullName: string;
  range: number;
  motor: number;
  acceleration: number;
  fastCharge: number;
  airBags: number;
  description: string;
  features: string[];
  images: StaticImageData[];
};

export interface DisplayVehicleProps {
  shortName: string;
  mainImage: string;
  option: {
    label: string;
    value: string;
  };
  modalInfo: ModalInfo;
}

const vehiclesData: DisplayVehicleProps[] = [
  {
    shortName: 'NETA V',
    mainImage: ocnVMain.src,
    option: {
      label: 'NETA V',
      value: 'NETA V',
    },
    modalInfo: {
      fullName: 'NETA MODEL V',
      range: 301,
      motor: 54,
      acceleration: 6.5,
      fastCharge: 0.5,
      airBags: 2,
      description:
        'Conoce el NETA V: El SUV eléctrico que contiene un paquete de baterías de alta tecnología que te permitirá hacer viajes de hasta 301 Km en una sola carga. Capaz de transportar a una familia de 5 integrantes y su equipaje, con la mayor comodidad y estilo único. (Conservador)',
      features: [
        'Pantalla táctil de 17 pulgadas',
        'Tracción delantera con caja de cambios automática',
        'Acabados de lujo y atmósfera minimalista',
        'Luces diurnas LED con apagado automático',
        'Con sistema de frenos ABS en las cuatro ruedas',
        'Un interior práctico y con gran espacio para todos los pasajeros',
      ],
      images: [ocnV1, ocnV2, ocnV3, ocnV4],
    },
  },
  {
    shortName: 'NETA U PRO',
    mainImage: ocnUPROMain.src,
    option: {
      label: 'NETA U PRO',
      value: 'NETA U PRO',
    },
    modalInfo: {
      fullName: 'NETA MODEL U PRO',
      range: 611,
      motor: 231,
      acceleration: 7,
      fastCharge: 0.5,
      airBags: 6,
      description:
        'Conoce el NETA U Pro: La camioneta contiene un paquete de baterías de alta tecnología bajo su chasis, que te permitirán hacer viajes de hasta 611 Km en una sola carga. Un auto con gran amplitud interior y con tecnología de vanguardia que te permitirá utilizar los gadgets de la manera más sencilla.',
      features: [
        'Sistema de infoentretenimiento con triple pantalla enfrente',
        'Acabados en piel vegana y volante forrado con acabado deportivo',
        'Aire acondicionado automático de multizona',
        'Techo panorámico con vista de 130º',
        'Asientos en cubo con corte deportivo y acabados en aluminio cepillado',
        'Recuperador de carga en los cuatros ejes y sistema de frenado ABS',
      ],
      images: [ocnUPRO1, ocnUPRO2, ocnUPRO3, ocnUPRO4],
    },
  },
  {
    shortName: 'NETA GT',
    mainImage: ocnGTMain.src,
    option: {
      label: 'NETA GT',
      value: 'NETA GT',
    },
    modalInfo: {
      fullName: 'NETA MODEL GT',
      range: 580,
      motor: 462,
      acceleration: 3.7,
      fastCharge: 0.5,
      airBags: 8,
      description:
        'Conoce el NETA GT: El auto deportivo eléctrico con elegancia y lujo, fabricado con dos motores los cuales producen una aceleración en 3.7 segundos. En el ámbito de la tecnología el vehículo cuenta con 5 cámaras y 3 radares de microondas que facilitan el manejo dinámico, habilitando un modo de seguridad inteligente.',
      features: [
        'Sistema de infoentretenimiento con triple pantalla enfrente',
        'Acabados en piel vegana y volante forrado con acabado deportivo',
        'Aire acondicionado automático de multizona',
        'Faros con luces LED de doble zona',
        'Control de tracción con niveles de potencia',
        'Cierre centralizado y controles de estacionamiento inteligente',
      ],
      images: [ocnGT1, ocnGT2, ocnGT3, ocnGT4],
    },
  },
  {
    shortName: 'NETA S',
    option: {
      label: 'NETA S',
      value: 'NETA S',
    },
    mainImage: ocnSMain.src,
    modalInfo: {
      fullName: 'NETA MODEL S',
      range: 650,
      motor: 462,
      acceleration: 3.9,
      fastCharge: 0.5,
      airBags: 8,
      description:
        'Conoce el NETA S: El auto de gama más alta ofrecido por la marca, fabricado con dos motores los cuales producen una aceleración de 3.9 segundos. Con un diseño que denota exclusividad y lujo. Vehículo con gran amplitud y comodidad de 4 plazas.',
      features: [
        'Sistema de infoentretenimiento con triple pantalla enfrente',
        'Acabados en piel vegana y volante forrado con acabado deportivo',
        'Aire acondicionado automático de multizona',
        'Techo panorámico con vista de 130º',
        'Asientos en cubo con corte deportivo y acabados en aluminio cepillado',
        'Recuperador de carga en los cuatros ejes y sistema de frenado ABS',
      ],
      images: [ocnS1, ocnS2, ocnS3, ocnS4],
    },
  },
];
export default vehiclesData;
