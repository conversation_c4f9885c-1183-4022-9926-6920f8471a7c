'use client';
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/rules-of-hooks */
import { useRouter } from 'next/navigation';
import LinkWrapper from './LinkWrapper';
import { FaGasPump } from 'react-icons/fa';
import { FC, useEffect, useState } from 'react';
import { CarPropsComponent } from './types/types';
import { GiCarSeat, GiLever } from 'react-icons/gi';
import { Button, Card, Center, Flex, Heading, Icon, Img, Skeleton, Text } from '@chakra-ui/react';

const CarLetter: FC<CarPropsComponent> = ({
  title,
  image,
  price,
  cardBtnColor,
  seats,
  liters,
  transmission,
  hoverColor,
  plan,
  id,
  biweeklyPay,
}) => {
  const router = useRouter();
  const [carName, setCarName] = useState<string>('');

  useEffect(() => {
    let carName: string = title;

    if (title.includes('2023')) {
      carName = title.replace('2023', '');
    }

    setCarName(carName);
  }, []);

  const [showLink, setShowLink] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setShowLink(window.innerWidth > 550);
    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <Center w="100%">
      <LinkWrapper link={`/mx/detalle/${plan?.toLowerCase()}/${id}`}>
        <Card
          w="320px"
          h="348px"
          borderRadius="10px"
          fontFamily="Plus-Jakarta-Sans"
          p="25px"
          boxShadow="0px 12px 32px rgba(195, 198, 255, 0.2)"
          position="relative"
        >
          {title ? (
            <Heading fontSize="20px" fontWeight={700} color="text.main" fontFamily="Plus-Jakarta-Sans">
              {carName}
            </Heading>
          ) : (
            <Skeleton h="24px" w="100px" />
          )}
          <Flex w="100%" justifyContent={'center'} alignItems="center">
            <Img
              mt="15px"
              w="230px"
              h="160px"
              src={image}
              objectFit="contain"
              objectPosition="center"
              alt="notFound"
              transition="all .6s"
              _hover={{ transform: 'scale(1.25)' }}
            />
          </Flex>

          <Flex
            justifyContent="end"
            color="#90A3BF"
            sx={{ '> *:not(:last-child)': { mr: 3 } }}
            fontSize="13px"
            mt="5px"
          >
            <Flex alignItems="center" sx={{ '> *:not(:last-child)': { mr: 1 } }}>
              {seats ? (
                <>
                  <Icon w="16px" h="16px" as={FaGasPump} />
                  <Text> {liters ? liters + ' L' : '1.6 L'} </Text>
                </>
              ) : (
                <Skeleton w="70px" h="16px" mt="5px" />
              )}
            </Flex>
            <Flex alignItems="center" sx={{ '> *:not(:last-child)': { mr: 1 } }}>
              {transmission ? (
                <>
                  <Icon w="15px" h="15px" as={GiLever} />
                  <Text>
                    {transmission.split(' ')[0][0].toUpperCase() + transmission.split(' ')[0].slice(1)}
                  </Text>
                </>
              ) : (
                <Skeleton w="70px" h="16px" mt="5px" />
              )}
            </Flex>
            <Flex alignItems="center" sx={{ '> *:not(:last-child)': { mr: 1 } }}>
              <>
                <Icon w="16px" h="16px" as={GiCarSeat} />
                <Text>{seats} personas</Text>
              </>
            </Flex>
          </Flex>

          <Flex
            mt="30px"
            justifyContent="space-between"
            alignItems="end"
            w="85%"
            position="absolute"
            bottom={'15px'}
          >
            <Flex w="100%" color={cardBtnColor}>
              {price ? (
                plan === 'Personal' ? (
                  <>
                    <Flex flexDir="column">
                      <Text fontSize="14px" fontWeight={500}>
                        Desde{' '}
                      </Text>
                      <Flex>
                        <Text fontSize="24px" fontWeight={700}>
                          ${biweeklyPay}
                        </Text>
                        <Text pt="12px" as="span" fontWeight={600} fontSize={{ base: '11px', md: '12.5px' }}>
                          - Quincenal
                        </Text>
                      </Flex>
                    </Flex>
                  </>
                ) : (
                  <>
                    <>
                      <Text fontSize="24px" fontWeight={700}>
                        ${price}
                      </Text>
                      <Text pt="12px" as="span" fontWeight={600} fontSize={{ base: '11px', md: '12.5px' }}>
                        - Semanal
                      </Text>
                    </>
                  </>
                )
              ) : (
                <Skeleton h="28px" w="100px" />
              )}
            </Flex>
            <Button
              // className={`bg-[#6210FF]`}
              // className={cardBtnColor}
              // sx={{
              //   bgColor: `${cardBtnColor} !important`,
              // }}
              bgColor={`${cardBtnColor} !important`}
              w="106px"
              h="44px"
              borderRadius={4}
              color="white"
              _hover={{ bgColor: `${hoverColor} !important` }}
              onClick={() => {
                !showLink && router.push(`/mx/detalle/${plan?.toLowerCase()}/${id}`);
              }}
            >
              Cotizar
            </Button>
          </Flex>

          {/* Por si se requiere mostrar este mensaje en la card */}
          {/* {
                        plan === "Plataforma" && <Text mt="5px" color="gray.400" position="absolute" fontSize="12px" bottom={"70px"} >* Promociones especiales para tu plataforma</Text>
                    } */}
        </Card>
      </LinkWrapper>
    </Center>
  );
};

export default CarLetter;
