import { cn } from '@/lib/utils';
import { Field, FieldProps, FormikProps, useField } from 'formik';
import Select, { StylesConfig } from 'react-select';

interface SelectInputProps {
  options: {
    value: string;
    label: string;
  }[];
  label: string;
  name: string;
  disabled?: boolean;
  onChange?: (option: SelectInputProps['options'][number], form: FormikProps<any>) => void;
  direction?: 'vertical' | 'horizontal';
  className?: string;
  inputClassName?: string;
}

export default function SelectInput({
  label,
  options,
  name,
  onChange,
  direction = 'vertical',
  className,
  inputClassName,
  disabled,
}: SelectInputProps) {
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;
  const objMessage = meta.error as unknown as {
    value: string;
  };
  const errorMessage = objMessage;

  const customStyles: StylesConfig = {
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected || state.isFocused ? '#5800F7' : 'transparent',
      color: state.isSelected || state.isFocused ? 'white' : 'black',
      border: '0 !important',
      // This line disable the blue border
      boxShadow: '0 !important',
      '&:hover': {
        border: '0 !important',
      },
    }),
    control: (base, state) => ({
      ...base,
      background: 'transparent',
      height: '45px',
      // // match with the menu
      // borderRadius: state.isFocused ? "3px 3px 0 0" : 3,
      // // Overwrittes the different states of border
      borderRadius: '8px',
      borderColor: state.isFocused ? 'rgba(66,153,225,0.9)' : hasError ? '#E53E3E' : '#E5E7EB',
      borderWidth: state.isFocused || hasError ? '2px' : '1px',
      // // Removes weird border around container
      boxShadow: state.isFocused ? undefined : undefined,
      '&:hover': {
        // Overwrittes the different states of border
        // borderColor: state.isFocused ? '#E5E7EB' : '#E5E7EB',
      },
    }),
    menu: (base) => ({
      ...base,
      // override border radius to match the box
      borderRadius: 0,
      // kill the gap
      marginTop: 0,
    }),
    menuList: (base) => ({
      ...base,
      // kill the white space on first and last option
      padding: 0,
    }),
  };

  return (
    <>
      <div
        className={cn(
          'flex w-full',
          // if horizontal, add flex-row
          direction === 'horizontal' && ' flex-row justify-between items-center ',
          // if vertical, add flex-col
          direction === 'vertical' && 'flex-col',
          className
        )}
        data-cy="cy-select"
      >
        <label className="block  text-gray-700 font-semibold" htmlFor="selectOption">
          {label}
        </label>
        <div>
          <Field
            id={name}
            name={name}
            component={({ form, ...props }: FieldProps) => (
              <>
                <Select
                  styles={{ ...customStyles }}
                  menuPlacement="auto"
                  {...field}
                  {...props}
                  value={field.value}
                  options={options}
                  placeholder="Selecciona"
                  onChange={(newValue: unknown) => {
                    const option = newValue as { value: string; label: string };
                    form.setFieldValue(field.name, option);
                    if (onChange) {
                      onChange(option, form);
                    }
                  }}
                  onBlur={() => form.setFieldTouched(field.name, true)}
                  inputId="431"
                  isDisabled={disabled}
                  className={cn(
                    `
                  border-0
                  outline-none
                  h-[40px]
                  focus:!ring-transparent
                  focus:!border-transparent
                  ${hasError && 'border-[#E53E3E]'}
              `,
                    inputClassName
                  )}
                />
                {hasError && direction === 'horizontal' && (
                  <div className="mt-3 font-[Plus-Jakarta-Sans] text-[13px] font-bold text-[#E53E3E]">
                    {errorMessage?.value || meta.error}
                  </div>
                )}
              </>
            )}
          />
        </div>
        {hasError && direction === 'vertical' && (
          <div className="mt-3 font-[Plus-Jakarta-Sans] text-[13px] font-bold text-[#E53E3E]">
            {errorMessage?.value || meta.error}
          </div>
        )}
      </div>
    </>
  );
}
