'use client';
import { Box, Flex, Heading, Image, Text } from '@chakra-ui/react';
import heroCar from '@/assets/electricos/herocar.png';
import { handleSmoothScroll } from '@/app/us/(components)/USNav/USNav';
import ReserveNowBtnMX from '@/components/custom/ui/ReserveNowBtnMX';

export default function Hero() {
  return (
    <Flex
      // bgColor="#C3C3C3"
      // bgColor=""
      alignItems="center"
      h="100%"
      minH={{ base: '100%', l: 'calc(100vh - 90px)' }}
      w="100%"
      pt={{ base: '40px', md: '60px', xl: '60px' }}
      pb="60px"
      id="hero"
      flexDir="column"
      px={{ base: '0px', l: '150px' }}
    >
      <Flex
        w={{ base: '100%', xl: '75%' }}
        px={{ base: '10px', md: 0 }}
        h="max-content"
        flexDir="column"
        fontFamily="Plus-Jakarta-Sans"
        alignItems="center"
        textAlign="center"
        gap="25px"
      >
        <Heading
          fontFamily="Plus-Jakarta-Sans"
          fontSize={{ base: '28px', l: '42px' }}
          letterSpacing="3px"
          color="#5A00F8"
        >
          La manera más fácil de estrenar un auto eléctrico
        </Heading>
        <Text fontSize="18px" fontWeight={400}>
          Con el servicio todo incluido para que estrenes de la manera más rápida y segura
        </Text>
        <ReserveNowBtnMX
          className="w-[250px] md:w-[320px] h-[60px] text-[20px] text-[#FAFAFF] px-[20px] mt-[10px] font-[400] "
          onClick={(e) => {
            handleSmoothScroll(e, 'reservar');
          }}
        >
          Reserva Ahora
        </ReserveNowBtnMX>
        {/* <Button
          w={{ base: '200px', md: '250px' }}
          h={{ base: '40px', md: '50px' }}
          fontWeight={500}
          _hover={{
            bgColor: '#1A1A2A',
          }}
          fontSize={{ base: '16px', md: '20px' }}
          mt="10px"
          bgColor="#1A1A1A"
          className="bg-[#1A1A1A]"
          color="white"
          borderRadius="25px"
          letterSpacing="2px"
          onClick={(e) => handleSmoothScroll(e, 'reservar')}
        >
          RESERVA AHORA
        </Button> */}
      </Flex>
      <Box
        w={{ base: '85', xl: '75%' }}
        mt="80px"
        mb={{ base: '40px', lg: '30px' }}
        overflow={{ base: 'hidden', l: 'visible' }}
      >
        <Image ml={{ base: '10px', md: '30px' }} src={heroCar.src} alt="car img" />
      </Box>
      <Text mt={{ base: '15px', lg: '20px' }}>* Disponible en Noviembre 2023</Text>
    </Flex>
  );
}
