/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable @typescript-eslint/no-shadow */
import React, { Component } from 'react';
import M from 'materialize-css';
import { Flex, Box, Text, Card, CardHeader, Heading, Avatar, Icon } from '@chakra-ui/react';
import { AiFillStar } from 'react-icons/ai';
import 'materialize-css/dist/css/materialize.min.css';

interface TestimonialProps {
  name: string;
  car: string;
  description: string;
  image: string;
  stars: number[];
}

const testimonials: TestimonialProps[] = [
  {
    name: '<PERSON>',
    car: 'Chevrolet Aveo L',
    image: 'https://bit.ly/sage-adebayo',
    description:
      'Dentro de la cuota mensual está incluido el mantenimiento, el seguro... ¡Todo! Y eso me resulta muy cómodo, me despreocupo de las gestiones del coche y me limito a disfrutarlo.',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: '<PERSON>',
    car: 'Nissan Kicks',
    image: 'https://bit.ly/sage-adebayo',
    description:
      'Realizo muchos kilómetros y el renting me resulta más rentable a final del año. Estoy encantado con mi coche y, por ahora, las gestiones con OCN han ido genial.',
    stars: [1, 1, 1, 1],
  },
  {
    name: 'Pablo',
    car: 'Kia Seltos',
    image: 'https://bit.ly/sage-adebayo',
    description:
      'Dentro de la cuota mensual está incluido el mantenimiento, el seguro... ¡Todo! Y eso me resulta muy cómodo, me despreocupo de las gestiones del coche y me limito a disfrutarlo.',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: 'Eduardo',
    car: 'Nissan Sentra',
    image: 'https://bit.ly/sage-adebayo',
    description:
      'Realizo muchos kilómetros y el renting me resulta más rentable a final del año. Estoy encantado con mi coche y, por ahora, las gestiones con OCN han ido genial.',
    stars: [1, 1, 1, 1, 1],
  },
  {
    name: 'Pedro',
    car: 'Honda Civic',
    image: 'https://bit.ly/sage-adebayo',
    description:
      'La entrega del coche ha sido la acordada, incluso 2 semanas antes. Estoy muy contenta con OCN, ya que me ha dado la oportunidad de disfrutar mi coche. .',
    stars: [1, 1, 1, 1],
  },
];

class Carousel extends Component {
  Carousel: any;

  state = {
    testimonials,
  };

  numStars = [];

  /*  constructor(props: readonly){
         super(props)

         for(let i = 0; i < this.state.testimonials.length; i++ ){

         }
     } */

  componentDidMount() {
    const options = {
      duration: 300,
      dist: -200,
      shift: 10,
      indicators: true,
      /* onCycleTo: () => {
              console.log("New Slide");
            } */
    };
    /* console.log(this.state.testimonials) */
    M.Carousel.init(this.Carousel, options);

    // Instance Plugin
    /* let instance = M.Carousel.getInstance(this.Carousel);
        setInterval(() => {
          instance.next();
        }, 10000); */
  }

  render() {
    return (
      <Box
        ref={(Carousel) => {
          this.Carousel = Carousel;
        }}
        className="carousel"
      >
        {this.state.testimonials.map((e, i) => {
          return (
            <Card
              key={i}
              className="carousel-item"
              px={10}
              userSelect="none"
              borderRadius={{ base: '20px', md: '35px' }}
              css={{ opacity: 1 }}
            >
              <CardHeader w="100%" pt={0}>
                <Flex w="100%">
                  <Flex
                    w="100%"
                    flex="1"
                    flexDir="row-reverse"
                    justifyContent="space-between"
                    sx={{ '> *:not(:last-child)': { mr: 4 } }}
                    alignItems="center"
                    flexWrap="wrap"
                  >
                    <Avatar w={{ base: '70px', md: '80px' }} h={{ base: '70px', md: '80px' }} src={e.image} />

                    <Box>
                      <Heading size="sm" color="#742BFA" fontSize="20px" fontWeight={700} mb={1}>
                        {e.name}
                      </Heading>
                      <Text color="#9E8EFF" fontWeight={400}>
                        {e.car}
                      </Text>
                    </Box>
                  </Flex>
                </Flex>
              </CardHeader>
              <Flex flexDir="column" sx={{ '> *:not(:last-child)': { mb: 5 } }}>
                <Text textAlign="justify">{e.description}</Text>
                <Box>
                  {e.stars.map((e, i) => {
                    return <Icon key={i} as={AiFillStar} w="20px" h="20px" color="#FFB422" />;
                  })}
                </Box>
              </Flex>
            </Card>
          );
        })}
      </Box>
    );
  }
}

export default Carousel;
