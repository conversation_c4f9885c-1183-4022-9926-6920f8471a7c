/* eslint-disable prettier/prettier */
import React from 'react';
import { DocumentData } from '@firebase/firestore';
import CarCard from '../CarCard';

interface CarGridProps {
  plan: DocumentData[];
  planOptions?: any;
}

const CarGrid = ({ plan, planOptions }: CarGridProps) => {
  // Determinar las clases de grid basado en la cantidad de elementos
  const getGridClasses = () => {
    if (plan.length <= 2) {
      return `
        h-max-content
        w-full
        mt-[70px]
        flex
        flex-wrap
        justify-center
        gap-6
        [&>*]:mb-5
        md:[&>*]:mb-10
      `;
    }
    return `
      h-max-content
      w-full
      mt-[70px]
      grid
      grid-cols-1
      md:grid-cols-2
      cards:grid-cols-3
      justify-center
      [&>*]:mb-5
      md:[&>*]:mb-10
      cards:[&>*]:mb-10
    `;
  };

  return (
    <div className={getGridClasses()}>
      {plan.map((e, i) =>
        planOptions ? (
          <CarCard
            key={i}
            title={e.name}
            image={e.images[0]}
            price={e.payment}
            liters={e.liters}
            seats={e.seats}
            transmission={e.transmission}
            plan={e.plan}
            id={e.id}
            cardBtnColor="#742BFA"
            hoverColor="#923AFB"
            biweeklyPay={e.plan.toLowerCase() === 'personal' && e.paymentOptions[0].biweeklyPay}
          />
        ) : (
          <CarCard
            key={i}
            title={e.name}
            image={e.images[0]}
            price={e.payment}
            liters={e.liters}
            seats={e.seats}
            transmission={e.transmission}
            plan={e.plan}
            id={e.id}
              cardBtnColor="#742BFA"
            hoverColor="#923AFB"
          />
        )
      )}
    </div>
  );
};

export default CarGrid;
