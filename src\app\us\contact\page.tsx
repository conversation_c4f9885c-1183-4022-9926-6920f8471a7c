'use client';
/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Flex, Heading, Image, Spinner, Text } from '@chakra-ui/react';
import React, { Dispatch, useEffect, useState } from 'react';
import CustomButton from '@/components/CustomButton';
import blob from '@/assets/contact/blob.png';
import { Formik } from 'formik';
import Swal from 'sweetalert2';
import axios from 'axios';
import CustomField from '@/components/Form/FieldForm';
import { dealershipContactUSSchema } from '@/schemas/dealershipContactSchema';
import SelectInputUS from '../(components)/Inputs/SelectInputUS';
import {
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  US_DEFAULT_STATE_OPTIONS,
  US_STATES_DEFAULT_CITIES,
  USSTATES,
} from '../[nameCar]/data/data';

// const Contact = () => {
export default function ContactUsPage() {
  const [sending, setSending] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Box
        w="100%"
        h="100%"
        minH="100vh"
        display="grid"
        gridTemplateColumns={{ base: '100%', l: '60fr 40fr' }}
        position="relative"
        justifyContent="center"
        alignItems="center"
      >
        {sending ? (
          <>
            <Flex
              w="100%"
              h="100%"
              justifyContent="center"
              alignItems="center"
              position="absolute"
              zIndex={3}
            >
              <>
                <Spinner
                  thickness="15px"
                  speed="0.65s"
                  emptyColor="gray.200"
                  color="gray.400"
                  w="200px"
                  h="200px"
                />
              </>
            </Flex>
          </>
        ) : null}
        <Flex
          w="100%"
          h="100%"
          bgColor="purple.soft"
          display={{ base: 'none', l: 'flex' }}
          flexDir="column"
          px="7%"
          pt="14%"
          sx={{ '> *:not(:last-child)': { mb: 5 } }}
          color="white"
          overflow="hidden"
          position="relative"
          fontFamily="Plus-Jakarta-Sans"
        >
          <Heading fontFamily="Plus-Jakarta-Sans" className="!text-[50px]">
            Referral Program
          </Heading>
          <Text className="!text-[25px]">Earn $100 USD for every driver you refer who joins OCN.</Text>
          <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-329px"
            bottom="-22%"
          />
          <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-80px"
            bottom="-33%"
          />
        </Flex>
        <Flex
          w={{ base: '100%', l: '100%' }}
          h="100%"
          justifyContent="center"
          alignItems={{ base: 'start', l: 'center' }}
          bgColor="bgColor"
          py="50px"
          position="relative"
        >
          <Image
            w="12rem"
            h="15rem"
            src={blob.src}
            position="absolute"
            right={0}
            top={'-4rem'}
            zIndex={10}
            display={{ l: 'none' }}
          />
          <Flex
            w={{ base: '90%', l: '70%' }}
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: '25px', l: '30px' } } }}
          >
            <Text color="text.main" fontSize="30px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
              Contact Us
            </Text>
            <FormContact2 sending={sending} setSending={setSending} />
          </Flex>
        </Flex>
      </Box>
    </>
  );
}

interface MyFormValues {
  referrerName: string;
  referrerPhone: string;
  referrerEmail: string;
  name: string;
  phone: string;
  email: string;
  country: string;
  state: string;
  city: string;
}

function FormContact2({
  sending,
  setSending,
}: {
  sending: boolean;
  setSending: Dispatch<React.SetStateAction<boolean>>;
}) {
  async function sendToHubspot(form: MyFormValues) {
    const url = '/api/hubspot/createContact';
    const data = {
      nombre_de_quien_refiere: form.referrerName,
      telefono_de_quien_refiere: form.referrerPhone,
      email_de_quien_refiere: form.referrerEmail,
      city: form.city,
      state: form.state,
      phone: form.phone,
      firstname: form.name,
      email: form.email,
      country: 'us',
      source: 'Agencia',
      fuente: 'Agencia USA',
      isEdit: true,
    };
    const response = await axios.post(`${url}`, data);
    return response;
  }

  return (
    <Formik
      initialValues={{
        referrerName: '',
        referrerPhone: '',
        referrerEmail: '',
        state: US_DEFAULT_STATE_OPTIONS[0].value,
        city: US_STATES_DEFAULT_CITIES[USSTATES.Florida][0].value,
        agencyName: '',
        name: '',
        phone: '',
        email: '',
        country: 'us',
        // car: 'Formulario desde apartado de contacto',
      }}
      validationSchema={dealershipContactUSSchema}
      onSubmit={async (values, { resetForm }) => {
        try {
          setSending(true);

          await sendToHubspot(values);

          Swal.fire({
            title: 'Información enviada',
            text: 'Te contactáremos lo más pronto posible',
            icon: 'success',
            confirmButtonText: 'Cerrar',
          });
          setSending(false);
          resetForm();
        } catch (e: any) {
          setSending(false);
          resetForm();

          return await Swal.fire({
            title: 'El referido ya se encuentra en registrado',
            // text: e.response.data.message,
            icon: 'info',
            confirmButtonText: 'Cerrar',
          });
        } finally {
        }
      }}
    >
      {({ errors, touched, handleSubmit, setFieldValue, values }) => (
        <form
          onSubmit={handleSubmit}
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Flex
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: 3, l: 5 } } }}
            w={{ base: '90%', l: '100%' }}
            alignItems="center"
            gap={2}
            fontFamily="Plus-Jakarta-Sans"
          >
            <CustomField
              name="referrerName"
              label="Referrer Name"
              touched={touched}
              errors={errors}
              type="text"
              placeholder="Full Name (Who Refers)..."
            />
            <CustomField
              name="referrerPhone"
              label="Referrer Phone Number"
              touched={touched}
              errors={errors}
              type="number"
              placeholder="Phone Number (Who Refers)..."
            />

            <CustomField
              name="referrerEmail"
              label="Referrer Email"
              touched={touched}
              errors={errors}
              type="text"
              placeholder="Email (Who Refers)..."
            />

            <SelectInputUS
              name="state"
              label="Choose the state"
              errors={errors}
              touched={touched}
              className="w-full"
              inputClassName="!w-full"
              options={getUSStatesOptions()}
              onChange={(e) => {
                const selectedState = e.target.value;
                const city = getUSCitiesBasedOnState(selectedState)[0].value;
                setFieldValue('state', selectedState);
                setFieldValue('city', city);
              }}
            />

            {/* <SelectInput label="City" name="city" options={cityLeadOptions} /> */}
            <SelectInputUS
              name="city"
              // label="Choose Your City"
              label="Choose the city"
              errors={errors as any}
              touched={touched as any}
              inputClassName="!w-full"
              options={values.state ? getUSCitiesBasedOnState(values.state) : []}
              onChange={(e) => {
                setFieldValue('sourceOption', e.target.value);
              }}
            />

            <CustomField
              name="name"
              // label="Nombre Completo del referido"
              label="Full Name of the Referral"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="text"
              placeholder="Full Name of the Referral..."
            />

            <CustomField
              name="phone"
              // label="Numero de Teléfono del referido"
              label="Referral Phone Number"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="number"
              // placeholder="Numero teléfonico"
              placeholder="Referral Phone Number..."
            />

            <CustomField
              name="email"
              // label="Correo electrónico del referido"
              label="Referral Email"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="email"
              placeholder="<EMAIL>"
            />

            <CustomButton
              /* message="Enviar" */
              message="Submit"
              className="btn-purple"
              type="submit"
              disabled={!!sending}
            />
          </Flex>
        </form>
      )}
    </Formik>
  );
}
