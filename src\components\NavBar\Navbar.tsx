'use client';
/* eslint-disable prettier/prettier */
// /* eslint-disable @typescript-eslint/no-unused-vars */
// /* eslint-disable no-unused-vars */
import { usePathname } from 'next/navigation';
import { ChevronRightIcon } from '@chakra-ui/icons';
import OCNLogo from '@/assets/nav-us-logo.webp';
import Link from 'next/link';
import MobileNav from './MobileNav';
import { COUNTRY_DATA } from '@/constants';
import Image from 'next/image';


const Navbar = ({ /* country */ }: { country?: string }) => {
  const pathname = usePathname();

  return (
    <nav className="bg-white px-4 w-full h-[64px] sticky top-0 z-20">
      <div className="h-full flex items-center w-full justify-between">
        <Link href="/mx">
          <div className="w-[73px] h-[30px] md:ml-[30px] lg:ml-[50px]">
            <Image src={OCNLogo} width={73} height={30} alt="logo" className="w-full h-full" priority />
          </div>
        </Link>

        <div className="h-full">
          <ul className="hidden l:flex h-full items-center space-x-4 lg:space-x-6 ">
            <li
              className={`
                font-[Satoshi] min-w-[120px] text-center transition-all duration-500
                ${pathname === '/mx/plan/plataforma'
                  ? 'font-bold text-purple-soft text-[15px]'
                  : 'font-medium text-description text-[13px]'
                } leading-[17.55px]
                hover:text-purple-strong hover:font-bold hover:text-[15px]
              `}
            >
              <Link
                prefetch={false}
                href="/mx/plan/plataforma"

              >
                {COUNTRY_DATA.MX.navigator.platform}
              </Link>
            </li>

            <li
              className={`
                font-satoshi min-w-[80px] text-center transition-all duration-500
                ${pathname === 'https://blog.onecarnow.com/'
                  ? 'font-bold text-purple-soft text-[15px]'
                  : 'font-medium text-description text-[13px]'
                } leading-[17.55px]
                hover:text-purple-strong hover:font-bold hover:text-[15px]
              `}
            >
              <Link
                prefetch={false}
                href="https://blog.onecarnow.com/"

              >
                {COUNTRY_DATA.MX.navigator.blog}
              </Link>
            </li>

            <li
              className={`
                font-[Satoshi] min-w-[80px] text-center transition-all duration-500
                ${pathname === '/mx/contacto'
                  ? 'font-bold text-purple-soft text-[15px]'
                  : 'font-medium text-description text-[13px]'
                } leading-[17.55px]
                hover:text-purple-strong hover:font-bold hover:text-[15px]
              `}
            >
              <Link
                prefetch={false}
                href="/mx/contacto"

              >
                {COUNTRY_DATA.MX.navigator.contact}
              </Link>
            </li>
            <li>
              <button
                className=" 
              h-full
              text-purple-strong
              font-[Manrope] 
              transition-all 
              duration-500 
              text-[13px] 
              bg-transparent 
              font-semibold 
              leading-[17.55px]
              hover:text-[15px] 
              flex items-center 
              gap-1
            "
              >
              {COUNTRY_DATA.MX.navigator.platform}
                <ChevronRightIcon width="19px" height="19px" />
              </button>
            </li>
          </ul>
        </div>
        <MobileNav />
      </div>
    </nav>
  );
};

export default Navbar;
