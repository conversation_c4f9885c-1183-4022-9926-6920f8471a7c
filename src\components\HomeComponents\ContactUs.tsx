import Image from 'next/image';

export default function ContactUs() {
  return (
    <div
      className="
      w-full 
      h-full xl:h-[550px]
      py-0 md:py-[50px] lg:py-0
      min-h-[80vh] md:min-h-fit
      flex items-center 
      bg-bgColor 
      justify-center
    "
    >
      <div
        className="
        w-[80%] 
        h-max-content 
        flex 
        flex-col lg:flex-row 
        pb-0 l:pb-[30px] lg:pb-0
      "
      >
        <div
          className="
          w-full lg:w-[45%]
          flex 
          flex-col 
          [&>*:not(:last-child)]:mb-10
          text-text-main
          items-center md:items-start
          text-center md:text-start
        "
        >
          <div>
            <h1
              className="
              font-[Plus-Jakarta-Sans]
              font-extrabold
              text-[40px]
              leading-[60px]
            "
            >
              Estrena un auto nuevo con
              <span className="text-purple-strong"> OCN</span>
            </h1>
          </div>

          <p
            className="
            font-[Plus-Jakarta-Sans]
            font-medium
            leading-[30px]
            w-full md:w-[80%]
          "
          >
            Te contamos todo sobre el renting, dejanos tus datos y nos pondremos en contacto contigo.
          </p>
        </div>

        {/* <div
          className="
          w-full lg:w-[55%]
          h-[70%]
          min-h-[300px] l:min-h-[400px] lg:min-h-[300px]
          flex
          justify-end
          items-center
          bg-[url('https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2FOCNautos.png?alt=media&token=57d8b859-83eb-4839-8122-d887854da261')]
          bg-no-repeat
          bg-center
          bg-contain l:bg-cover
        "
        /> */}

        <div
          className="
            w-full lg:w-[55%]
            h-[70%]
            min-h-[300px] l:min-h-[400px] lg:min-h-[300px]
            flex
            justify-end
            items-center
            relative
          "
        >
          <Image
            src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2FOCNautos.png?alt=media&token=57d8b859-83eb-4839-8122-d887854da261"
            alt="OCN Autos"
            fill
            sizes="(max-width: 1024px) 100vw, 55vw"
            className="
              object-contain l:object-cover
              object-center
            "
            loading="lazy"
          />
        </div>
      </div>
    </div>
  );
}
