/* eslint-disable consistent-return */
import { FormControl, FormLabel, Input, Select } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikValues } from 'formik';
import React from 'react';

interface InputProps {
  label: string;
  placeholder: string;
  name: string;
  errors: string | undefined;
  touched: any;
  type: 'text' | 'number';
}

interface InputSelectProps {
  label: string;
  name: string;
  errors: string | undefined;
  touched: any;
  options: string[];
}

export const InputElectricBasic = ({ name, label, errors, touched, placeholder, type }: InputProps) => {
  return (
    <Field name={name}>
      {({ field }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl w={{ base: '100%', md: '40%' }} isInvalid={errors && touched}>
          <FormLabel color="#E5E5E5" _focus={{ color: '#5A00F8' }} fontSize="16px">
            {' '}
            {label}{' '}
          </FormLabel>
          <Input
            type={type}
            focusBorderColor="#5A00F8"
            bgColor="#E5E5E5"
            opacity="0.6000000238418579"
            borderRadius={5}
            onKeyDown={(event: any) => {
              if (type === 'number') {
                if (['e', 'E', '+', '-', '.'].includes(event.key)) return event.preventDefault();
                return null;
              }
            }}
            {...field}
            placeholder={placeholder}
          />
        </FormControl>
      )}
    </Field>
  );
};

interface InputSelectProps2 {
  label: string;
  name: string;
  errors: string | undefined;
  touched: any;
  options: {
    label: string;
    value: string;
  }[];
}

export const InputElectricSelect2 = ({ name, label, errors, touched, options }: InputSelectProps2) => {
  return (
    <Field name={name}>
      {({ field }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl w={{ base: '100%', md: '40%' }} isInvalid={errors && touched}>
          <FormLabel color="#E5E5E5" fontSize="16px" _focus={{ color: '#5A00F8' }}>
            {' '}
            {label}{' '}
          </FormLabel>
          <Select
            placeholder="Seleccione..."
            focusBorderColor="#5A00F8"
            bgColor="#E5E5E5"
            opacity="0.6000000238418579"
            borderRadius={5}
            {...field}
          >
            {options.map((option, i) => {
              return (
                <option key={i} value={option.value}>
                  {option.label}
                </option>
              );
            })}
          </Select>
        </FormControl>
      )}
    </Field>
  );
};

export const InputElectricSelect = ({ name, label, errors, touched, options }: InputSelectProps) => {
  return (
    <Field name={name}>
      {({ field }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl w={{ base: '100%', md: '40%' }} isInvalid={errors && touched}>
          <FormLabel color="#E5E5E5" fontSize="16px" _focus={{ color: '#5A00F8' }}>
            {' '}
            {label}{' '}
          </FormLabel>
          <Select
            placeholder="Seleccione..."
            focusBorderColor="#5A00F8"
            bgColor="#E5E5E5"
            opacity="0.6000000238418579"
            borderRadius={5}
            {...field}
          >
            {options.map((option, i) => {
              return (
                <option key={i} value={option}>
                  {option}
                </option>
              );
            })}
          </Select>
        </FormControl>
      )}
    </Field>
  );
};
