import { <PERSON><PERSON>ventHand<PERSON> } from 'react';
import './CustomButton.css';

interface ButtonProps {
  message: string;
  handleClick?: MouseEventHandler<HTMLButtonElement>;
  className: string;
  type?: 'submit' | 'button' | 'reset' | undefined;
  disabled?: boolean;
  'aria-label'?: string;
  children?: React.ReactNode;
}

export default function CustomButton({
  message,
  children,
  handleClick,
  className,
  type = 'button',
  disabled,
  'aria-label': ariaLabel,
}: ButtonProps) {
  return (
    <button
      onClick={handleClick}
      type={type}
      className={`btn ${className}`}
      disabled={disabled}
      aria-label={ariaLabel || message}
    >
      {/* <span className="btn-text">{message}</span> */}
      {children || message}
    </button>
  );
}
