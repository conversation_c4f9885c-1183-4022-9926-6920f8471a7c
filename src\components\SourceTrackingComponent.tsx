'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export const SourceTrackingComponent = ({ source }: { source?: string }) => {
  const searchParams = useSearchParams();
  const sourceParam = searchParams.get('source');

  useEffect(() => {
    if (source) {
      localStorage.setItem('source', source);
    } else if (sourceParam) {
      localStorage.setItem('source', sourceParam);
    }
  }, [source, sourceParam]);

  return <span />;
};
