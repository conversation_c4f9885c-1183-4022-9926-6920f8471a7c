'use client';
import { useEffect, useState } from 'react';
import USCarrouselDetail from './CarrouselImg';
// import { Image } from '@chakra-ui/react';

import Image, { StaticImageData } from 'next/image';
import SimpleSlider from './Slider';
import useHandleResize from '@/components/useHandleResize';

interface ImagesProps {
  images: StaticImageData[];
}

export default function Images({ images }: ImagesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [initialSlide, setInitialSlide] = useState(0);
  const isMobile = useHandleResize({ breakpoint: 1350 });

  const firstFiveImages = images.slice(1, 4);

  // useEffect(() => {
  //   window.scrollTo({ top: 0, left: 0 });
  // }, []);

  const toggleModal = () => {
    setIsOpen(!isOpen);
  };

  const setSlider = (index: number) => {
    setInitialSlide(index);
  };

  useEffect(() => {
    if (isMobile) {
      setIsOpen(false);
    }
  }, [isMobile]);

  return (
    <div className="w-full h-[max-content] 2xl:min-h-[450px] px-6 md:px-10 xl:px-[75px] pt-[30px] sm:pt-[45px] ">
      <div className="w-full h-[max-content] block lg:hidden ">
        <SimpleSlider images={images} />
      </div>
      <div className="hidden lg:grid grid-cols-2 gap-[16px] justify-center items-center">
        <div className="w-full ">
          <Image
            width={1000}
            height={1000}
            src={images[0]}
            alt="img-1"
            onClick={() => {
              toggleModal();
              setSlider(0);
            }}
            className="w-full h-full rounded-[10px] object-contain bg-no-repeat cursor-pointer "
          />
        </div>
        <div className="hidden lg:grid grid-cols-2 gap-2 justify-center items-center ">
          {firstFiveImages.map((image, i) => (
            <Image
              key={i}
              width={1000}
              height={1000}
              src={image}
              alt={`img-${i}`}
              onClick={() => {
                toggleModal();
                setSlider(i + 1);
              }}
              className={`
                w-full
                flex
                cursor-pointer
                rounded-[10px] 
                object-contain 
              `}
            />
          ))}
          <div className="relative h-full">
            <Image
              width={1000}
              height={1000}
              src={images[4]}
              alt={'img-4'}
              className={`
                w-full h-full
                flex
                cursor-pointer
                rounded-[10px] 
                object-cover
                bg-[linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7))]
              `}
            />
            <div
              className="absolute inset-0 z-[1] cursor-pointer bg-black opacity-30 rounded-[10px] items-center "
              onClick={() => {
                toggleModal();
                setSlider(4);
              }}
            />
            <div className="absolute inset-0 cursor-pointer text-white flex justify-center items-center text-[40px] ">
              +{images.length - 5}
            </div>
          </div>
        </div>
      </div>

      {isOpen && <USCarrouselDetail images={images} toggleModal={toggleModal} initialSlide={initialSlide} />}
    </div>
  );
}
