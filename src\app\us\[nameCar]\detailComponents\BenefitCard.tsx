/* eslint-disable prettier/prettier */
import { AccordionButton, AccordionIcon, AccordionItem, AccordionPanel } from '@chakra-ui/react';
import React from 'react';

const BenefitCard = ({
  title,
  description,
  // indexOpen, // Ya no es necesario
  // toggleOpen, // Ya no es necesario
}: {
  title: string;
  description: string;
    // indexOpen: boolean; // Ya no es necesario
    // toggleOpen: (index: any) => void; // Ya no es necesario
}) => {
  return (
    <div
      className="
        flex 
        w-full h-[max-content]
        pr-[20px]
        mb-[30px]
        rounded-[15px]
        items-center
        justify-between  
      "
    >
      <AccordionItem w="100%" border="none" fontFamily="Plus-Jakarta-Sans" color="text.main">
        {() => (
          <>
            <AccordionButton
              w="100%"
              h="100%"
              minH="50px"
              _hover={{ bgColor: 'transparent', borderRadius: '30px' }}
              sx={{ '> *:not(:last-child)': { mr: { base: 2, md: 0 } } }}
              px={{ base: 'none', md: 5 }}
              // onClick={toggleOpen} // Ya no es necesario
            >
              <span
                className="flex-1 text-purple-strong text-[15px] md:text-[16px] font-[600] text-left"
              >
                {title}
              </span>

              <div className="rounded-[50%]">
                <AccordionIcon boxSize={'30px'} color="purple.strong" />
              </div>
            </AccordionButton>

            <AccordionPanel>
              <ul className="list-disc ml-2">
                <li>{description}</li>
              </ul>
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    </div>
  );
};

export default BenefitCard;
