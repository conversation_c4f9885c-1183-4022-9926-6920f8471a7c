/* RIGHT */
import { MouseEvent, ReactNode } from 'react';
// import './ReserveNowBtn.css';
import { LuLoader2 } from 'react-icons/lu';

interface ReserverBtnProps {
  full?: boolean;
  size?: 'sm' | 'md' | 'lg';
  children?: ReactNode;
  onClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  type?: 'submit';
  isSubmitting?: boolean;
  className?: string;
}

const sizes = {
  sm: '40px',
  md: '50px',
  lg: '60px',
};

export default function ReserveNowBtnMX({
  full,
  size = 'md',
  children = 'Reserve Now',
  onClick,
  type,
  isSubmitting,
  className,
}: ReserverBtnProps) {
  return (
    <button
      type={type && type}
      className={`
        // ${full ? 'w-full' : ''}
        h-[${sizes[size]}]
        py-2 px-2
        min-h-[${sizes[size]}]
        text-[16px]
        font-[Plus-Jakarta-Sans]
        font-bold
        text-white rounded-[10px]
        reserve-btn
        flex justify-center items-center
        ${className}
        `}
      onClick={(e) => {
        if (onClick) {
          onClick(e);
        }
      }}
      disabled={isSubmitting}
    >
      {isSubmitting ? <LuLoader2 size={26} className=" animate-spin " /> : children}
    </button>
  );
}
