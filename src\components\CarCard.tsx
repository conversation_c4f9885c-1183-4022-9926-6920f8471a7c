/* eslint-disable prettier/prettier */
'use client';

import { useRouter } from 'next/navigation';
import LinkWrapper from './LinkWrapper';
import { FaGasPump } from 'react-icons/fa';
import { useEffect, useState } from 'react';
import { GiCarSeat, GiLever } from 'react-icons/gi';
import Image from 'next/image';

interface CarCardProps {
  title: string;
  image: string;
  price: number;
  cardBtnColor: string;
  hoverColor: string;
  seats: string;
  transmission: string;
  plan: string;
  id: string;
  liters?: string;
  biweeklyPay?: number;
}

export default function CarCard({
  title,
  image,
  price,
  cardBtnColor,
  seats,
  liters,
  transmission,
  hoverColor,
  plan,
  id,
  biweeklyPay,
}: CarCardProps) {
  const router = useRouter();
  const [showLink, setShowLink] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setShowLink(window.innerWidth > 550);
    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex items-center justify-center w-full">
      <LinkWrapper link={`/mx/detalle/${plan?.toLowerCase()}/${id}`}>
        <div className="w-[320px] h-[348px] rounded-[10px] font-[Plus-Jakarta-Sans] p-[25px] shadow-[0px_12px_32px_rgba(195,198,255,0.2)] relative bg-white">
          {/* Title */}
          <h2 className="text-[20px] font-[700] text-text-main font-[Plus-Jakarta-Sans]">{title}</h2>

          {/* Image Container */}
          <div className="w-full flex justify-center items-center mt-[15px]">
            <div className="w-[230px] h-[160px] relative">
              <Image
                src={image}
                // src={"/mx/mg3/mg3-cool-miniatura.png"}
                alt={title || 'car'}
                layout="fill"
                objectFit="contain"
                objectPosition="center"
                className="transition-all duration-600 hover:scale-125"
                loading="lazy"
              />

            </div>
          </div>

          {/* Car Features */}
          <div className="flex justify-end text-[#586f90] text-[13px] mt-[5px]">
            <div className="flex items-center mr-3">
              <FaGasPump className="w-4 h-4 mr-1" />
              <p>{liters ? liters + ' L' : '1.6 L'}</p>
            </div>
            <div className="flex items-center mr-3">
              <GiLever className="w-4 h-4 mr-1" />
              <p>{transmission.split(' ')[0][0].toUpperCase() + transmission.split(' ')[0].slice(1)}</p>
            </div>
            <div className="flex items-center">
              <GiCarSeat className="w-4 h-4 mr-1" />
              <p>{seats} personas</p>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="absolute bottom-[15px] flex justify-between items-end w-[85%] text-primaryPurple ">
            <div className="flex flex-col" style={{ color: cardBtnColor }}>
              {plan.toLowerCase() === 'personal' ? (
                <>
                  <span className="text-[14px] font-[500]">Desde</span>
                  <div className="flex items-start">
                    <span className="text-[24px] font-[700]">${biweeklyPay}</span>
                    <span className="pt-[12px] font-[600] text-[11px] md:text-[12.5px]">- Quincenal</span>
                  </div>
                </>
              ) : (
                  <div className="flex items-start">
                    <span className="text-[24px] font-[700]">${price}</span>
                    <span className="pt-[12px] font-[600] text-[11px] md:text-[12.5px]">- Semanal</span>
                  </div>
              )}
            </div>

            <button
              onClick={() => {
                if (!showLink) {
                  router.push(`/mx/detalle/${plan?.toLowerCase()}/${id}`);
                }
              }}
              className={`w-[106px] h-[44px] rounded text-white transition-colors duration-200 hover:${hoverColor}" bg-[${cardBtnColor}] `}

              onMouseOver={(e) => {
                (e.target as HTMLButtonElement).style.backgroundColor = hoverColor;
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.backgroundColor = cardBtnColor;
              }}
            >
              Cotizar
            </button>
          </div>
        </div>
      </LinkWrapper>
    </div>
  );
}
