import ChakraProvider from '@/providers/chakraProvider';
import './globals.css';
import { Analytics } from '@vercel/analytics/react';
import Script from 'next/script';

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="es" suppressHydrationWarning={true}>
      <head>
        {process.env.NODE_ENV === 'production' && (
          <Script id="hotjar">
            {`
          <script async src="https://www.googletagmanager.com/gtag/js?id=AW-392601275">
          </script>
          <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'AW-392601275');
          </script>
        `}
          </Script>
        )}

        <meta name="robots" content="all" />
      </head>
      <body className="relative">
        <ChakraProvider>{children}</ChakraProvider>
        {process.env.NODE_ENV === 'production' && <Analytics />}
      </body>
    </html>
  );
}
