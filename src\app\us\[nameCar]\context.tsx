'use client';
import { createContext, useState, useContext } from 'react';

// Crea el contexto

interface ContextProps {
  form: {
    fullName: string;
    email: string;
    phone: string;
    vehicle: string;
  };
  setForm: (value: any) => void;
}

export const VehicleContext = createContext<ContextProps | null>(null);

export const useUsers = () => {
  const context = useContext(VehicleContext);
  if (!context) throw new Error('There is not vehicle detail context');
  return context;
};

interface ProviderProps {
  children: React.ReactNode;
}

// Proveedor del contexto
export default function VehicleProvider({ children }: ProviderProps) {
  // const [vehicleData, setVehicleData] = useState(data);
  const [form, setForm] = useState({
    fullName: '',
    email: '',
    phone: '',
    vehicle: '',
  });

  return <VehicleContext.Provider value={{ form, setForm }}>{children}</VehicleContext.Provider>;
}
