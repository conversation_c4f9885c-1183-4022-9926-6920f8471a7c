import { Flex, Heading, Text } from '@chakra-ui/react';
import React from 'react';

const WhatWeDo = () => {
  return (
    <Flex
      py={{ base: '60px', md: '120px' }}
      justify={'center'}
      align={{ base: 'start', l: 'center' }}
      flexDir={{ base: 'column', l: 'row' }}
      sx={{ '> *:not(:last-child)': { mb: { base: '10vw', l: 0 }, mr: { base: 0, l: '10vw' } } }}
    >
      <Heading fontSize={'32px'} color="text.description">
        ¿Qué hacemos?
      </Heading>

      <Text fontSize={'15px'} color="text.description" lineHeight="30px" w={{ base: '100%', l: '60%' }}>
        En OCN nos esforzamos en proporcionar una solución diferente e innovadora para poder estrenar un auto,
        una opción fácil y sencilla que te incluye todo menos la gasolina. <br />
        <br />
        Por eso creamos el primer servicio de suscripción de autos en México, permitiendo a nuestros clientes
        estrenar un auto nuevo a mediano o largo plazo, nos esforzamos todos los días en ser líderes de la
        industria, adaptandonos a las necesidades de nuestros clientes, ofreciendo soluciones flexibles y una
        experiencia completamente digital.
      </Text>
    </Flex>
  );
};

export default WhatWeDo;
