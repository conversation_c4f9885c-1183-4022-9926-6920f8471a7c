import React from 'react';
import { Divider, Flex, Grid, Heading, Image, Text } from '@chakra-ui/react';

const AboutHeader = () => {
  return (
    <>
      <Flex
        flexDir={'column'}
        sx={{ '> *:not(:last-child)': { mb: '26px' } }}
        w="100%"
        h="100%"
        alignItems="center"
        textAlign="center"
        mt="50px"
        mb="80px"
        color="text.description"
        fontFamily="Plus-Jakarta-Sans"
      >
        <Heading fontSize="30px" as={'h1'}>
          Sobre nosotros
        </Heading>

        <Divider w="42px" border={'2px solid #9E8EFF'} borderRadius="10px" />

        <Text w={{ base: '100%', md: '50%' }} fontWeight={500} as={'h3'}>
          Conoce quienes somos, cuál es nuestro origen, y que es lo que nos motiva a revolucionar la forma de
          estrenar un auto
        </Text>
      </Flex>

      <Grid w="100%" h="100%" gridTemplateColumns="49% 49%" justifyContent="space-between">
        <Image
          src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/about%2Focn-pic.jpg?alt=media&token=0f9aaea5-a75b-457d-b4f1-8e397d0f1507"
          alt={'about_people'}
          borderRadius={{ base: '1em', md: '2em' }}
          mr="30px"
          objectFit="contain"
        />

        <Image
          src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/about%2Focn-vehicle.jpg?alt=media&token=0460e492-8627-4da9-8055-7ce3e27290a8"
          alt={'about_carLogo'}
          borderRadius={{ base: '1em', md: '2em' }}
          objectFit="contain"
        />
      </Grid>
    </>
  );
};

export default AboutHeader;
