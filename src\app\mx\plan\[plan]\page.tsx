import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import PlansClient from './planClient';
// import { getPersonalCache, getPlatformCache } from '@/app/getters/getPlatforms';
import { get3TabPersonalVehicles } from '@/middlewares/getVehiclesFirebase';
import { platformVehiclesData } from '../../vehicles.data';

export default async function PlansPage({ params: { plan } }: { params: { plan: string } }) {
  const locationPersonal = plan === 'personal';
  // const platformVehicles = await getPlatformCache();
  // const personalVehicles = await getPersonalCache();
  const data = locationPersonal ? await get3TabPersonalVehicles() : platformVehiclesData;
  // const personalFirebase = await get3TabPersonalVehicles();
  // const platformVehicles = await get3TabPlatformVehicles();
  return (
    <>
      <Navbar country={'MX'} />
      <PlansClient personal={data} platform={data} isPersonal={locationPersonal} />
      <Footer footerBgColor={locationPersonal ? 'footer.personal' : 'footer.main'} />
    </>
  );
}
