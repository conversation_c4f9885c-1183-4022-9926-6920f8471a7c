'use client';
import { useEffect } from 'react';
import { Flex } from '@chakra-ui/react';
import { AboutHeader, AboutService, WhatWeDo, Mission, Values } from './AboutComponents';
import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';

const About = () => {
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Navbar country="mx" />

      <Flex
        justifyContent="center"
        as="main"
        w={'100%'}
        h="max-content"
        bgColor="bgColor"
        fontFamily="Plus-Jakarta-Sans"
        color="text.main"
        overflow="hidden"
      >
        <Flex flexDir="column" alignItems="center" w={{ base: '90%', md: '80%' }}>
          {/* Sobre nosotros */}
          <AboutHeader />

          <Flex w="100%" direction={'column'} sx={{ '> *:not(:last-child)': { mb: 3 } }}>
            {/* Que hacemos? */}
            <WhatWeDo />

            <Flex justify="center" w="100%">
              {/* Que hacemos? */}
              <AboutService />
            </Flex>

            {/* Nuestra mision */}
            <Mission />

            {/* Nuestros valores */}
            <Values />
          </Flex>
        </Flex>
      </Flex>

      <Footer footerBgColor="footer.main" />
    </>
  );
};

export default About;
