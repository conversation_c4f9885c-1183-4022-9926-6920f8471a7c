// import About from './components/About';
import ElectricNavbar from './components/ElectricNavbar';
import Hero from './components/Hero';
// import Vehicles from './components/Vehicles';
import logoOcn from '@/assets/electricos/logoOCN-white.png';
import Faq from './components/Faq';
import ElectricForm from './components/ElectricForm';
import Footer from '@/components/Footer';
import Vehicles2 from './components/Vehicles2';

export default function ElectricsPage() {
  return (
    <>
      <ElectricNavbar />
      <div>
        <Hero />
        {/* <Vehicles /> */}
        <Vehicles2 />
        {/* <About /> */}
        <Faq />
        {/* Este flex sería el componente del formulario con id="reservar" 
          para redireccionar con el boton de los vehiculos */}
        <div className="flex" id="reservar">
          <ElectricForm />
        </div>
      </div>
      <Footer footerBgColor="footer.electrics" logo={logoOcn.src} colorText="#E5E5E5" />
    </>
  );
}
