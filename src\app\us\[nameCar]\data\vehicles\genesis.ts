import genesisCard from '@/assets/home/<USER>/genesis_header.webp';
import genesis1 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_1.webp';
import genesis2 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_2.webp';
import genesis3 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_3.webp';
// import genesis4 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_4.gif';
import genesis5 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_5.webp';
// import genesis6 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_6.webp';
import genesis7 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_7.webp';
// import genesis8 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_8.webp';
import genesis9 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_9.webp';
import genesis10 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_10.webp';
import genesis11 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_11.webp';
// import genesis12 from '@/assets/home/<USER>/vehicleImages/genesis/genesis_12.webp';

import { IVehicleData } from '../data';

const genesisData: IVehicleData = {
  url: 'genesis-gv60',
  name: 'Genesis GV60 Long Range',
  value: 'genesis-gv60',
  cardName: 'Genesis GV60',
  cardImage: genesisCard.src,
  imageCar: genesisCard,
  description:
    'Genesis GV60 Long Range one of the most futuristic and tailor-made SUVs with several sporty and elegant features offering a spacious and comfortable cabin.',
  features: {
    range: '310 mi',
    acceleration: '7.2 s 0-60 mph',
    battery: '77.4 KW',
    engine: '225 hp RWD',
    charge: '7.5 hours',
    airbags: '8',
    fuel: 'Electric',
    doors: 5,
    airConditioner: true,
    seats: 5,
    distance: 430,
  },
  carPrice: 43950,
  payment: 549,
  comingSoon: true,
  payments: {
    1: 1200,
    2: 1000,
    3: 800,
    4: 600,
  },
  mainFeatures: {
    inside: [
      'The latest infotainment technology, including Apple CarPlay and Android Auto wireless connectivity.',
      'Soft vegan leather upholstery and heated front seats make for a cozy ride.',
      'BlueCruise system, which offers hands-free driving for thousands of miles of driving.',
    ],
    abroad: [
      'It features extra exterior styling with bolder wheels and LED lighting.',
      'Battery life is for 8 years or 100,000 miles, whichever comes first, retaining a minimum of 70% of its original capacity during that period.',
    ],
  },
  images: [
    genesis1,
    genesis2,
    genesis3,
    // genesis4,
    genesis5,
    // genesis6,
    genesis7,
    // genesis8,
    genesis9,
    genesis10,
    genesis11,
    // genesis12,
  ],
  costPerMile: 0.055,
  savePerMile: 0.099,
};

export default genesisData;
