/* eslint-disable prettier/prettier */
import ReserveNowBtn from './ReserveNowBtn';
import Image from 'next/image';
// import CarPriceCalculatorModal from './USHome/car-price-calculator-modal';
import Link from 'next/link';
import { IVehicleData } from '../[nameCar]/data/data';
// import { Button } from '@/components/ui/button';

interface VehicleCardProps extends IVehicleData {
  removeLink?: boolean;
  prefetch?: boolean;
}

export default function VehicleCard({ removeLink = false, prefetch = false, ...vehicle }: VehicleCardProps) {
  // const carData = {
  //   carModel: vehicle.name,
  //   carYear: '2025',
  //   carImage: vehicle.cardImage,
  //   carSpecs: {
  //     gearBox: vehicle.features.engine,
  //     fuel: vehicle.features.fuel,
  //     doors: vehicle.features.doors,
  //     airConditioner: vehicle.features.airConditioner,
  //     seats: vehicle.features.seats,
  //     distance: vehicle.features.distance,
  //   },
  //   carPrice: vehicle.carPrice,
  //   trigger: (
  //     <>
  //       <Button
  //         className="border-[#5A00F8] border-2 text-[#5A00F8] hover:text-[#5a0be3] font-semibold h-[50px]"
  //         variant="outline"
  //       >
  //         Lease Calculator
  //       </Button>
  //     </>
  //   ),
  // };

  return (
    <div
      className="
        flex flex-col
        w-full 
        max-w-[366px] 2xl:max-w-[396px]
        bg-[#FFF]
        rounded-[10px]
        font-[Plus-Jakarta-Sans]
        shadow-[0px_10px_30px_0px_rgba(195,198,255,0.20)]
        p-[30px]
      "
    >
      <ShowLinkOrNot url={`/us/${vehicle.url}`} removeLink={removeLink} prefetch={prefetch}>
        <div className="w-full h-[200px] mb-[10px] transition-all duration-300 ease-in-out hover:transform-[scale(1.15)]">
          <Image
            src={vehicle.cardImage}
            width={1000}
            height={1000}
            priority
            className="w-full h-full object-contain"
            alt="vehicle img"
          />
        </div>
        <div className="mb-[15px] flex flex-col 2xl:mt-[25px]">
          <p className="text-description font-bold text-[22px]">{vehicle.name}</p>
          {/* <p className="text-[16px] text-[#5A00F8] font-[600]">From ${vehicle.payment} / week</p> */}
          {vehicle.comingSoon ? (
            <p className="text-[16px] text-[#5A00F8] font-[600]">Coming Soon</p>
          ) : (
            <p className="text-[16px] text-[#5A00F8] font-[600]">From ${vehicle.payment} / week</p>
          )}
        </div>
      </ShowLinkOrNot>
      <div className="flex flex-col gap-2">
        <ReserveNowBtn full size={`md`} text={vehicle.comingSoon ? 'Register Interest' : 'Reserve Now'} />
        {/* <CarPriceCalculatorModal {...carData} /> */}
      </div>
    </div>
  );
}

function ShowLinkOrNot({
  children,
  url,
  removeLink = false,
  prefetch = false,
}: {
  children: any;
  url: string;
  removeLink?: boolean;
  prefetch?: boolean;
}) {
  return removeLink ? (
    <>{children}</>
  ) : (
    <Link href={url} prefetch={prefetch}>
      {children}
    </Link>
  );
}
