'use client';
import useHandleResize from '@/components/useHandleResize';
import { Select, Slider, SliderFilledTrack, SliderThumb, SliderTrack } from '@chakra-ui/react';

import Image, { StaticImageData } from 'next/image';
import { useEffect, useMemo, useState } from 'react';
import { vehiclesFormOptions } from '../data/data';

interface CalculatorProps {
  carName: string;
  imageCar: StaticImageData;
}

export default function Calculator({ carName, imageCar }: CalculatorProps) {
  const [perdiod, setPeriod] = useState(1);
  const [miles, setMiles] = useState(250);
  const [costSelected, setCostSelected] = useState(0);
  const isMobile = useHandleResize({ breakpoint: 1280 });
  const [first, setFirst] = useState(vehiclesFormOptions.find((el) => el.label === carName));

  const rest = vehiclesFormOptions.filter((el) => el.label !== first?.label);
  useEffect(() => {
    const price = first?.costPerMi as number;
    setCostSelected(price);
  }, [carName, first?.costPerMi]);

  const cost = useMemo(() => {
    const period = perdiod === 1 ? 1 : 4;
    const totalDays = period === 1 ? 7 : 30;
    return (miles * costSelected * totalDays).toFixed(2);
  }, [miles, costSelected, perdiod]);

  const savings = useMemo(() => {
    // const totalCost = miles * costSelected; // this means
    // const gas = miles * 0.1286;
    // console.log('miles', miles);
    // console.log('costSelected', costSelected);
    // console.log('savePerMi', first!.savePerMi, first!.savePerMi * miles);
    // console.log('period', perdiod);
    // const savingsResukt = (miles * first!.savePerMi) as number;
    const period = perdiod === 1 ? 1 : 4;

    const totalDays = period === 1 ? 7 : 30;
    // return ((savingsResukt - totalCost) * totalDays).toFixed(2);
    return (first!.savePerMi * miles * totalDays).toFixed(2);
  }, [miles, perdiod, first]);

  return (
    <div className="px-6 md:px-10 xl:px-[75px] grid min-h-[730px] py-[50px] xl:py-0 ">
      <p className="text-[44px] text-[#742BFA] mb-[90px] items-center text-center font-bold ">
        Savings per charge
      </p>
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 justify-between">
        <div className="flex flex-col justify-center  gap-[40px] mb-[30px] xl:mb-0 ">
          <div className="flex w-full text-[20px] mb-[30px] justify-center xl:justify-start ">
            <button
              onClick={() => setPeriod(1)}
              className={`
                w-full max-w-[310px] h-[60px] 
                rounded-l-[8px]
                ${
                  perdiod === 1
                    ? 'reserve-btn text-white '
                    : 'border-y-2 border-l-2 border-[#742BFA] text-[#742BFA]'
                }
                `}
            >
              Week
            </button>
            <button
              onClick={() => setPeriod(2)}
              className={`
                w-full max-w-[310px] h-[60px] 
                rounded-r-[8px] 
                ${
                  perdiod === 2
                    ? 'reserve-btn text-white '
                    : 'border-y-2 border-r-2 border-[#742BFA] text-[#742BFA]'
                }
              `}
            >
              Monthly
            </button>
          </div>

          <div className="w-full flex flex-col md:flex-row gap-4 ">
            <div className="flex flex-col gap-1">
              <p className="text-[#464E5F] text-[20px] ">Choose your EV</p>
              <Select
                name="vehicle"
                _focus={{
                  borderColor: '#742BFA',
                  borderWidth: '1px',
                }}
                minW="320px"
                borderWidth="2px"
                bgColor="white"
                w={{ base: '100%', md: '320px' }}
                className="h-[55px] "
                defaultValue={first?.value}
                onChange={(e) => {
                  // setCostSelected(e.target.value);
                  const vehicle = vehiclesFormOptions.find((v) => v.value === e.target.value); /* as {
                    value: string;
                    label: string;
                    costPerMi: number;
                  } */
                  setCostSelected(vehicle!.costPerMi);
                  setFirst(vehicle);
                }}
              >
                <option value={first?.value}>{first?.label}</option>
                {rest?.map((el) => (
                  <option key={el.label} value={el.value}>
                    {el.label}
                  </option>
                ))}
              </Select>
            </div>
            <div className="flex flex-col gap-1">
              <p className="text-[#464E5F] text-[20px]">Miles drive daily</p>
              <input
                value={miles}
                readOnly
                type="text"
                className=" h-[55px] w-full xl:max-w-[238px] border-[2px] border-[#742BFA] rounded px-2 select-none focus:outline-[#742BFA] "
              />
            </div>
          </div>
          <div className="flex gap-3 justify-center xl:justify-start ">
            <p className="w-[max-content] flex-shrink-0">0 Mi</p>
            <Slider
              w={{ base: '80%', xl: '65%' }}
              aria-label="slider-ex-2"
              defaultValue={miles}
              min={0}
              max={500}
              onChange={(value) => setMiles(value)}
            >
              <SliderTrack h="15px" borderRadius="8px">
                <SliderFilledTrack bg="#742BFA" />
              </SliderTrack>
              <SliderThumb w="25px" h="25px" className="focus:outline-none" _focus={{ boxShadow: 'none' }} />
            </Slider>
            <p className="flex-shrink-0">500 Mi</p>
          </div>
          <div className="flex justify-evenly xl:w-[90%] ">
            <div className="flex flex-col text-[#3F404C] items-center">
              <p className="text-[34px] xl:text-[50px] font-bold">${cost}</p>
              <p className="text-[14px] xl:text-[18px]">Charge cost</p>
            </div>
            <div className="flex flex-col text-[#3F404C] items-center">
              <p className="text-[34px] xl:text-[50px] font-bold">${savings}</p>
              <p className="text-[14px] xl:text-[18px]">Gas savings</p>
            </div>
          </div>
        </div>
        {isMobile && (
          <div className="w-full h-full flex justify-center items-center ">
            <Image
              src={first!.imageCar || imageCar}
              alt={carName}
              width="1000"
              height="1000"
              className="w-full h-full object-contain "
            />
          </div>
        )}
        {!isMobile && (
          <div className="w-full  flex justify-center items-center ">
            <Image
              src={first!.imageCar || imageCar}
              alt={carName}
              width="1000"
              height="1000"
              className="w-full object-contain "
            />
          </div>
        )}
      </div>
    </div>
  );
}
