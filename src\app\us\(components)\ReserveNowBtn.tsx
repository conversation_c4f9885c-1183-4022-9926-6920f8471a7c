'use client';
/* RIGHT */
import { MouseEvent } from 'react';
import './ReserveNowBtn.css';
import { LuLoader2 } from 'react-icons/lu';
import { handleSmoothScroll } from './USNav/USNav';

interface ReserverBtnProps {
  full?: boolean;
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  onClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  type?: 'submit';
  isDefault?: boolean;
  isSubmitting?: boolean;
  className?: string;
}

const sizes = {
  sm: '40px',
  md: '50px',
  lg: '60px',
};

export default function ReserveNowBtn({
  full,
  size = 'md',
  text = 'Reserve Now',
  onClick,
  type,
  isSubmitting,
  isDefault = true,
  className,
}: ReserverBtnProps) {
  return (
    <button
      type={type && type}
      className={`
        ${full ? 'w-full' : 'w-[250px]'}
        h-[${sizes[size]}]
        py-2 px-2
        min-h-[${sizes[size]}]
        text-[16px]
        font-[Plus-Jakarta-Sans]
        font-bold
        text-white rounded-[10px]
        reserve-btn
        flex justify-center items-center
        ${className}
        `}
      onClick={(e) => {
        if (onClick) {
          onClick(e);
        } else {
          if (isDefault) {
            handleSmoothScroll(e, 'reserve-now');
          }
        }
      }}
      disabled={isSubmitting}
    >
      {isSubmitting ? <LuLoader2 size={26} className=" animate-spin " /> : text}
    </button>
  );
}
