import Link from 'next/link';
import { BsWhatsapp } from 'react-icons/bs';

export default function WhatsappWidget() {
  return (
    <Link
      href="https://api.whatsapp.com/send?phone=5215590632045"
      target="_blank"
      rel="noopener noreferrer"
      aria-label="Contáctanos por WhatsApp"
      className="
        fixed
        w-[60px] xl:w-[70px]
        h-[60px] xl:h-[70px]
        right-6 xl:right-8 
        bottom-6 xl:bottom-8 
        flex
        justify-center
        items-center
        rounded-full
        bg-[#1acc3e]
        p-3 xl:p-4
        cursor-pointer
        z-[99]
        hover:bg-[#18b938]
        transition-colors
        duration-300
        shadow-lg
      "
    >
      <span className="hidden xl:block">
        <BsWhatsapp size={38} className="text-white" />
      </span>
      <span className="block xl:hidden">
        <BsWhatsapp size={32} className="text-white" />
      </span>
    </Link>
  );
}
