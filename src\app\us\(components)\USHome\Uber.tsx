import { FaMoneyBillAlt } from 'react-icons/fa';
import { AiFillStar } from 'react-icons/ai';
import ReserveNowBtn from '../ReserveNowBtn';

export default function Uber() {
  return (
    <section
      id="uber"
      className="
        w-full
        h-[max-content]
        min-h-[664px]
        flex flex-col
        justify-center
        items-center
        gap-6
        bg-[#1E1F24]
        px-6
        md:px-8
        py-[60px]
        lg:py-0
      "
    >
      <div
        // columns={{ base: 1, lg: 2 }}
        // gap={{ base: '50px', md: '70px' }}
        // fontFamily="Plus-Jakarta-Sans"
        // alignItems="center"
        // mx={{ base: 0, md: '50px' }}
        className="
            grid grid-cols-1 lg:grid-cols-2
            gap-[50px] md:gap-[70px]
            font-[Plus-Jakarta-Sans]
            items-center
            mx-0 md:mx-[50px]
        "
      >
        <div className="w-full flex flex-col font-[Plus-Jakarta-Sans] text-white">
          <p className="text-[28px] lg:text-[40px] font-bold">Drive an EV and Boost your Earnings</p>
          <p className="text-[18px] lg:text-[20px] mt-[40px] lg:mt-[80px]">
            Our EV selection qualifies for Zero Emissions incentives and is eligible for:
          </p>
          <ul className="mt-[10px] text-[18px] ml-2  ">
            <li className="flex items-center gap-2 m-0">
              <span className="mb-[4px]">•</span>
              <span>Uber Comfort Electric.</span>
            </li>
            <li className="flex items-center gap-2 m-0">
              <span className=" mb-[4px]">•</span>
              <span>Uber Premium.</span>
            </li>
          </ul>
        </div>
        <div className="grid gap-[10px] md:gap-[20px] w-full lg:w-[90%] text-white">
          <div className="flex flex-col rounded-[10px] py-[20px] px-[30px] rounded-10px text-[#742BFA] hover:bg-purple-strong hover:text-white">
            <div className="flex items-center mb-2">
              <FaMoneyBillAlt size={24} />
              <p className="font-[600] ml-3 ">Get the Zero Emissions incentive</p>
            </div>
            <p className="text-white">
              Drivers of fully electric vehicles (EVs) can earn upto an extra $4000 annually on Uber.
            </p>
          </div>
          {/* 2 */}
          <div
            /* flexDir="column"
            _hover={{ bgColor: 'purple.strong', color: 'white', fill: 'white' }}
            py="20px"
            px="30px"
            borderRadius="10px"
            color="#742BFA"
            fill="purple.strong" */
            className="
              flex flex-col 
              text-[#742BFA]
              icon-container 
              hover:bg-purple-strong 
              hover:text-white 
              rounded-[10px]
              py-[20px] px-[30px]
               hover:fill-white
            "
          >
            <div className="flex items-center mb-2">
              <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="healthicons:money-bag">
                  <path
                    id="Vector"
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M12.5203 3.125C10.1677 3.125 8.02448 3.90208 6.75625 4.53698C6.64167 4.59427 6.53489 4.65052 6.43542 4.70469C6.23854 4.81198 6.07083 4.91198 5.9375 5L7.38021 7.12396L8.05937 7.39427C10.7135 8.73333 14.2729 8.73333 16.9276 7.39427L17.6984 6.99427L19.0625 5C18.78 4.81565 18.4862 4.64918 18.1828 4.50156C16.9208 3.87344 14.8286 3.125 12.5208 3.125H12.5203ZM9.1651 5.52917C8.65423 5.43357 8.14965 5.30695 7.65417 5.15C8.84219 4.6224 10.6125 4.0625 12.5203 4.0625C13.8417 4.0625 15.0911 4.33125 16.1245 4.67188C14.9135 4.84219 13.6214 5.13125 12.3901 5.48698C11.4214 5.76719 10.2891 5.73698 9.1651 5.52917ZM17.4776 8.16667L17.3495 8.23125C14.4297 9.70417 10.5568 9.70417 7.63698 8.23125L7.51562 8.16979C3.12917 12.9828 -0.219794 21.8734 12.5203 21.8734C25.2604 21.8734 21.8297 12.8167 17.4776 8.16667ZM11.9792 12.5C11.7029 12.5 11.4379 12.6097 11.2426 12.8051C11.0472 13.0004 10.9375 13.2654 10.9375 13.5417C10.9375 13.8179 11.0472 14.0829 11.2426 14.2782C11.4379 14.4736 11.7029 14.5833 11.9792 14.5833V12.5ZM13.0208 11.4583V10.9375H11.9792V11.4583C11.4266 11.4583 10.8967 11.6778 10.506 12.0685C10.1153 12.4592 9.89583 12.9891 9.89583 13.5417C9.89583 14.0942 10.1153 14.6241 10.506 15.0148C10.8967 15.4055 11.4266 15.625 11.9792 15.625V17.7083C11.526 17.7083 11.1401 17.4193 10.9964 17.0141C10.975 16.9478 10.9406 16.8864 10.8952 16.8337C10.8497 16.7809 10.7942 16.7378 10.7317 16.7069C10.6693 16.676 10.6014 16.6579 10.5319 16.6537C10.4623 16.6495 10.3927 16.6593 10.327 16.6825C10.2614 16.7057 10.201 16.7418 10.1496 16.7887C10.0981 16.8357 10.0566 16.8924 10.0275 16.9557C9.99834 17.019 9.98219 17.0874 9.97997 17.157C9.97776 17.2266 9.98953 17.296 10.0146 17.3609C10.1582 17.7672 10.4243 18.1189 10.7761 18.3676C11.128 18.6164 11.5483 18.75 11.9792 18.75V19.2708H13.0208V18.75C13.5734 18.75 14.1033 18.5305 14.494 18.1398C14.8847 17.7491 15.1042 17.2192 15.1042 16.6667C15.1042 16.1141 14.8847 15.5842 14.494 15.1935C14.1033 14.8028 13.5734 14.5833 13.0208 14.5833V12.5C13.474 12.5 13.8599 12.7891 14.0036 13.1943C14.025 13.2606 14.0594 13.3219 14.1048 13.3747C14.1503 13.4274 14.2058 13.4706 14.2682 13.5015C14.3306 13.5324 14.3986 13.5505 14.4681 13.5547C14.5376 13.5588 14.6073 13.549 14.673 13.5258C14.7386 13.5026 14.799 13.4665 14.8504 13.4196C14.9019 13.3727 14.9434 13.3159 14.9725 13.2526C15.0017 13.1894 15.0178 13.1209 15.02 13.0513C15.0222 12.9817 15.0105 12.9124 14.9854 12.8474C14.8417 12.4412 14.5757 12.0894 14.2239 11.8407C13.872 11.5919 13.4517 11.4583 13.0208 11.4583ZM13.0208 15.625V17.7083C13.2971 17.7083 13.562 17.5986 13.7574 17.4032C13.9528 17.2079 14.0625 16.9429 14.0625 16.6667C14.0625 16.3904 13.9528 16.1254 13.7574 15.9301C13.562 15.7347 13.2971 15.625 13.0208 15.625Z"
                    className="fill-[#742BFA]"
                  />
                </g>
              </svg>
              <p className="ml-3 font-[600]">Stop paying for gas</p>
            </div>
            <p className="text-white">
              The cost of charging is typically much cheaper than gas often less than half the cost.
            </p>
          </div>
          {/* 3 */}
          <div
            className="
            flex flex-col 
            text-[#742BFA]
            icon-container 
            hover:bg-purple-strong 
            hover:text-white 
            rounded-[10px]
            py-[20px] px-[30px]
             hover:fill-white
          "
          >
            <div className="flex items-center mb-2">
              <AiFillStar size={24} />
              <p className="font-[600] ml-3 ">Elegible for Uber Premium</p>
            </div>
            <p className="text-white">
              Our EV selection is eligible for Uber Premium trips, which riders pay an extra for.
            </p>
          </div>
        </div>
      </div>
      <div className="w-full flex justify-center mt-[50px]">
        <ReserveNowBtn />
        {/* <p>something</p> */}
      </div>
    </section>
  );
}
