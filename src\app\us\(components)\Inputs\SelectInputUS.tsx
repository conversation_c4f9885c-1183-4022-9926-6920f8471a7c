import { cn } from '@/lib/utils';
import { FormControl, FormErrorMessage, Select } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikErrors, FormikTouched, FormikValues } from 'formik';
import { ChangeEvent } from 'react';

interface CustomInputProps {
  name: string;
  label: string;
  errors: FormikErrors<Record<string, string>>;
  touched: FormikTouched<Record<string, string>>;
  options?: {
    label: string;
    value: string;
  }[];
  onChange: (e: ChangeEvent<HTMLSelectElement>) => void;
  className?: string;
  inputClassName?: string;
  disabled?: boolean;
  labelClassName?: string;
}

export default function SelectInputUS({
  name,
  label,
  errors,
  touched,
  options,
  onChange,
  ...rest
}: CustomInputProps) {
  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <>
          <FormControl
            // className="grid gap-3"
            className={cn('grid gap-3', rest.className)}
            isInvalid={Boolean(errors ? errors[name] : '') && touched && touched[name]}
          >
            <label
              htmlFor={name}
              className={cn(
                // 'text-[16px] font-bold',
                rest.labelClassName
              )}
            >
              {label}
            </label>
            {/* <div className="relative w-[full] sm:w-[270px]"> */}
            <div className={cn('relative w-[full] sm:w-[270px]', rest.inputClassName)}>
              <Select
                {...field}
                onChange={onChange}
                _focus={{
                  borderColor: '#742BFA',
                }}
                errorBorderColor="#CED4DA"
                id={name}
                name={name}
                // className="!border-[1px] h-[46px] font-normal border-gray-[#CED4DA] bg-white rounded-md px-3 sm:pr-[50px] md:pr-[30px]  "
                className={cn(
                  `
                  !border-[1px] 
                  h-[46px] 
                  font-normal 
                  border-gray-[#CED4DA] 
                  bg-white 
                  rounded-md 
                  px-3 
                  sm:pr-[50px] 
                  md:pr-[30px] 
                  `,
                  rest.inputClassName
                )}
                style={{
                  textOverflow: 'ellipsis', // Agrega este estilo para recortar el texto
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                }}
              >
                {options?.map((op) => (
                  <option key={op.value} value={op.value}>
                    {op.label}
                  </option>
                ))}
                {/* <option value="Tesla Model Y">Tesla Model Y Long Range</option>
                <option value="Mustan March">Mustang March-E Premium</option>
                <option value="Kia EV6">KIA EV6 Wind (RWD)</option> */}
              </Select>
              <FormErrorMessage
                className="absolute bottom-[-24px] "
                fontSize="13px"
                fontWeight={600}
                letterSpacing=".03rem"
              >
                {form.errors[name]}
              </FormErrorMessage>
            </div>
          </FormControl>
        </>
      )}
    </Field>
  );
}
