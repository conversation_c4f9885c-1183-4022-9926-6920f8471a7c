'use client';
import { usePathname } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerCloseButton,
  useDisclosure,
  Button,
  IconButton,
  Flex,
} from '@chakra-ui/react';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { RiMenu3Fill } from 'react-icons/ri';
import { RefObject, useEffect, useRef } from 'react';
import Link from 'next/link';
import useHandleResize from '../useHandleResize';
// import { customLogEvent } from '@/middlewares/firebase'

export default function DrawerExample() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const btnRef: RefObject<HTMLButtonElement> = useRef(null);
  const pathname = usePathname();
  const isVisible = useHandleResize({ breakpoint: 1000 });

  useEffect(() => {
    if (!isVisible) {
      onClose();
    }
  }, [isVisible, onClose]);

  return (
    <>
      {/* <Button ref={btnRef} colorScheme='teal' onClick={onOpen}>
                Open
            </Button> */}
      <IconButton
        size={'md'}
        icon={isOpen ? <></> : <RiMenu3Fill size={32} color="#742BFA" />}
        aria-label={'Open Menu'}
        bgColor="transparent"
        _hover={{
          bgColor: 'transparent',
        }}
        display={{ base: 'flex', l: 'none' }}
        onClick={isOpen ? onClose : onOpen}
      />
      <Drawer isOpen={isOpen} placement="right" onClose={onClose} finalFocusRef={btnRef} size="custom">
        <DrawerContent p={0} position="relative">
          <DrawerCloseButton position="absolute" size={'lg'} top={4} right={5} />
          <DrawerHeader></DrawerHeader>
          <DrawerBody pr={5} display="flex" flexDir="column" alignItems="end">
            <Flex flexDir="column" mt="50px" alignItems={'end'} sx={{ '> *:not(:last-child)': { mb: 5 } }}>
              {/* <Text
                as="li"
                fontFamily={'Satoshi'}
                minW="120px"
                textAlign="center"
                fontWeight={700}
                color={'#46d97d'}
                transition=".5s"
                fontSize={'15px'}
                lineHeight="17.55px"
                _hover={{
                  bgColor: 'transparent',
                  color: '#47EB84',
                  fontWeight: 800,
                  fontSize: '15px',
                }}
              >
                <Link href="/mx/electricos">Electricos</Link>
              </Text>
              <Link href="/mx/plan/personal">
                <Button
                  fontFamily={'Satoshi'}
                  fontWeight={pathname === '/mx/plan/personal' ? 700 : 500}
                  color={pathname === '/mx/plan/personal' ? 'purple.soft' : 'text.description'}
                  fontSize={pathname === '/mx/plan/personal' ? '15px' : '13px'}
                  bgColor="transparent"
                  transition=".5s"
                  lineHeight="17.55px"
                  _hover={{
                    bgColor: 'transparent',
                    color: 'purple.strong',
                    fontWeight: 700,
                    fontSize: '15px',
                  }}
                >
                  {' '}
                  Uso personal{' '}
                </Button>
              </Link> */}

              <Link href="/mx/plan/plataforma">
                <Button
                  fontFamily={'Satoshi'}
                  fontWeight={pathname === '/mx/plan/plataforma' ? 700 : 500}
                  color={pathname === '/mx/plan/plataforma' ? 'purple.soft' : 'text.description'}
                  fontSize={pathname === '/mx/plan/plataforma' ? '15px' : '13px'}
                  bgColor="transparent"
                  transition=".5s"
                  lineHeight="17.55px"
                  _hover={{
                    bgColor: 'transparent',
                    color: 'purple.strong',
                    fontWeight: 700,
                    fontSize: '15px',
                  }}
                >
                  {' '}
                  Uso plataformas{' '}
                </Button>
              </Link>

              {/* <Link href='/faq'>
                                <Button fontFamily={'Satoshi'} fontWeight={500} bgColor="transparent" color='text.description' transition=".5s" fontSize='13px' lineHeight='17.55px' _hover={{
                                    bgColor: "transparent", color: "purple.strong", fontWeight: 700, fontSize: '15px'
                                }} > Pregunas frecuentes  </Button>
                            </Link> */}

              {/* <Link href="/nosotros">
                                <Button fontFamily={'Satoshi'} fontWeight={500} bgColor="transparent" color='text.description' transition=".5s" fontSize='13px' lineHeight='17.55px' _hover={{
                                    bgColor: "transparent", color: "purple.strong", fontWeight: 700, fontSize: '15px'
                                }} > Nosotros  </Button>
                            </Link> */}
              <Link href="https://blog.onecarnow.com/">
                <Button
                  fontFamily={'Satoshi'}
                  fontWeight={500}
                  color={'text.description'}
                  fontSize={'13px'}
                  bgColor="transparent"
                  transition=".5s"
                  lineHeight="17.55px"
                  // onClick={() => customLogEvent("Blog", "Ingreso a sección de blog")}
                  _hover={{
                    bgColor: 'transparent',
                    color: 'purple.strong',
                    fontWeight: 700,
                    fontSize: '15px',
                  }}
                >
                  {' '}
                  Blog
                </Button>
              </Link>
              <Link href="/mx/contacto">
                <Button
                  fontFamily={'Satoshi'}
                  fontWeight={pathname === '/mx/contacto' ? 700 : 500}
                  color={pathname === '/mx/contacto' ? 'purple.soft' : 'text.description'}
                  fontSize={pathname === '/mx/contacto' ? '15px' : '13px'}
                  bgColor="transparent"
                  transition=".5s"
                  lineHeight="17.55px"
                  _hover={{
                    bgColor: 'transparent',
                    color: 'purple.strong',
                    fontWeight: 700,
                    fontSize: '15px',
                  }}
                >
                  {' '}
                  Contacto{' '}
                </Button>
              </Link>

              <Button
                color={'purple.strong'}
                fontFamily="Manrope"
                bgColor="transparent"
                transition=".5s"
                fontSize="13px"
                fontWeight={600}
                lineHeight="17.55px"
                _hover={{
                  bgColor: 'transparent',
                  fontSize: '15px',
                }}
              >
                {' '}
                Acceso a clientes <ChevronRightIcon fontSize={'19px'} />{' '}
              </Button>
            </Flex>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
}
