import { configureStore, Store } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/dist/query';
import { firestoreApi } from './reducers/firestoreApi';

export const store: Store = configureStore({
  reducer: {
    // [userApi.reducerPath] : userApi.reducer,
    [firestoreApi.reducerPath]: firestoreApi.reducer,
    // doc : docSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({ serializableCheck: false }).concat(firestoreApi.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
