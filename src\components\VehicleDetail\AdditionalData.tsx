import {
  Box,
  Flex,
  Text,
  Heading,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Stack,
  Skeleton,
} from '@chakra-ui/react';
import React, { MouseEventHandler } from 'react';
import CustomButton from '@/components/CustomButton';

const AdditionalData = ({
  aditionalData,
  interior,
  exterior,
  security,
  specialDetails,
  loading,
  borderColor,
}: {
  aditionalData: string;
  interior: string;
  exterior: string;
  security: string;
  specialDetails: string;
  loading: boolean;
  borderColor: string;
}) => {
  const handleClick: MouseEventHandler<HTMLButtonElement> = (e) => {
    e.preventDefault();
  };

  return (
    <>
      {/* // Skeleton mientras llega la informacón de la base de datos */}
      <Flex
        justify={'space-around'}
        w="100%"
        flexDir={{ base: 'column', md: 'row' }}
        mt={{ base: '80px', md: '150px' }}
        pb={{ base: 0, md: '100px' }}
        sx={{ '> *:not(:last-child)': { mb: { base: '50px', md: 0 }, mr: { base: 0, md: '50px' } } }}
      >
        <Flex direction={'column'} w={{ base: '100%', md: '40%' }}>
          <Flex direction={'column'}>
            <Heading
              fontSize={'14px'}
              fontFamily="Plus-Jakarta-Sans"
              color={'text.description'}
              fontWeight={700}
            >
              {' '}
              Datos adicionales del vehículo{' '}
            </Heading>

            {loading ? (
              <Stack w="100%" mt="2vh">
                <Skeleton h="14px" />
                <Skeleton h="14px" />
                <Skeleton h="14px" />
                <Skeleton h="14px" />
              </Stack>
            ) : (
              <Text
                mt="2vw"
                fontSize={'12px'}
                fontFamily="Plus-Jakarta-Sans"
                textAlign="justify"
                color={'text.description'}
                fontWeight={400}
              >
                {aditionalData}
              </Text>
            )}
          </Flex>

          <Box mt={'10vh'} display={{ base: 'none', md: 'initial' }}>
            <Heading
              fontSize={'14px'}
              fontWeight={600}
              fontFamily="Plus-Jakarta-Sans"
              color="text.description"
            >
              {' '}
              ¿Aún con dudas?{' '}
            </Heading>
            <Text fontSize="16px">Dejanos tus datos y nos podremos en contacto contigo</Text>
            <Box mt={'5vh'} w={'50%'}>
              <CustomButton message="Contáctanos" className={borderColor} handleClick={handleClick} />
            </Box>
          </Box>
        </Flex>
        <Box w={{ base: '100%', md: '40%' }}>
          <Accordion allowToggle>
            <AccordionItem border="none">
              <AccordionButton px="10px">
                <Box as="span" flex="1" textAlign="left">
                  <Text
                    fontSize={'14px'}
                    fontWeight={600}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    Interior
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb={4} px="none">
                {loading ? (
                  <Stack>
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                  </Stack>
                ) : (
                  <Text
                    fontSize={'12px'}
                    px="15px"
                    fontWeight={400}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    {interior}
                  </Text>
                )}
              </AccordionPanel>
            </AccordionItem>
            <AccordionItem border="none">
              <AccordionButton px="10px">
                <Box as="span" flex="1" textAlign="left">
                  <Text
                    fontSize={'14px'}
                    fontWeight={600}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    Exterior
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel pb={4} px="none">
                {loading ? (
                  <Stack>
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                  </Stack>
                ) : (
                  <Text
                    fontSize={'12px'}
                    px="15px"
                    fontWeight={400}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    {exterior}
                  </Text>
                )}
              </AccordionPanel>
            </AccordionItem>
            <AccordionItem border="none">
              <AccordionButton px="10px">
                <Box as="span" flex="1" textAlign="left">
                  <Text
                    fontSize={'14px'}
                    fontWeight={600}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    Seguridad
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px="none">
                {loading ? (
                  <Stack>
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                  </Stack>
                ) : (
                  <Text
                    fontSize={'12px'}
                    px="15px"
                    fontWeight={400}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    {security}
                  </Text>
                )}
              </AccordionPanel>
            </AccordionItem>
            <AccordionItem border="none">
              <AccordionButton px="10px">
                <Box as="span" flex="1" textAlign="left">
                  <Text
                    fontSize={'14px'}
                    fontWeight={600}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    Caracteristicas especiales
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px="none">
                {loading ? (
                  <Stack>
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                    <Skeleton h="14px" />
                  </Stack>
                ) : (
                  <Text
                    fontSize={'12px'}
                    px="15px"
                    fontWeight={400}
                    fontFamily="Plus-Jakarta-Sans"
                    color="text.description"
                  >
                    {specialDetails}
                  </Text>
                )}
              </AccordionPanel>
            </AccordionItem>
          </Accordion>
        </Box>
      </Flex>

      <Box my={'70px'} display={{ base: 'initial', md: 'none' }}>
        <Heading fontSize={'14px'} fontWeight={600} fontFamily="Plus-Jakarta-Sans" color="text.description">
          {' '}
          ¿Aún con dudas?{' '}
        </Heading>
        <Text fontSize="16px">Dejanos tus datos y nos podremos en contacto contigo</Text>
        <Box mt={'5vh'} w={'50%'}>
          <CustomButton message="Contáctanos" className={borderColor} handleClick={handleClick} />
        </Box>
      </Box>
    </>
  );
};

export default AdditionalData;
