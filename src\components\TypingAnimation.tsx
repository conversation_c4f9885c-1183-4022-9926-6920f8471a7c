'use client';
import React from 'react';
import { Typewriter, Cursor } from 'react-simple-typewriter';

interface TypeAnimationProps {
  texts: string[];
  cursorColor?: string;
  typeSpeed: number;
  deleteSpeed: number;
  delaySpeed: number;
  loop?: boolean;
}

export default function TypingAnimation({
  texts,
  cursorColor,
  typeSpeed,
  deleteSpeed,
  delaySpeed,
  loop,
}: TypeAnimationProps) {
  return (
    <span>
      <Typewriter
        words={texts}
        typeSpeed={typeSpeed}
        deleteSpeed={deleteSpeed}
        delaySpeed={delaySpeed}
        loop={loop}
      />
      <Cursor cursorColor={`${cursorColor ? cursorColor : null}`} />
    </span>
  );
}
