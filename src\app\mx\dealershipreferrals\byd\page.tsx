'use client';
/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Flex, Heading, Image, Spinner, Text } from '@chakra-ui/react';
import React, { Dispatch, useEffect, useState } from 'react';
import CustomButton from '@/components/CustomButton';
import blob from '@/assets/contact/blob.png';
import { Formik } from 'formik';
import Swal from 'sweetalert2';
import axios from 'axios';
import CustomField from '@/components/Form/FieldForm';
import { dealershipContactSchema } from '@/schemas/dealershipContactSchema';
import SelectInput from '@/components/Form/SelectInput';
import { cityLeadOptions } from '@/constants';

const Contacto = () => {
  const [sending, setSending] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Box
        w="100%"
        h="100vh"
        minH="100vh"
        display="grid"
        gridTemplateColumns={{ base: '100%', l: '60fr 40fr' }}
        position="relative"
        justifyContent="center"
        alignItems="center"
      >
        {sending ? (
          <>
            <Flex
              w="100%"
              h="100%"
              justifyContent="center"
              alignItems="center"
              position="absolute"
              zIndex={3}
            >
              <>
                <Spinner
                  thickness="15px"
                  speed="0.65s"
                  emptyColor="gray.200"
                  color="gray.400"
                  w="200px"
                  h="200px"
                />
              </>
            </Flex>
          </>
        ) : null}
        <Flex
          w="100%"
          h="100%"
          bgColor="purple.soft"
          display={{ base: 'none', l: 'flex' }}
          flexDir="column"
          px="7%"
          pt="14%"
          sx={{ '> *:not(:last-child)': { mb: 5 } }}
          color="white"
          overflow="hidden"
          position="relative"
          fontFamily="Plus-Jakarta-Sans"
        >
          <Heading fontFamily="Plus-Jakarta-Sans">¿Tienes dudas?</Heading>
          <Text>Compártenos todas tus dudas y preguntas, y un experto se pondrá en contacto.</Text>
          <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-329px"
            bottom="-22%"
          />
          <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-80px"
            bottom="-33%"
          />
        </Flex>
        <Flex
          w={{ base: '100%', l: '100%' }}
          h="100%"
          justifyContent="center"
          alignItems={{ base: 'start', l: 'center' }}
          bgColor="bgColor"
          py="50px"
          position="relative"
        >
          <Image
            w="12rem"
            h="15rem"
            src={blob.src}
            position="absolute"
            right={0}
            top={'-4rem'}
            zIndex={10}
            display={{ l: 'none' }}
          />
          <Flex
            w={{ base: '90%', l: '70%' }}
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: '25px', l: '30px' } } }}
          >
            <Text color="text.main" fontSize="30px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
              Contáctanos
            </Text>
            <FormContact2 sending={sending} setSending={setSending} />
          </Flex>
        </Flex>
      </Box>
    </>
  );
};

export default Contacto;

interface MyFormValues {
  nameSeller: string;
  phoneSeller: string;
  city: { value: string; label: string };
  // agencyName: { value: string; label: string };
  agencyName: string;
  name: string;
  phone: string;
  email: string;
}

function FormContact2({
  sending,
  setSending,
}: {
  sending: boolean;
  setSending: Dispatch<React.SetStateAction<boolean>>;
}) {
  async function sendToHubspot(form: MyFormValues) {
    const url = '/api/hubspot/createContact';
    const data = {
      nombre_del_ejecutivo_de_ventas: form.nameSeller,
      telefono_del_ejecutivo_de_ventas: form.phoneSeller,
      ciudad__con_selector_: form.city.value,
      nombre_de_la_agencia: form.agencyName,
      phone: form.phone,
      firstname: form.name,
      email: form.email,
      fuente: 'Agencia BYD',
      // source: 'Agencia',
      isEdit: true,
      source: localStorage.getItem('source') || 'vvAPlIX',
    };
    const response = await axios.post(`${url}`, data);
    return response;
  }

  return (
    <Formik
      initialValues={{
        nameSeller: '',
        phoneSeller: '',
        city: { value: '', label: 'Selecciona' },
        agencyName: '',
        name: '',
        phone: '',
        email: '',
        // car: 'Formulario desde apartado de contacto',
      }}
      validationSchema={dealershipContactSchema}
      onSubmit={async (values, { resetForm }) => {
        try {
          setSending(true);

          await sendToHubspot(values);

          Swal.fire({
            title: 'Información enviada',
            text: 'Te contactáremos lo más pronto posible',
            icon: 'success',
            confirmButtonText: 'Cerrar',
          });
          setSending(false);
          resetForm();
        } catch (e: any) {
          setSending(false);
          resetForm();

          return await Swal.fire({
            title: 'El referido ya se encuentra en registrado',
            // text: e.response.data.message,
            icon: 'info',
            confirmButtonText: 'Cerrar',
          });
        } finally {
        }
      }}
    >
      {({ errors, touched, handleSubmit }) => (
        <form
          onSubmit={handleSubmit}
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Flex
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: 3, l: 5 } } }}
            w={{ base: '90%', l: '100%' }}
            alignItems="center"
            gap={2}
            fontFamily="Plus-Jakarta-Sans"
          >
            <CustomField
              name="nameSeller"
              label="Nombre de Ejecutivo de Ventas"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="text"
              placeholder="Nombres..."
            />
            <CustomField
              name="phoneSeller"
              label="Teléfono de Ejecutivo de Ventas"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="number"
              placeholder="Numero teléfonico"
            />

            <SelectInput label="Ciudad" name="city" options={cityLeadOptions} />

            <CustomField
              label="Nombre de la Agencia"
              name="agencyName"
              touched={touched}
              errors={errors}
              type="text"
              placeholder="Agencia..."
            />

            <CustomField
              name="name"
              label="Nombre Completo del referido"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="text"
              placeholder="Nombres..."
            />

            <CustomField
              name="phone"
              label="Numero de Teléfono del referido"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="number"
              placeholder="Numero teléfonico"
            />

            <CustomField
              name="email"
              label="Correo electrónico del referido"
              touched={touched}
              // handleBlur={handleBlur}
              errors={errors}
              type="email"
              placeholder="<EMAIL>"
            />

            <CustomButton message="Enviar" className="btn-purple" type="submit" disabled={!!sending} />
          </Flex>
        </form>
      )}
    </Formik>
  );
}
