import { AiOutlineCar, AiOutlineClockCircle } from 'react-icons/ai';
import { BsPhone } from 'react-icons/bs';
import ReserveNowBtn from '../ReserveNowBtn';

export default function EasyWay() {
  return (
    <section
      id="how-it-works"
      className="w-full min h-[100%] min-h-[840px] bg-[#FFF] pt-[100px] pb-[100px] lg:pb-0 relative overflow-y-hidden"
    >
      <div className="w-full items-center flex flex-col gap-[60px]">
        <div className="flex flex-col text-center items-center font-[Plus-Jakarta-Sans] gap-2 md:gap-0 px-6 xl:px-0 ">
          <p className="lg:text-[48px] text-[28px] text-[#742BFA] font-bold">The Easiest Way to Get an EV</p>
          <p className="text-[16px] xl:text-[18px] text-center md:font-normal mt-[10px] lg:text-[22px] text-[#3F404C] w-[90%]">
            You choose the car, we take care of the rest
          </p>
        </div>
        <div className="w-full 2xl:w-[85%] flex flex-wrap justify-evenly text-center  ">
          <div
            className={`
              w-[300px] h-[250px] 
              flex flex-col 
              items-center justify-center
              gap-2 font-[Plus-Jakarta-Sans] 
              rounded-[10px]
              text-[#742BFA] hover:bg-[#742BFA] hover:text-white
              group
            `}
          >
            <AiOutlineCar size={50} strokeWidth={3} />
            <p className="font-bold">1. Select your car</p>
            <p className="text-[#3F404C] font-[500] group-hover:text-white w-[90%]">
              Select your preferred car and available city.
            </p>
          </div>
          <div
            className={`
              w-[290px] h-[250px] 
              flex flex-col 
              items-center justify-center
              gap-2 font-[Plus-Jakarta-Sans] 
              rounded-[10px]
              text-[#742BFA] hover:bg-[#742BFA] hover:text-white
              group
            `}
          >
            <BsPhone size={50} />
            <p className="font-bold">2. Submit your application</p>
            <p className="text-[#3F404C] font-[500] group-hover:text-white w-[90%] ">
              Through a simple online process that takes less than 10 minutes.
            </p>
          </div>
          <div
            className={`
              w-[290px] h-[250px] 
              flex flex-col 
              items-center justify-center
              gap-2 font-[Plus-Jakarta-Sans] 
              rounded-[10px]
              text-[#742BFA] hover:bg-[#742BFA] hover:text-white
              group
            `}
          >
            <AiOutlineClockCircle size={50} strokeWidth={3} />
            <p className="font-bold">3. Receive your EV</p>
            <p className="text-[#3F404C] font-[500] group-hover:text-white w-[90%]">
              Within 3 to 5 business days and get ready to increase your profits.
            </p>
          </div>
        </div>
      </div>
      <div className="flex justify-center mt-[50px] md:mt-[115px] relative z-[2] lg:mt-[60px]">
        <ReserveNowBtn />
      </div>
      {/* <div className="w-[100%] h-[20px] origin-[rotate(3.048deg)] border-red-400 border-2"> */}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="100%"
        // height="271"
        viewBox="0 0 1728 271"
        fill="none"
        className="h-[130px] sm:h-[271px] lg:h-[40%] absolute bottom-[-5px] md:bottom-[60px]"
      >
        <path
          d="M-10.5 0.5C302.611 472.393 875.699 80.3468 1739.37 269.713"
          stroke="#B288FF"
          strokeWidth="3"
        />
      </svg>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="100%"
        // height="195"
        viewBox="0 0 1728 195"
        fill="none"
        className="h-[90px] sm:h-[195px] lg:h-[30%]  absolute bottom-0 md:bottom-[45px]"
      >
        <path
          d="M0.0266414 1.00065C313.138 472.894 864.352 -96.3657 1728.03 93.0006"
          stroke="#9747FF"
          strokeWidth="2"
        />
      </svg>
      {/* </div> */}
    </section>
  );
}
