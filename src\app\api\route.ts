// import { IVehicles } from '@/middlewares/interfaces';
import { dbMain } from '@/services/firebase';
import axios from 'axios';
import { collection, getDocs } from 'firebase/firestore';
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  const getAllVehiclesMain = async () => {
    // const data: IVehicles[] = [];
    const querySnapshot = await getDocs(collection(dbMain, 'vehiclesMain'));
    const first = querySnapshot.docs[0].data();
    return first;
  };
  const resultado = await getAllVehiclesMain();
  const response = await axios.get(resultado.images[0], {
    responseType: 'arraybuffer', // Configura responseType como 'arraybuffer'
  });
  // Verifica que la respuesta sea un array de bytes
  if (ArrayBuffer.isView(response.data)) {
    // Obtiene el tipo MIME de la imagen desde la respuesta HTTP
    const contentType = response.headers['content-type'];

    // Crea un Blob a partir de los datos y el tipo MIME
    const blob = new Blob([response.data], { type: contentType });

    // Nombre del archivo
    const fileName = resultado.name;

    // Ruta de la carpeta de destino
    const folderPath = path.join(__dirname, '/imagenes');

    // Asegúrate de que la carpeta de destino exista, y créala si no existe
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true }); // Crea la carpeta de destino y todas las carpetas intermedias si es necesario
    }
    const arrayBuffer = await blob.arrayBuffer();
    // Ruta completa del archivo de destino
    const filePath = path.join(folderPath, fileName);

    // Guarda el ArrayBuffer como archivo en la carpeta de destino
    fs.writeFileSync(filePath, Buffer.from(arrayBuffer));

    console.log('Archivo guardado en:', filePath);
    // const blob = new Blob([response.data], { type: contentType });

    // console.log('Blob creado:', blob);

    // // Convert the Blob to an ArrayBuffer
    // const arrayBuffer = await blob.arrayBuffer();

    // console.log('ArrayBuffer creado:', arrayBuffer);

    // // Guarda el ArrayBuffer como archivo en una carpeta en el servidor
    // const filePath = path.join(__dirname, 'imagenes', resultado.name.replace(' ', ''));

    // // Asegúrate de que filePath sea una ruta válida
    // console.log('Ruta de archivo de destino:', filePath);

    // fs.writeFileSync(filePath, Buffer.from(arrayBuffer));

    // console.log('Archivo guardado en:', filePath);
  } else {
    console.error('La respuesta no es un array de bytes.');
  }
  // console.log(firstImage.data);
  return NextResponse.json(resultado);
}

// querySnapshot.forEach((doc) => {
//   const vehicle = doc.data();
//   const vehicleData: IVehicles = {
//     name: vehicle.name,
//     description: vehicle.description,
//     payment: vehicle.payment,
//     images: vehicle.images,
//     plan: vehicle.plan,
//     security: vehicle.security,
//     seats: vehicle.seats,
//     motor: vehicle.motor,
//     transmission: vehicle.transmission,
//     liters: vehicle.liters,
//     interior: vehicle.interior,
//     exterior: vehicle.exterior,
//     aditionalData: vehicle.aditionalData,
//     specialDetails: vehicle.specialDetails,
//     uid: doc.id.trim(),
//     durationMonths: vehicle.durationMonths,
//     planOptions: vehicle.planOptions,
//     paymentOptions: vehicle.paymentOptions,
//   };
//   data.push(vehicleData);
// });
