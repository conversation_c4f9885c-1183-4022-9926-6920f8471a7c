'use client';
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Center, Flex, Image, Text } from '@chakra-ui/react';
import { useState } from 'react';
import Slider from 'react-slick';

type DocData = {
  url: string;
  docId: string;
  originalName: string;
};

export default function PlatformSlider({ images, borderColor }: { images: string[]; borderColor: string }) {
  const [slider, setSlider] = useState<Slider | null>(null);
  // These are the images used in the slide

  // console.log(images.length)
  const settings = {
    customPaging: function (i: any) {
      return (
        <Center key={i} w="100%" h="100%">
          <Image w={'90%'} h="90%" borderRadius="8px" src={images[i]} alt="image" objectFit="contain" />
        </Center>
      );
    },
    dots: true,
    dotsToShow: 3,
    dotsClass: 'slick-dots slick-thumb',
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 1,
    slidesToScroll: 1,
  };
  return (
    <Flex w="100%" h="100%" justifyContent="center" position="relative">
      {/* <Center w="!00%" position="absolute" zIndex={3}> */}
      <Text fontSize="9px" fontFamily="" position="absolute" zIndex={3} color="#90A3BF" left={0}>
        *Imágenes de ilustración
      </Text>
      <Box
        w={'100%'}
        position="relative"
        sx={{
          '.slick-slider .slick-track': {
            display: 'flex',
            alignItems: 'center',
          },
          '.slick-thumb': {
            bottom: '-85px',
          },
          '.slick-thumb li': {
            width: '90px',
            height: '90px',
          },
          '@media(max-width: 425px)': {
            '.slick-slider .slick-thumb': {
              display: 'flex !important',
              flexWrap: 'wrap !important',
              bottom: images.length > 4 ? '-120px' : '-80px',
            },
          },
          '@media only screen and (min-width: 1000px) and (max-width: 1078px)': {
            '.slick-slider .slick-thumb': {
              // bottom: '-120px',
              bottom: images.length > 4 ? '-120px' : '-60px',
            },
          },
          '@media screen and (max-width: 1000px)': {
            '.slick-thumb li': {
              width: '60px',
              height: '60px',
            },
          },
          '.slick-thumb .slick-active': {
            border: `2px solid ${borderColor}`,
            borderRadius: '10px',
            // backgroundColor: '#FC506E',
          },
          '.slick-thumb img': {
            objectFit: 'contain',
          },
          '.slick-arrow': {
            backgroundColor: 'cyan.500',
            color: 'white',
            w: '30px',
            h: '50px',
            transition: '0.2s',
            _hover: {
              backgroundColor: '#7B61FF',
              color: 'white',
            },
            _focus: {
              backgroundColor: 'cyan.500',
              color: 'white',
            },
            _before: {
              transition: '0.2s',
            },
          },
        }}
      >
        {/* CSS files for react-slick */}
        <link
          rel="stylesheet"
          type="text/css"
          charSet="UTF-8"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
        />
        <link
          rel="stylesheet"
          type="text/css"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
        />
        <Slider {...settings} ref={() => setSlider(slider)}>
          {images.map((img, i) => {
            return <Slide key={i} img={img} />;
          })}
        </Slider>
      </Box>
    </Flex>
  );
}

function Slide({ img }: { img: string }) {
  return (
    <Center>
      <Image
        w={{ base: '500px', l: '600px' }}
        h={{ base: '400px', l: '500px' }}
        src={img}
        alt="img"
        objectFit="contain"
        px={{ base: '3px', md: 0 }}
      />
    </Center>
  );
}
