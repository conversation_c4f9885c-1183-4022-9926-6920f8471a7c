'use client';
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Flex, Image, Text } from '@chakra-ui/react';
import first from '@/assets/electricos/icons/1.png';
import second from '@/assets/electricos/icons/2.png';
import third from '@/assets/electricos/icons/3.png';
import { StaticImageData } from 'next/image';

const points = [
  {
    image: first,
    title: 'MOVILIDAD SOSTENIBLE',
    description: 'La nueva tendencia de movilidad eléctrica en México que se preocupa por el medio ambiente',
  },
  {
    image: second,
    title: 'LUJO FUTURISTA',
    description:
      'Tener un OCN-NETA es sinónimo de exclusividad, fina tecnología y autenticidad. Los autos eléctricos más modernos con la mejor tecnología',
  },
  {
    image: third,
    title: 'RENTING',
    description:
      'Revolucionamos la manera de estrenar un auto eléctrico a través de una suscripción, con pagos a tu medida y los mejores beneficios',
  },
];

export default function About() {
  return (
    <Flex
      w="100%"
      id="ocn-electricos"
      minH="760px"
      bgColor="#F2F2F2"
      pt="60px"
      pb="50px"
      flexDir="column"
      alignItems="center"
      px={{ base: '30px', md: '150px' }}
      gap="30px"
      color="#1A1A1A"
      fontFamily="Plus-Jakarta-Sans"
      textAlign="center"
    >
      <Text fontSize={{ md: '40px', base: '24px' }} fontWeight={700}>
        OCN ELÉCTRICOS
      </Text>
      <Text
        w={{ base: '100%', lg: '58%' }}
        fontSize={{ base: '16px', md: '20px' }}
        fontWeight={300}
        lineHeight="40px"
      >
        Los atributos más lujosos y tecnológicos para personas que buscan revolucionar la manera en la que
        adquieren un auto nuevo.
      </Text>
      <Flex
        w="100%"
        mt="50px"
        listStylePosition="inside"
        sx={{
          '> *:not(:last-child)': {
            mb: { base: '40px', lg: 0 },
          },
        }}
        justifyContent="space-around"
        flexWrap="wrap"
      >
        {points.map((p, i) => (
          <Step key={i} props={p} />
        ))}
      </Flex>
    </Flex>
  );
}

interface StepProps {
  image: StaticImageData;
  title: string;
  description: string;
}

function Step({ props }: { props: StepProps }) {
  return (
    <Flex
      as="li"
      w="320px"
      m="0"
      flexDir="column"
      textAlign="center"
      alignItems="center"
      sx={{
        '> *:not(:last-child)': {
          mb: 1,
        },
      }}
    >
      <Flex w="150px" h="150px" justifyContent="center" alignItems="center">
        {/* <Icon color={"white"} w="50px" h="50px" as={props.icon} /> */}
        <Image src={props.image.src} alt="some" />
      </Flex>
      <Text fontWeight="700" mt="10px" fontSize={{ base: '16px', md: '24px' }} fontFamily="Plus-Jakarta-Sans">
        {' '}
        {props.title}{' '}
      </Text>
      <Text fontSize={{ base: '14px', md: '16px' }} fontFamily="Manrope">
        {props.description}
      </Text>
    </Flex>
  );
}
