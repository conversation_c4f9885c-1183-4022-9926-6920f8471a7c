@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  .theme {
    --animate-marquee: marquee var(--duration) infinite linear;
    --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@font-face {
  font-family: "Manrope";
  src: local("Manrope-Medium"),
    url("./fonts/Manrope/Manrope-Medium.ttf") format("truetype");
}

@font-face {
  font-family: "Plus-Jakarta-Sans";
  src: local("PlusJakartaSans"),
    url("./fonts/PlusJakartaSans/PlusJakartaSans-Medium.ttf") format("truetype");
}

@font-face {
  font-family: "Satoshi";
  src: local("Satoshi-Medium"),
    url("./fonts/Satoshi/Satoshi-Medium.ttf") format("truetype");
}

* {
  margin: 0;
  padding: 0;
}

body{
  position: relative;
}

body::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

body::-webkit-scrollbar {
  width: 10px;
  background-color: #f5f5f5;
}

body::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: rgba(158, 142, 255, 0.65);
}

.slick-slider .slick-list,
.slick-slider .slick-track {
  height: 100%;
}

@media (max-width: 1550) {
  .slick-slide .slick-active .slick-current {
    width: 200px;
  }
}
@media (max-width: 450px) {
  .slick-slider .slick-list,
  .slick-slider .slick-track {
    height: 340px;
  }
}

thead tr th {
  border: 1.5px solid #e1e1e1;
  border-bottom: 2px solid black;
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 20px;
  padding-right: 20px;
  background-color: rgb(215, 212, 212);
}

tbody tr td {
  border: 1px solid #e1e1e1;
  padding-left: 25px;
  padding-right: 35px;
}

ul,
li {
  margin-left: 30px;
}

ol,
li {
  margin-left: 30px;
}

/* ESTILOS RESPONSIVE PARA EL BLOG  */

div.se-component.se-image-container.__se__float-right {
  display: flex !important;
  justify-content: flex-end !important;
}

div.se-component.se-image-container.__se__float-right figure {
  margin: 0 !important;
}

div.se-component.se-image-container.__se__float-center,
div.se-component.se-image-container.__se__float-none,
div.se-component.se-image-container.__se__float-right,
figure img {
  border-radius: 2em;
}

@media (max-width: 950px) {
  div.se-component.se-video-container.__se__float-center figure {
    width: 50% !important;
    height: 60% !important;
    padding-bottom: 0px !important;
  }

  div.se-component.se-video-container.__se__float-center figure iframe {
    width: 100% !important;
    height: 100% !important;
    min-height: 200px !important;
    margin: auto !important;
    padding-bottom: 0px !important;
  }

  div.se-component.se-image-container.__se__float-none figure img {
    width: 70% !important;
    height: 70% !important;
    padding-bottom: 0px !important;
  }

  div.se-component.se-image-container.__se__float-right figure {
    /* width: 100% !important; */
    height: 100% !important;
    /* padding-bottom: 0px !important; */
  }

  div.se-component.se-image-container.__se__float-right figure img {
    width: 70% !important;
    height: 70% !important;
    padding-bottom: 0px !important;
    margin-left: 30% !important;
  }

  div.se-component.se-image-container.__se__float-center figure {
    width: 100% !important;
    height: 100% !important;
    /* padding-bottom: 0px !important; */
  }

  div.se-component.se-image-container.__se__float-center figure img {
    width: 70% !important;
    height: 70% !important;
    padding-bottom: 0px !important;
    margin: auto !important;
  }
}

@media (max-width: 650px){
  div.se-component.se-video-container.__se__float-center figure {
    width: 70% !important;
    height: 60% !important;
    padding-bottom: 0px !important;
  }
}

@media (max-width: 450px){
  div.se-component.se-video-container.__se__float-center figure {
    width: 80% !important;
    /* height: 100% !important; */
    padding-bottom: 0px !important;
  }
}

.error-message {
  color: red;
  font-weight: 400;
  font-size: 14px;
}

/* TWILIO WEBCHAT */

#twilio-webchat-widget-root button.css-1cpgmj2 {
  width: 60px;
  height: 60px;
  border: 1px solid white !important;
  display: none;
  transition: .2s;
  background-color: transparent;
  background-size: 120% auto;
  background-image: linear-gradient(92.67deg, #A74DF9 30.58%, #6210FF 76.12%);
}

#twilio-webchat-widget-root button.css-1cpgmj2:hover{
  background-position: right center;
}

@media (max-width: 400px) {
  #twilio-webchat-widget-root button.css-1cpgmj2 {
    width: 60px;
    height: 60px;
  }

  #twilio-webchat-widget-root button.css-1cpgmj2 span {
    width: 40px;
    height: 40px;
  }
}

.reserve-btn {
  /* background-image: linear-gradient(111deg, #5A00F8 0%, #A74DF9 100%); */
  background-image: linear-gradient(to right, #6210FF 0%, #9c37fa 51%, #6210FF 100%);
  background-size: 200% auto;
  transition: background-position .5s;
}

.reserve-btn:hover {
    background-position: right;
}

.icon-container:hover svg path {
  fill: white;
}

.whatsapp-swal-btn{
  background-color: #1ACC3E !important;
}

.custom-margin > *:not(:last-child) {
  margin-right: 0; /* Default margin for mobile */
  margin-left: 0;
}

@media (min-width: 768px) {
  .custom-margin > *:not(:last-child) {
    margin-right: 30px; /* Margin for md breakpoint */
  }
}

.custom-mr-3 > *:not(:last-child) {
  margin-right: 0.75rem; /* 3 * 0.25rem = 0.75rem */
}

.custom-mb-5 > *:not(:last-child) {
  margin-bottom: 1.25rem; /* 3 * 0.25rem = 0.75rem */
}

/* ---break---  */

@theme inline {
  @keyframes marquee {
  from {
    transform: translateX(0);
    }
  to {
    transform: translateX(calc(-100% - var(--gap)));
    }
  }
  @keyframes marquee-vertical {
  from {
    transform: translateY(0);
    }
  to {
    transform: translateY(calc(-100% - var(--gap)));
    }
  }
    @keyframes marquee {
      from {
        transform: translateX(0);
      }
  
      to {
        transform: translateX(calc(-100% - var(--gap)));
      }
    }
  
    @keyframes marquee-vertical {
      from {
        transform: translateY(0);
      }
  
      to {
        transform: translateY(calc(-100% - var(--gap)));
      }
    }
}
@layer utilities {
  .animation-pause {
    animation-play-state: paused;
  }

  .animation-running {
    animation-play-state: running;
  }
}

/* Asegúrate de que estas definiciones estén disponibles globalmente */
:root {
  --duration: 40s;
  --gap: 1rem;
}

/* Definiciones explícitas de keyframes para asegurar que estén disponibles en producción */
@keyframes marquee {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes marquee2 {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}