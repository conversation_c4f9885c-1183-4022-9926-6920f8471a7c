import { HUBSPOT_TOKEN, HUBSPOT_URL, API_URL } from '@/constants';
import axios from 'axios';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  const body = await req.json();
  const { requestId, hubspotId } = body;
  if (!requestId || !hubspotId) {
    return NextResponse.json({ message: 'Hubo un error', error: 'Faltan datos necesarios' }, { status: 400 });
  }

  const data = {
    requestId,
    properties: {
      'personalData.firstName': body.firstname || body.firstName || null,
      'personalData.lastName': body.lastname || body.lastName || null,
      'personalData.email': body.email || null,
      'personalData.city': body.city || body.ciudad__con_selector_,
    },
  };

  try {
    await axios.patch(`${API_URL}/admissionRequest/update`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    await axios.patch(
      `${HUBSPOT_URL}/${hubspotId}`,
      {
        properties: {
          firstname: body.firstname || body.firstName || null,
          email: body.email || null,
          city: body.city || body.ciudad__con_selector_,
        },
      },
      {
        headers: {
          Authorization: `Bearer ${HUBSPOT_TOKEN}`,
        },
      }
    );
  } catch (error: any) {
    console.log('error data', error.response);
    console.log('error message', error.message);
    if (error.response.data?.message?.includes('already exists')) {
      return NextResponse.json(
        { message: 'Ya has enviado tu solicitud, espera a que te contactemos', error },
        { status: 409 }
      );
    }
  }
  return NextResponse.json({
    message: 'Hello',
    requestId,
    hubspotId,
  });
}
