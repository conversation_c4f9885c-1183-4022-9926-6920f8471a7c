/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';

const useWidgetColorHandler = (pathname: string, secondPathname?: string) => {
  const [widget, setWidget] = useState<any>(null);
  useEffect(() => {
    let button: any = document.querySelector('#twilio-webchat-widget-root button.css-1cpgmj2');
    if (button && location.pathname.endsWith(secondPathname as string)) {
      button.style.backgroundImage = 'linear-gradient(92.67deg, #A74DF9 30.58%, #6210FF 76.12%)';
      setWidget(button);
    } else if (button && location.pathname.startsWith(pathname)) {
      button.style.display = 'flex';
      button.style.backgroundImage = 'linear-gradient(92.28deg, #00D2FF 2.67%, #028CF3 78.85%)';
    }
    if (!button) {
      setTimeout(() => {
        button = document.querySelector('#twilio-webchat-widget-root button.css-1cpgmj2');
        setWidget(button);
      }, 2550);
    }
    setWidget(button);

    return () => {
      if (button) button.style.backgroundImage = 'linear-gradient(92.67deg, #A74DF9 30.58%, #6210FF 76.12%)';
    };
  }, [widget, pathname]);
};

export default useWidgetColorHandler;

export function useWidgetApperance() {
  const [buttonExist, setButtonExist] = useState<boolean>(false);

  useEffect(() => {
    let button: any = document.querySelector('#twilio-webchat-widget-root button.css-1cpgmj2');
    if (!button) {
      setTimeout(() => {
        button = document.querySelector('#twilio-webchat-widget-root button.css-1cpgmj2');
        setButtonExist(true);
      }, 2550);
    }
    if (button) button.style.display = 'flex';
  }, [buttonExist]);
}
