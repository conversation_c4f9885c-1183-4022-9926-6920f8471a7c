import React, { useState, useEffect } from 'react';
import Link from 'next/link';

function LinkWrapper({ children, link }: { children: any; link: string }) {
  const [showLink, setShowLink] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setShowLink(window.innerWidth > 550);
    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return <div>{showLink ? <Link href={link}>{children}</Link> : <>{children}</>}</div>;
}

export default LinkWrapper;
