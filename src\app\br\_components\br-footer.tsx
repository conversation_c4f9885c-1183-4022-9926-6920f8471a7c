import Image from 'next/image';
import ComingSoon from '@/assets/br/coming soon image.png';
import BRCountry from '@/assets/br/br-country.png';

export default function BRFooter() {
  return (
    <div className="relative w-full min-h-[220px] overflow-hidden mt-8">
      {/* Background image */}
      <Image
        src={ComingSoon}
        alt="Coming soon background"
        fill
        className="object-cover"
        priority
        style={{ zIndex: 1 }}
      />
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/40 z-10" />
      {/* Content */}
      <div
        className="
        relative z-20 flex flex-col items-center justify-center
        py-8 px-6
        md:absolute md:inset-0 md:flex-row md:items-center md:justify-between md:px-16 lg:px-32 md:h-full
      "
      >
        <span className="text-white text-lg md:text-2xl font-medium text-center md:text-left md:mr-8">
          Novos elétricos pra rodar: mais economia,
          <br className="hidden md:block" /> mais lucro!
        </span>
        <div className="flex flex-col md:flex-row items-center mt-4 md:mt-0 md:ml-8">
          <span className="text-white text-2xl md:text-3xl font-bold mr-2">Chegando em Breve</span>
          <Image
            src={BRCountry}
            alt="Brasil country"
            width={48}
            height={48}
            className="inline-block mx-1"
            style={{ minWidth: 32, minHeight: 32 }}
          />
          <span className="text-white text-2xl md:text-3xl font-bold ml-2"> en São Paulo!</span>
        </div>
      </div>
    </div>
  );
}
