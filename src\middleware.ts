/* eslint-disable @typescript-eslint/no-unused-vars */
// middleware.ts
import { NextRequest, NextFetchEvent, NextResponse } from 'next/server';
export async function middleware(request: NextRequest, _next: NextFetchEvent) {
  const res = NextResponse.next();
  const country = request.geo?.country ?? 'MX';
  res.headers.set('x-country', country);
  const url = new URL(request.url);
  const origin = url.origin;
  const pathname = url.pathname;
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-url', request.url);
  requestHeaders.set('x-origin', origin);
  requestHeaders.set('x-pathname', pathname);

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  // return res;
}
