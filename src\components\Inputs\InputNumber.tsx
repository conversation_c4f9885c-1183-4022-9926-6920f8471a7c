// import { ErrorMessage, Field, useField } from 'formik';
// import { ChangeEvent } from 'react';
// import { Input } from '../ui/input';
// import { cn } from '@/lib/utils';

// type InputNumberProps = {
//   label: string;
//   name: string;
//   disabled?: boolean;
//   defaultValue?: string;
//   onChange?: (value: number) => Promise<any> | any;
//   onBlur?: (event: any) => Promise<any>;
//   allowDecimals?: boolean;
//   max?: number;
//   min?: number;
//   className?: string;
//   direction?: 'horizontal' | 'vertical';
//   inputClassName?: string;
// };

// export default function InputNumber({
//   label,
//   name,
//   disabled,
//   onChange,
//   allowDecimals,
//   defaultValue,
//   onBlur,
//   max,
//   min,
//   className,
//   direction = 'vertical',
//   inputClassName,
// }: InputNumberProps) {
//   const [field, meta, helpers] = useField(name);
//   const hasError = meta.touched && meta.error;
//   // const { setValue } = helpers;

//   const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
//     let value = e.target.value;
//     // const numValue = Number(value);
//     helpers.setValue(value);

//     // console.log('numValue', numValue);
//     // Validación de min/max
//     // if (typeof min === 'number' && numValue < min) value = min.toString();
//     // if (typeof max === 'number' && numValue > max) value = max.toString();

//     // setValue(value);
//     // Si hay una función onChange, se aplica
//     // if (onChange) {
//     //   const result = await onChange(Number(value));
//     //   value = result?.toString() || value;
//     // }

//     // Se actualiza el valor en Formik
//   };

//   const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
//     if (allowDecimals) {
//       if (['e', 'E', '+', '-'].includes(event.key)) {
//         event.preventDefault();
//       }
//     } else {
//       if (['e', 'E', '+', '-', '.'].includes(event.key)) {
//         event.preventDefault();
//       }
//     }
//   };

//   return (
//     <div /* className="flex flex-col" */
//       className={cn(
//         'flex',
//         // if horizontal, add flex-row
//         direction === 'horizontal' && 'flex-row',
//         // if vertical, add flex-col
//         direction === 'vertical' && 'flex-col',
//         className
//       )}
//     >
//       <label className="block font-semibold text-gray-700 text-[16px] mb-2" htmlFor={name}>
//         {label}
//       </label>
//       <div className="flex">
//         <Field
//           onBlur={onBlur}
//           type="number"
//           id={name}
//           value={defaultValue ? defaultValue : field.value}
//           name={name}
//           disabled={disabled}
//           // min={min}
//           // max={max}
//           // onChange={handleChange}
//           onKeyDown={handleKeyDown}
//           className={`
//             border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
//             border-2
//             text-black
//             ${disabled ? 'bg-[#EAECEE]' : ''}
//             rounded
//             px-3
//             w-full
//             h-[40px]
//             outline-none
//           `}
//           component={({ /* form, */ ...props }) => {
//             return (
//               <Input
//                 {...props}
//                 onChange={handleChange}
//                 className={cn(
//                   // 'border-0',
//                   // 'outline-none',
//                   // 'h-[40px]',
//                   // hasError ? 'border-red-500' : 'border-[#9CA3AF]'
//                   'text-right w-full',
//                   inputClassName,
//                   hasError ? 'border-red-500' : 'border-[#9CA3AF]'
//                 )}
//               />
//             );
//           }}
//         />
//       </div>
//       {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
//     </div>
//   );
// }

import { cn } from '@/lib/utils';
import { ErrorMessage, Field, useField } from 'formik';
import { ChangeEvent } from 'react';

interface InputProps {
  label: string;
  name: string;
  onChange?: (value: number) => void | number | Promise<void | number>;
  className?: string;
  inputClassName?: string;
  direction?: 'vertical' | 'horizontal';
}

export default function InputNumber({
  label,
  name,
  className,
  inputClassName,
  direction = 'vertical',
  onChange,
}: InputProps) {
  const [field, meta, helpers] = useField(name);

  const hasError = meta.touched && meta.error;
  const { setValue } = helpers;

  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    let value = +e.target.value;
    // Si hay una función onChange, se aplica
    if (onChange) {
      value = (await onChange(value)) || value;
    }

    // Se actualiza el valor en Formik
    setValue(value);
  };

  return (
    <div
      className={cn(
        'flex',
        // if horizontal, add flex-row
        direction === 'horizontal' && 'w-full flex-row justify-between items-center ',
        // if vertical, add flex-col
        direction === 'vertical' && 'flex-col',
        className
      )}
    >
      <label className="block text-gray-700 mb-2 font-semibold" htmlFor={name}>
        {label}
      </label>
      <div className="flex flex-col relative items-center">
        <Field
          type="number"
          id="emailLogin"
          value={field.value}
          name={name}
          onChange={handleChange}
          // placeholder={placeholder}
          onKeyDown={(event: any) => {
            if (['e', 'E', '+', '-', '.'].includes(event.key)) return event.preventDefault();
            return null;
          }}
          className={cn(
            `
            border ${hasError ? 'border-red-500' : 'border-gray-[#CED4DA] '}
            text-black 
            rounded
            px-3 
            h-[40px] 
            w-full
            outline-none 
          `,
            inputClassName
          )}
        />
        {hasError && direction === 'horizontal' && (
          <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />
        )}
      </div>
      {hasError && direction === 'vertical' && (
        <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />
      )}
    </div>
  );
}
