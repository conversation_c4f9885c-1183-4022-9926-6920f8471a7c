'use client';
/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Image } from '@chakra-ui/react';
import genesis from '@/assets/home/<USER>/genesis_f.png';
import ReserveNowBtn from '../ReserveNowBtn';
import { Formik } from 'formik';
// import axios from 'axios';
// import Swal from 'sweetalert2';
import { contactUSSchema } from '@/schemas/contactUSSchema';
import CustomInputUS from '../Inputs/CustomInputUS';
import SelectInputUS from '../Inputs/SelectInputUS';
import Swal from 'sweetalert2';
import useHandleResize from '@/components/useHandleResize';
import {
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  US_STATES_DEFAULT_CITIES,
  US_DEFAULT_STATE_OPTIONS,
  USSTATES,
  vehiclesFormOptions,
} from '../../[nameCar]/data/data';
import { sendToHubspotUS } from '../../actions/submit-form';

const sourceOptions = [
  { label: 'Select an option', value: '' },
  { label: 'Facebook', value: 'Facebook' },
  { label: 'Instagram', value: 'Instagram' },
  { label: 'Google', value: 'Google' },
  { label: 'Referral', value: 'Referral' },
  { label: 'LinkedIn', value: 'LinkedIn' },
  { label: 'Physical Flyer', value: 'Physical Flyer' },
  { label: 'Billboard', value: 'Billboard' },
  { label: 'Other', value: 'Other' },
];

export default function ReserveForm() {
  const onSubmit = async (form: any, { resetForm }: any) => {
    try {
      form = {
        ...form,
        vehicleSelected: vehiclesFormOptions.find((vehicle) => vehicle.value === form.electric_car_usa)!
          .label,
      };

      await sendToHubspotUS(form);

      return await Swal.fire({
        title: 'Information sent',
        text: 'We will contact you as soon as possible',
        icon: 'success',
        confirmButtonText: 'Close',
      });
      // setSending(false);
    } catch (e: any) {
      // setSending(false);
      if (e.response.data.message.includes('Ya has enviado tu solicitud')) {
        return await Swal.fire({
          title: 'We will contact you soon 😁',
          // text: e.response.data.message,
          text: 'You have already sent your request, please wait for us to contact you',
          icon: 'info',
          confirmButtonText: 'Close',
        });
      }
      return await Swal.fire({
        title: 'Something went wrong',
        // text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
        text: 'Try again, if the problem persists please wait for us to fix it',
        icon: 'error',
        confirmButtonText: 'Close',
      });
    } finally {
      resetForm();
    }
  };

  const isMobile = useHandleResize({ breakpoint: 640 });

  return (
    <section id="reserve-now" className="w-full h-[max-content] min-h-[600px] py-[100px] px-6 lg:px-8 ">
      <div className="w-[100%] h-[100%] justify-center grid grid-cols-1 xl:grid-cols-2 gap-[30px]">
        <div className="w-[100%] h-[100%] flex justify-center items-center sticky order-last xl:order-first mt-[35px] xl:mt-0 ">
          <div className="w-[90%] h-[100%] xl:h-[70%] absolute">
            <svg
              className="absolute top-[-20px] left-[15%] "
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 22 22"
              fill="none"
            >
              <path
                d="M11 0L13.489 8.51098L22 11L13.489 13.489L11 22L8.51098 13.489L0 11L8.51098 8.51098L11 0Z"
                fill="#FFC130"
              />
            </svg>
            <div className="w-[12px] h-[12px] bg-[#742BFA] rounded-full absolute right-2 bottom-[25%] " />
            <div className="w-[17px] h-[17px] bg-[#BEFF6C] rounded-full absolute left-[40%] bottom-[-1rem] xl:bottom-[-2rem] " />
          </div>
          <Image
            src={genesis.src}
            w="90%"
            h={{ base: '100%', l: '70%' }}
            alt="teslaWhite"
            className="object-contain sticky "
          />
        </div>
        <div className="w-full xl:w-[95%] 2xl:w-[70%] lg:pl-[20px] xl:pl-0  h-[100%] grid xl:justify-between gap-[30px] text-[#464E5F] font-[Plus-Jakarta-Sans] order-first xl:order-last  ">
          <p className="text-[#5A00F8] text-[28px] xl:text-[40px] font-[700] text-center md:text-start ">
            Reserve your EV now
          </p>
          <p className="text-center md:text-start">
            Leave your contact information to start your application.
          </p>
          <Formik
            initialValues={{
              fullName: '',
              phone: '',
              email: '',
              electric_car_usa: vehiclesFormOptions[0].value,
              state: US_DEFAULT_STATE_OPTIONS[0].value,
              city: US_STATES_DEFAULT_CITIES[USSTATES.Florida][0].value,
              postalCode: '',
              sourceOption: sourceOptions[0].value,
            }}
            onSubmit={onSubmit}
            validationSchema={contactUSSchema}
          >
            {({ errors, touched, handleSubmit, isSubmitting, values, setFieldValue }) => {
              // console.log('errors', errors);
              return (
                <form onSubmit={handleSubmit} className="flex flex-col w-full">
                  <div className="flex flex-col md:w-[300px] 2xl:w-full gap-[20px] sm:gap-[50px] xl:gap-[30px] xl:justify-between mt-[30px] font-[600]">
                    <div className="flex flex-col sm:flex-row gap-[30px] w-full">
                      <CustomInputUS
                        name="fullName"
                        label="Full Name"
                        errors={errors}
                        touched={touched}
                        type="text"
                      />

                      <CustomInputUS
                        name="phone"
                        label="Cell phone"
                        errors={errors}
                        touched={touched}
                        type="number"
                      />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-[30px]">
                      <CustomInputUS
                        name="email"
                        label="Email"
                        errors={errors}
                        touched={touched}
                        type="text"
                      />
                      <SelectInputUS
                        name="electric_car_usa"
                        label="Choose Your EV"
                        errors={errors}
                        touched={touched}
                        options={vehiclesFormOptions}
                        onChange={(e) => {
                          setFieldValue('electric_car_usa', e.target.value);
                        }}
                      />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-[30px]">
                      <SelectInputUS
                        name="state"
                        label="Choose Your State"
                        errors={errors}
                        touched={touched}
                        options={getUSStatesOptions()}
                        onChange={(e) => {
                          const selectedState = e.target.value;
                          const city = getUSCitiesBasedOnState(selectedState)[0].value;
                          setFieldValue('state', selectedState);
                          setFieldValue('city', city);
                        }}
                      />
                      <SelectInputUS
                        name="city"
                        label="Choose Your City"
                        errors={errors}
                        touched={touched}
                        options={values.state ? getUSCitiesBasedOnState(values.state) : []}
                        onChange={(e) => {
                          setFieldValue('city', e.target.value);
                        }}
                      />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-[30px]">
                      <CustomInputUS
                        name="postalCode"
                        label="Postal Code"
                        errors={errors}
                        touched={touched}
                        type="text"
                      />
                      <SelectInputUS
                        name="sourceOption"
                        label="How did you hear about us?"
                        errors={errors}
                        touched={touched}
                        options={sourceOptions}
                        onChange={(e) => {
                          setFieldValue('sourceOption', e.target.value);
                        }}
                      />
                    </div>
                  </div>
                  <div className="xl:w-[270px] mt-[35px] ">
                    <ReserveNowBtn
                      full={!!isMobile}
                      text="Submit"
                      isDefault={false}
                      type="submit"
                      isSubmitting={isSubmitting}
                      className="w-[270px]"
                    />
                  </div>
                </form>
              );
            }}
          </Formik>
        </div>
      </div>
    </section>
  );
}
