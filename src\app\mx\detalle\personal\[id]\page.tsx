import Navbar from '@/components/NavBar/Navbar';
import VehicleDetailPersonal from './detailClient';
import NotFoundMX from '@/app/NotFoundMX';
import { getPersonalById } from '@/middlewares/getVehiclesFirebase';
// import { getVehicleCache } from '@/app/getters/getByIdPersonal';

interface DetailPersonalProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: DetailPersonalProps) {
  try {
    const vehicle = await getPersonalById(params.id);
    if (!vehicle)
      return {
        title: 'Not found',
        description: 'Page not found',
      };
    return {
      title: vehicle.name + ' | OCN',
      description: vehicle.description,
      openGraph: {
        images: [vehicle.images[0]],
      },
    };
  } catch (error) {
    return {
      title: 'Not found',
      description: 'Page not found',
    };
  }
}

export default async function DetailPersonal({ params }: DetailPersonalProps) {
  // const vehicle = await getVehicle(params.id);
  const vehicleFirebase = await getPersonalById(params.id);
  if (!vehicleFirebase) return <NotFoundMX />;
  return (
    <>
      <Navbar />
      <VehicleDetailPersonal data={vehicleFirebase} />
    </>
  );
}
