'use client';
import { Divider, Flex, Heading, Text } from '@chakra-ui/react';
import NewsList from './NewsList';
import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import { useEffect } from 'react';

export default function News() {
  useEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
      <Navbar country="mx" />

      <Flex
        w="100%"
        h="100%"
        minH="95vh"
        alignItems="center"
        flexDir="column"
        sx={{ '> *:not(:last-child)': { mb: 5 } }}
        bgColor="#FAFAFF"
        color="text.main"
      >
        <Flex
          w="80%"
          h={250}
          justifyContent="center"
          textAlign="start"
          flexDir="column"
          sx={{ '> *:not(:last-child)': { mb: 30 } }}
          fontFamily="Plus-Jakarta-Sans"
        >
          <Heading fontSize="30px" fontWeight={700} fontFamily="Plus-Jakarta-Sans">
            Noticias
          </Heading>

          <Divider w="42px" border={'2px solid #9E8EFF'} borderRadius="3px" />

          <Text fontSize="15px">Articulos con noticias de OCN y noticias relacionadas</Text>
        </Flex>

        {/* Aqui se encuentran las cards de las noticias destacadas */}
        <NewsList />
      </Flex>
      <Footer footerBgColor="footer.main" />
    </>
  );
}
