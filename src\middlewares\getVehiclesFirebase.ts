import { collection, getDocs, limit, query, where } from 'firebase/firestore';
import { IVehicles } from './interfaces';
import { dbMain } from '@/services/firebase';
import { cache } from 'react';

export const get3TabPersonalVehicles = cache(async () => {
  const data: IVehicles[] = [];
  const q = query(collection(dbMain, 'vehiclesMain'), where('plan', '==', 'Personal'), limit(3));
  const querySnapshot = await getDocs(q);
  querySnapshot.forEach((doc) => {
    const vehicle = doc.data();
    const vehicleData: IVehicles = {
      name: vehicle.name,
      description: vehicle.description,
      payment: vehicle.payment,
      images: vehicle.images,
      plan: vehicle.plan,
      security: vehicle.security,
      seats: vehicle.seats,
      motor: vehicle.motor,
      transmission: vehicle.transmission,
      liters: vehicle.liters,
      interior: vehicle.interior,
      exterior: vehicle.exterior,
      aditionalData: vehicle.aditionalData,
      specialDetails: vehicle.specialDetails,
      id: doc.id.trim(),
      durationMonths: vehicle.durationMonths,
      planOptions: vehicle.planOptions,
      paymentOptions: vehicle.paymentOptions,
    };
    data.push(vehicleData);
  });

  // console.log(data)
  return data;
});

export const get3TabPlatformVehicles = cache(async () => {
  const data: IVehicles[] = [];
  const q = query(collection(dbMain, 'vehiclesMain'), where('plan', '==', 'Plataforma'), limit(3));
  const querySnapshot = await getDocs(q);
  querySnapshot.forEach((doc) => {
    const vehicle = doc.data();
    const vehicleData: IVehicles = {
      name: vehicle.name,
      description: vehicle.description,
      payment: vehicle.payment,
      images: vehicle.images,
      plan: vehicle.plan,
      security: vehicle.security,
      seats: vehicle.seats,
      motor: vehicle.motor,
      transmission: vehicle.transmission,
      liters: vehicle.liters,
      interior: vehicle.interior,
      exterior: vehicle.exterior,
      aditionalData: vehicle.aditionalData,
      specialDetails: vehicle.specialDetails,
      id: doc.id.trim(),
      durationMonths: vehicle.durationMonths,
      planOptions: vehicle.planOptions,
      paymentOptions: vehicle.paymentOptions,
    };
    data.push(vehicleData);
  });

  // console.log(data)
  return data;
});

export const getAllPersonalVehicles = cache(async () => {
  const data: IVehicles[] = [];
  const q = query(collection(dbMain, 'vehiclesMain'), where('plan', '==', 'Personal'));
  const querySnapshot = await getDocs(q);
  querySnapshot.forEach((doc) => {
    const vehicle = doc.data();
    const vehicleData: IVehicles = {
      name: vehicle.name,
      description: vehicle.description,
      payment: vehicle.payment,
      images: vehicle.images,
      plan: vehicle.plan,
      security: vehicle.security,
      seats: vehicle.seats,
      motor: vehicle.motor,
      transmission: vehicle.transmission,
      liters: vehicle.liters,
      interior: vehicle.interior,
      exterior: vehicle.exterior,
      aditionalData: vehicle.aditionalData,
      specialDetails: vehicle.specialDetails,
      id: doc.id.trim(),
      durationMonths: vehicle.durationMonths,
      planOptions: vehicle.planOptions,
      paymentOptions: vehicle.paymentOptions,
    };
    data.push(vehicleData);
  });

  return data;
});

export const getAllPlatformVehicles = cache(async () => {
  const data: IVehicles[] = [];
  const q = query(collection(dbMain, 'vehiclesMain'), where('plan', '==', 'Plataforma'));
  const querySnapshot = await getDocs(q);
  querySnapshot.forEach((doc) => {
    const vehicle = doc.data();
    const vehicleData: IVehicles = {
      name: vehicle.name,
      description: vehicle.description,
      payment: vehicle.payment,
      images: vehicle.images,
      plan: vehicle.plan,
      security: vehicle.security,
      seats: vehicle.seats,
      motor: vehicle.motor,
      transmission: vehicle.transmission,
      liters: vehicle.liters,
      interior: vehicle.interior,
      exterior: vehicle.exterior,
      aditionalData: vehicle.aditionalData,
      specialDetails: vehicle.specialDetails,
      id: doc.id.trim(),
      durationMonths: vehicle.durationMonths,
      planOptions: vehicle.planOptions,
      paymentOptions: vehicle.paymentOptions,
    };
    data.push(vehicleData);
  });

  return data;
});

export const getPersonalById = cache(async function (id: string) {
  const q = query(collection(dbMain, 'vehiclesMain'), where('id', '==', id), limit(3));
  const querySnapshot = await getDocs(q);
  if (querySnapshot.docs.length === 0) {
    // Manejar el caso en el que no se encuentra ningún vehículo con el ID proporcionado.
    return null;
  }

  // Obtener el primer documento que cumple con el criterio de búsqueda (debería ser único).
  const doc = querySnapshot.docs[0].data() as IVehicles;

  return doc;
});

export const getPlatformById = cache(async function (id: string) {
  const q = query(
    collection(dbMain, 'vehiclesMain'),
    where('plan', '==', 'Plataforma'),
    where('id', '==', id),
    limit(3)
  );
  const querySnapshot = await getDocs(q);
  if (querySnapshot.docs.length === 0) {
    // Manejar el caso en el que no se encuentra ningún vehículo con el ID proporcionado.
    return null;
  }

  // Obtener el primer documento que cumple con el criterio de búsqueda (debería ser único).
  const doc = querySnapshot.docs[0].data() as IVehicles;

  return doc;
});
