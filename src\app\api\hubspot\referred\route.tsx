import { HUBSPOT_TOKEN, HUBSPOT_URL } from '@/constants';
import axios from 'axios';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  const body = await req.json();

  const origin = req.headers.get('origin');

  if (!origin)
    return NextResponse.json(
      { message: 'Hubo un error' /* , error: 'Faltan datos necesarios' */ },
      { status: 400 }
    );

  const selfServerUrl = `${origin}/api/hubspot`;

  const createContactUrl = `${selfServerUrl}/createContact`;

  const updateContactUrl = `${selfServerUrl}/updateContact`;

  try {
    const data = {
      firstName: body.firstname || body.firstName,
      lastName: body.lastname || body.lastName,
      email: body.email,
      phone: body.phone,
      city: body.city || body.ciudad__con_selector_,
      referrer: {
        name: body.nombre_de_quien_refiere,
        phone: body.telefono_de_quien_refiere,
        email: body.email_de_quien_refiere,
      },
      source: body.source,
      ...body,
    };
    const response1 = await axios.post(`${createContactUrl}`, data);

    const requestId = response1.data.requestId;
    const hubspotId = response1.data.hubspotId;

    const response2 = await axios.post(`${updateContactUrl}`, { requestId, hubspotId, ...data });

    try {
      await axios.patch(
        `${HUBSPOT_URL}/${hubspotId}`,
        {
          properties: body,
        },
        {
          headers: {
            Authorization: `Bearer ${HUBSPOT_TOKEN}`,
          },
        }
      );
    } catch (error) {
      console.log('error', error);
    }

    return NextResponse.json({ message: 'ok', data: response2.data });
  } catch (error: any) {
    if (error.response.data.message.includes('already exists')) {
      return NextResponse.json(
        { message: 'Ya has enviado tu solicitud, espera a que te contactemos', error },
        { status: 409 }
      );
    }

    return NextResponse.json({ message: 'Hubo un error', error }, { status: 500 });
  }
}
