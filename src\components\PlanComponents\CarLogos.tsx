import { Box, Flex } from '@chakra-ui/layout';
import mazda from '@/assets/plan/MazdaLogo.png';
import kia from '@/assets/plan/KIALogo.png';
import chirey from '@/assets/plan/ChireyLogo.png';
import mg from '@/assets/plan/MGLogo.png';
import chevrolet from '@/assets/plan/Chevroletlogo.png';
import nissan from '@/assets/plan/NissanLogo.png';

const logos = [mazda, kia, chirey, mg, chevrolet, nissan];

export default function CarLogos() {
  return (
    <Flex
      as="span"
      w="100%"
      h="100%"
      minH="170px"
      py={{ base: '25px', md: 0 }}
      display={{ base: 'grid', md: 'flex' }}
      gridTemplateColumns={{ base: 'repeat(3, minmax(auto, auto))' }}
      alignItems="center"
      justifyContent={{ base: 'space-around', md: 'space-evenly', l: 'center' }}
      sx={{ '> *:not(:last-child)': { mx: { base: 1, l: '40px' } } }}
      bgColor="bgColor"
    >
      {logos.map((logo, i) => (
        <Box
          key={i}
          w="80px"
          h="50px"
          backgroundImage={logo.src}
          backgroundSize="contain"
          backgroundPosition="center"
          backgroundRepeat="no-repeat"
        />
      ))}
    </Flex>
  );
}
