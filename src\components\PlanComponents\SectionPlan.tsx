/* eslint-disable jsx-a11y/alt-text */
import { Box, Flex, Grid, Heading, Image, Text } from '@chakra-ui/react';

const personalColor = '#028CF3';
const platformGreenColor = '#43D17D';
const platformPurpleColor = 'purple.strong';

export default function SectionPlan({ isPersonalPlan }: { isPersonalPlan: boolean }) {
  return (
    <Flex
      as="section"
      w="100%"
      h={{ base: 'max-content', l: '700px' }}
      pt={{ base: '30px', l: 0 }}
      justifyContent="center"
      overflow="hidden"
      bgColor="bgColor"
    >
      <Flex
        w="90%"
        h="100%"
        justifyContent="center"
        flexDir={{ base: 'column-reverse', l: 'row' }}
        sx={{ '> *:not(:last-child)': { mb: 5 } }}
      >
        <Flex
          w={{ base: '100%', l: '50%' }}
          h="100%"
          // pt={{ base: "30px" }}
          justifyContent={{ base: 'start', l: 'center' }}
          alignItems="center"
        >
          <Box w={{ base: '320px', l: '405px' }} h="520px" position="relative" borderRadius="30px">
            <Box
              w={{ base: '115px', l: '165px' }}
              h={{ base: '163px', l: '203px' }}
              backgroundImage={
                isPersonalPlan
                  ? // dots
                    'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fpersonal%2Fdots.png?alt=media&token=529b7551-c720-40d5-9ce7-6cc707c16408'
                  : 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fplatform%2FdotsH.png?alt=media&token=fc6714d8-b8d5-43dd-9bc5-509e1141eb50'
              }
              backgroundRepeat="no-repeat"
              backgroundSize="contain"
              backgroundPosition="center"
              position="absolute"
              left={{ base: '-15px', l: '-25px' }}
              top={{ base: '25px', l: '-25px' }}
            />
            <Image
              w="100%"
              top={{ base: '55px', l: 0 }}
              src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Finterior.jpg?alt=media&token=75a78ac3-b237-4671-a84b-4a0feabff6cf"
              borderRadius="25px"
              position="absolute"
              zIndex={1}
            />
            <Box
              w={{ base: '100px', l: '177px' }}
              h={{ base: '100px', l: '177px' }}
              bgColor={isPersonalPlan ? personalColor : platformGreenColor}
              borderRadius="50%"
              position="absolute"
              right={{ base: '-40px', l: '-75px' }}
              bottom={{ base: '15px', l: '-75px' }}
            />
          </Box>
        </Flex>
        <Flex
          w={{ base: '100%', l: '50%' }}
          h="100%"
          alignItems="center"
          pl={{ md: '10px', l: 0 }}
          justifyContent={{ base: 'start', l: 'center' }}
        >
          <Grid
            w={{ base: '90%', md: '75%', lg: '80%' }}
            sx={{ '> *:not(:last-child)': { mb: 5 } }}
            color="text.main"
          >
            <Text
              color={isPersonalPlan ? personalColor : platformPurpleColor}
              fontSize="20px"
              fontFamily="Plus-Jakarta-Sans"
              fontWeight={500}
            >
              {isPersonalPlan ? 'Plan personal' : 'Plan plataformas'}
            </Text>
            <Heading lineHeight="60px" fontSize="40px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
              La forma más sencilla de adquirir un auto
            </Heading>
            <Text lineHeight="30px" fontFamily="Plus-Jakarta-Sans" fontWeight={400}>
              {isPersonalPlan ? (
                <>
                  ¡Inicia el año estrenando uno de nuestros vehículos! <br /> Sin enganches ni depósitos, con
                  todos los servicios incluidos, grandes beneficios y al mejor precio posible.
                </>
              ) : (
                <>
                  Estrena un auto nuevo sin enganches ni depósitos en garantía para continuar trabajando en
                  plataformas de movilidad (Uber, Didi, etc.), y con nuestro servicio todo incluido te
                  despreocupas de los gastos fuertes y costos a futuro.
                </>
              )}
            </Text>
          </Grid>
        </Flex>
      </Flex>
    </Flex>
  );
}
