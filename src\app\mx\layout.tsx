import { SourceTrackingComponent } from '@/components/SourceTrackingComponent';
import WhatsappWidget from '@/components/WhatsappWidget/WhatsappWidget';
import { Metadata } from 'next';
import { headers } from 'next/headers';

export const metadata: Metadata = {
  title: 'OCN | Inicio',
  description: 'OCN te ofrece un auto nuevo a la medida, 100% online, rapido, seguro y con todo incluido.',
};

const excludedRoutes = ['/dealershipreferrals', '/customers'];

export default function MXLayout({ children }: { children: React.ReactNode }) {
  const headersList = headers();
  const pathname = headersList.get('x-pathname');
  // read the custom x-url header
  // const headerUrl = headersList.get('x-url') || 'a';
  // const domain = headersList.get('host') || '';
  // const fullUrl = headersList.get('referer') || '';
  // const headerUrl = headersList.get('x-url') || '';

  const displayWhatsappWidget = !excludedRoutes.some((route) => pathname?.endsWith(route));

  return (
    <>
      <SourceTrackingComponent />
      {children}
      {displayWhatsappWidget && <WhatsappWidget />}
    </>
  );
}
