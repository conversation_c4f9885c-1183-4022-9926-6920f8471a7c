'use client';
import CustomButton from '@/components/CustomButton';
import { COUNTRY_DATA } from '@/constants';
import { useState } from 'react';
import { createPortal } from 'react-dom';
import Swal from 'sweetalert2';
// import withReactContent from 'sweetalert2-react-content';

// const MySwal = withReactContent(Swal);

const CITIES = [
  'São Paulo',
  'Rio de Janeiro',
  'Belo Horizonte',
  'Brasília',
  'Salvador',
  'Curitiba',
  'Porto Alegre',
  'Recife',
  'Fortaleza',
  'Manaus',
];

export default function ModalForm() {
  const [open, setOpen] = useState(false);
  const [form, setForm] = useState({
    nome: '',
    sobrenome: '',
    email: '',
    telefone: '',
    cidade: '',
    mensagem: '',
  });
  const [sending, setSending] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleClose = () => setOpen(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSending(true);

    try {
      const url = '/api/hubspot/createContact';
      const data = {
        firstname: form.nome,
        lastname: form.sobrenome,
        email: form.email,
        phone: form.telefone,
        city: form.cidade,
        country: 'br',
        message: form.mensagem,
        fuente: 'Pagina OCN',
        source: localStorage.getItem('source') || '',
      };
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      setOpen(false);
      await Swal.fire({
        // title: 'Información enviada',
        // text: 'Te contactáremos lo más pronto posible 😁',
        title: 'Informação enviada',
        text: 'Entraremos em contato com você o mais breve possível 😁',
        icon: 'success',
        confirmButtonText: 'Cerrar',
      });
      return null;
    } catch (error: any) {
      console.log('error: ', error);
      // check if error is 409, if so, show alert that user already exists
      if (error.response?.data?.message.includes('Ya has enviado tu solicitud')) {
        return await Swal.fire({
          title: 'Entraremos em contato com você em breve 😁',
          text: 'Recebemos sua solicitação. Você pode entrar em contato conosco pelo WhatsApp.',
          icon: 'info',
          confirmButtonText: 'Fechar',
          showCancelButton: true,
          cancelButtonText: `Whatsapp`,
          customClass: {
            cancelButton: 'whatsapp-swal-btn',
          },
        }).then((result) => {
          if (result.isDismissed) {
            // window.open('https://api.whatsapp.com/send?phone=5215590632045', '_blank');
          }
        });
      }
      return null;
    } finally {
      setSending(false);
    }
  };

  return (
    <>
      <CustomButton
        message={COUNTRY_DATA.BR.home.hero.button}
        className="w-full h-full btn-purple"
        aria-label="Ver autos"
        // type="submit"
        handleClick={() => setOpen(true)}
      >
        {COUNTRY_DATA.BR.home.hero.button}
      </CustomButton>
      {open &&
        createPortal(
          <>
            <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/30">
              <div className="bg-white rounded-xl shadow-lg w-[95vw] max-w-md p-6 relative">
                <button
                  className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 text-2xl font-bold"
                  onClick={handleClose}
                  aria-label="Fechar"
                >
                  ×
                </button>
                <h2 className="text-xl font-bold mb-6 text-gray-900">Preencha o formulário</h2>
                <form className="flex flex-col gap-4" onSubmit={handleSubmit}>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="nome">
                      Nome
                    </label>
                    <input
                      id="nome"
                      name="nome"
                      type="text"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      placeholder="Nome"
                      value={form.nome}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="sobrenome">
                      Sobrenome
                    </label>
                    <input
                      id="sobrenome"
                      name="sobrenome"
                      type="text"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      placeholder="Sobrenome"
                      value={form.sobrenome}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="email">
                      Email
                    </label>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      placeholder="<EMAIL>"
                      value={form.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="telefone">
                      Telefone
                    </label>
                    <input
                      id="telefone"
                      name="telefone"
                      type="tel"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      placeholder="Telephone number"
                      value={form.telefone}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="cidade">
                      Cidade
                    </label>
                    <select
                      id="cidade"
                      name="cidade"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      value={form.cidade}
                      onChange={handleChange}
                      required
                    >
                      <option value="">Enter your city</option>
                      {CITIES.map((city) => (
                        <option key={city} value={city}>
                          {city}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1" htmlFor="mensagem">
                      Mensagem
                    </label>
                    <textarea
                      id="mensagem"
                      name="mensagem"
                      className="w-full border rounded-lg px-3 py-2 outline-none focus:ring-2 focus:ring-[#7000FF] bg-gray-50"
                      placeholder="Escreva sua pergunta (opcional)"
                      value={form.mensagem}
                      onChange={handleChange}
                      rows={3}
                    />
                  </div>
                  <button
                    type="submit"
                    className="w-full mt-2 bg-[#7000FF] hover:bg-[#4B00B3] text-white font-semibold rounded-lg py-2 text-lg transition"
                    disabled={sending}
                  >
                    {sending ? 'Enviando...' : 'Enviar'}
                  </button>
                </form>
              </div>
            </div>
          </>,

          document.body
        )}
    </>
  );
}
