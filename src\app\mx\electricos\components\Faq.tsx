'use client';
import { Accordion, Flex, Text } from '@chakra-ui/react';
import React, { useState } from 'react';
import ElectricQuestions from './ElectricQuestions';

const Faq = () => {
  const questions = [
    {
      title: '¿En dónde se carga mi OCN?',
      answer:
        'Con cualquier modelo de OCN, podrás recorrer distancias largas sin tener que detenerte para cargar la batería gracias a su alto grado de recuperación de energía. Además, tendrás un centro de carga propio en tu casa y así puedas disfrutar de un rendimiento prolongado.',
    },
    {
      title: '¿Cuánto cuesta cargar mi OCN?',
      answer:
        'OCN te ofrece la instalación de un centro de recarga en casa incluida en la compra del automóvil eléctrico.',
    },
    {
      title: '¿Cuánta energía ahorro con mi OCN?',
      answer:
        'Podrás ahorrar un promedio de $5,000 mensuales *. Cualquier usuario de un OCN NETA economiza desde 300 km hasta 600 km sin detenerse. Eso equivale a un tanque lleno de gasolina en un auto común, con el que podrías viajar desde de Ciudad de México a Acapulco o de Ciudad de México hasta Guadalajara sin problemas. * Estimación basada en un recorrido de 30 Km diarios por la Ciudad de México con un auto de 4 cilindros y una duración de 45 minutos',
    },
  ];

  const [, setIsOpen] = useState(() => {
    const initial = new Array(questions.length).fill(false);
    initial[0] = true;
    return initial;
  });

  const toggleOpen = (index: any) => {
    setIsOpen((prev) => {
      const newState = [...prev];
      newState[index] = !prev[index];
      return newState;
    });
  };

  return (
    <>
      <Flex
        w="100%"
        minH="648px"
        id="about"
        bgColor="white"
        pt="100px"
        pb="140px"
        flexDir="column"
        alignItems="center"
        px={{ base: '30px', md: '150px' }}
        gap="30px"
        color="#1A1A1A"
        fontFamily="Plus-Jakarta-Sans"
        textAlign="center"
      >
        <Text fontSize={{ md: '40px', base: '24px' }} fontWeight={700}>
          TODO SOBRE TU OCN
        </Text>

        <Flex w="100%" h="100%" justifyContent="center" justify={'center'} transition="ease-in .1s">
          <Accordion w="100%" display="flex" flexDir="column" alignItems="center" allowMultiple>
            {questions.map((q, i) => {
              return (
                <ElectricQuestions
                  key={i}
                  question={q.title}
                  answer={q.answer}
                  toggleOpen={() => toggleOpen(i)}
                />
              );
            })}
          </Accordion>
        </Flex>
      </Flex>
    </>
  );
};

export default Faq;
