'use client';
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Button, Flex, Image, SimpleGrid, Text } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import CarouselElectricVehicles from './Carrousel';
import vehicleData, { DisplayVehicleProps } from './vehicleData';

export default function Vehicles() {
  return (
    <Flex
      flexDir="column"
      bgColor="#E5E5E5"
      id="nuestros-vehiculos"
      alignItems="center"
      py="90px"
      px={{ base: '10px', md: '70px' }}
      // minH="1150px"
      gap="30px"
    >
      <Text fontSize={{ md: '40px', base: '24px' }} fontWeight={700} textAlign="center">
        NUESTROS VEHÍCULOS
      </Text>
      <SimpleGrid columns={{ base: 1, lg: 2 }} w="100%" h="100%">
        {/* {vehicleData.map((v, i) => (
          <DisplayVehicle key={i} shortName={v.shortName} mainImage={v.mainImage} modalInfo={v.modalInfo} />
        ))} */}
      </SimpleGrid>
    </Flex>
  );
}

function DisplayVehicle({ shortName, mainImage, modalInfo }: DisplayVehicleProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const toggleModal = () => {
    setIsModalOpen(!isModalOpen);
  };
  const [showLink, setShowLink] = useState(false);
  useEffect(() => {
    const handleResize = () => {
      setShowLink(window.innerWidth < 570);
    };
    window.addEventListener('resize', handleResize);
    handleResize();
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSmoothScroll = (event: React.MouseEvent<HTMLAnchorElement>, targetId: string): void => {
    event.preventDefault();
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      window.scrollTo({
        top: targetElement.offsetTop,
        behavior: 'smooth',
      });
      window.history.pushState(null, '', `#${targetId}`);
    }
  };

  return (
    <Flex w="100%" justifyContent="space-between" alignItems="center" position="relative">
      <Image src={mainImage} objectFit="cover" alt="car img" />
      <Text
        pt="45%"
        color="black"
        position="absolute"
        zIndex={1}
        left={{ base: '2%', md: '4%' }}
        fontSize={{ base: '18px', sm: '32px' }}
        fontWeight={700}
      >
        {shortName}
      </Text>
      <Flex
        pt="45%"
        position="absolute"
        zIndex={1}
        right={{ base: '2%', md: '4%' }}
        gap={{ base: '5px', md: '20px' }}
        fontWeight={400}
      >
        <Button
          fontSize={{ base: '12px', sm: '16px' }}
          h={{ base: '30px', sm: '40px' }}
          bgColor="transparent"
          px={{ base: '8px', sm: '14px' }}
          border="1px solid"
          borderColor={shortName === 'OCN S' ? '#E5E5E5' : '#1A1A1A'}
          color={shortName === 'OCN S' ? '#E5E5E5' : '#1A1A1A'}
          borderRadius="25px"
          _hover={{
            bgColor: 'transparent',
          }}
          onClick={toggleModal}
        >
          {showLink ? <>INFORMACIÓN</> : <>MÁS INFORMACIÓN</>}
        </Button>
        {/* <ModalElectrics /> */}
        <a onClick={(e) => handleSmoothScroll(e, 'reservar')}>
          <Button
            fontSize={{ base: '12px', sm: '16px' }}
            h={{ base: '30px', sm: '40px' }}
            // bg="#5A00F8"
            className="bg-[#5A00F8]"
            fontWeight={400}
            color="white"
            px={{ base: '8px', sm: '14px' }}
            borderRadius="25px"
            _hover={{
              bgColor: 'purple.soft',
            }}
          >
            {showLink ? <>RESERVAR</> : <>RESERVA AHORA</>}
          </Button>
        </a>
      </Flex>
      {isModalOpen && (
        <CarouselElectricVehicles shortName={shortName} toggleModal={toggleModal} modalInfo={modalInfo} />
      )}
    </Flex>
  );
}
