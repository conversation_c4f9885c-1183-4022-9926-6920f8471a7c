// /* eslint-disable array-bracket-newline */
// import tesla1 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla1.webp';
// import tesla2 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla2.webp';
// import tesla3 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla3.webp';
// import tesla4 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla4.webp';
// import tesla5 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla5.webp';
// import tesla6 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla6.webp';
// import tesla7 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/Tesla7.webp';
// // import tesla8 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 8.webp';
// // import tesla9 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 9.webp';
// // import tesla10 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 10.webp';
// // import tesla11 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 11.webp';
// import tesla12 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 12.webp';
// import tesla14 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG14.webp';
// import tesla15 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG15.webp';
// import tesla16 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG16.webp';
// import tesla17 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG17.webp';
// import tesla18 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG18.webp';
// // import tesla19 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG19.webp';
// import tesla20 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG20.webp';
// import { IVehicleData } from './data';
// import teslaY from '@/assets/home/<USER>/imageCards/tesla-card.png';

// const teslaYData: IVehicleData = {
//   url: 'tesla-model-y',
//   name: 'Tesla Model Y Long Range',
//   cardName: 'Tesla Model Y Long Range',
//   cardImage: teslaY.src,
//   description: `The Tesla Model Y is one of Tesla's most innovative cars, as it is a sleek and spacious electric SUV with great passenger and cargo carrying capacity.`,
//   features: {
//     range: '336 mi',
//     acceleration: '4.8seg 0-60 mph',
//     battery: '250 KW',
//     engine: '384 hp (Dual Motor)',
//     charge: '7.5 hours',
//     airbags: '6',
//   },
//   payment: 410,
//   payments: {
//     1: 1200,
//     2: 1000,
//     3: 800,
//     4: 600,
//   },
//   mainFeatures: {
//     inside: [
//       'Tendency with autopilot and rests on trips or heavy moments.',
//       'Room for up to 7 passengers with optional third row & Max Cargo Volume 76 cu ft.',
//       'With a 15-inch touch screen, immersive sound system and an expansive all-glass roof that creates extra headroom and provides a seamless view of the sky.',
//     ],
//     abroad: [
//       'Rear side and front 360° cameras provide maximum visibility, helping to avoid potential collisions and better parking.',
//     ],
//   },
//   images: [
//     tesla1,
//     tesla2,
//     tesla3,
//     tesla4,
//     tesla5,
//     tesla6,
//     tesla7,
//     // tesla8,
//     // tesla9,
//     // tesla10,
//     // tesla11,
//     tesla12,
//     tesla14,
//     tesla15,
//     tesla16,
//     tesla17,
//     tesla18,
//     // tesla19,
//     tesla20,
//   ],
// };

// export default teslaYData;

// /* eslint-disable array-bracket-newline */
// // import tesla1 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla1.webp';
// // import tesla2 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla2.webp';
// // import tesla3 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla3.webp';
// // import tesla4 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla4.webp';
// // import tesla5 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla5.webp';
// // import tesla6 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla6.webp';
// // import tesla7 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/tesla7.webp';
// // // import tesla8 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 8.webp';
// // // import tesla9 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 9.webp';
// // // import tesla10 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 10.webp';
// // // import tesla11 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 11.webp';
// // // import tesla12 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG 12.webp';
// // // import tesla14 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG14.webp';
// // // import tesla15 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG15.webp';
// // // import tesla16 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG16.webp';
// // // import tesla17 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG17.webp';
// // // import tesla18 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG18.webp';
// // // import tesla19 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG19.webp';
// // // import tesla20 from '@/assets/home/<USER>/vehicleImages/tesla-model-y/IMG20.webp';
// // import { IVehicleData } from './data';

// // const teslaYData: IVehicleData = {
// //   url: 'tesla-model-y',
// //   name: 'Tesla Model Y Long Range',
// //   description: `The Tesla Model Y is one of Tesla's most innovative cars, as it is a sleek and spacious electric SUV with great passenger and cargo carrying capacity.`,
// //   features: {
// //     range: '336 mi',
// //     acceleration: '4.8seg 0-60 mph',
// //     battery: '250 KW',
// //     engine: '384 hp (Dual Motor)',
// //     charge: '7.5 hours',
// //     airbags: '6',
// //   },
// //   payment: 600,
// //   payments: {
// //     1: 1200,
// //     2: 1000,
// //     3: 800,
// //     4: 600,
// //   },
// //   mainFeatures: {
// //     inside: [
// //       'Tendency with autopilot and rests on trips or heavy moments.',
// //       'Room for up to 7 passengers with optional third row & Max Cargo Volume 76 cu ft.',
// //       'With a 15-inch touch screen, immersive sound system and an expansive all-glass roof that creates extra headroom and provides a seamless view of the sky.',
// //     ],
// //     abroad: [
// //       'Rear side and front 360° cameras provide maximum visibility, helping to avoid potential collisions and better parking.',
// //     ],
// //   },
// //   images: [
// //     tesla1,
// //     tesla2,
// //     tesla3,
// //     tesla4,
// //     tesla5,
// //     tesla6,
// //     tesla7,
// //     // tesla8,
// //     // tesla9,
// //     // tesla10,
// //     // tesla11,
// //     // tesla12,
// //     // tesla14,
// //     // tesla15,
// //     // tesla16,
// //     // tesla17,
// //     // tesla18,
// //     // tesla19,
// //     // tesla20,
// //   ],
// // };

// // export default teslaYData;
