export const maxDuration = 300;
export const dynamic = 'force-dynamic';

import {
  // HUBSPOT_TOKEN,
  // HUBSPOT_URL,
  API_URL,
} from '@/constants';
import axios from 'axios';
import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  const body = await req.json();
  const data = {
    ...body,
    firstName: body.firstname || body.firstName,
    lastName: body.lastname || body.lastName,
    email: body.email,
    phone: body.phone,
    city: body.city || body.ciudad__con_selector_,
    agency: body.agency,
    requestId: '',
    country: body.country || 'mx',
    vehicleSelected: body.vehicleSelected,
    clientIpAddress: req.headers.get('x-forwarded-for'),
  };

  try {
    const getRequestToken = await axios.post(`${API_URL}/admission/requests`, data, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const requestId = getRequestToken.data.data.id;
    const hubspotId = getRequestToken.data.data?.hubspot?.id;
    return NextResponse.json({
      message: 'Hello',
      requestId,
      hubspotId,
    });
  } catch (error: any) {
    const basicInfo = error.response.data?.basicInfo;
    const requestId = error.response.data?.requestId;
    const hubspotId = error.response.data?.hubspot?.id;
    if (Object.values(basicInfo).includes(null)) {
      return NextResponse.json({ basicInfo, requestId, hubspotId }, { status: 200 });
    }
    if (error.response.data?.message?.includes('already exists')) {
      return NextResponse.json(
        { message: 'Ya has enviado tu solicitud, espera a que te contactemos', error },
        { status: 409 }
      );
    }

    return NextResponse.json({ message: 'Hubo un error', error }, { status: 500 });
  }
}
