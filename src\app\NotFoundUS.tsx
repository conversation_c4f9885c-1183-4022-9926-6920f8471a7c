'use client';
import Link from 'next/link';
import { Heading, Text, Flex } from '@chakra-ui/react';
import CustomButton from '@/components/CustomButton';
export default function NotFoundUS() {
  return (
    <>
      <Flex
        flexDir="column"
        justifyContent="center"
        bgColor="bgColor"
        h={{ base: '80vh', md: '90vh' }}
        flex={1}
        w="100%"
        alignItems="center"
        textAlign="center"
      >
        <Heading display="inline-block" as="h2" size="2xl" color={'purple.strong'}>
          404
        </Heading>
        <Text fontSize="18px" mt={3} mb={2}>
          Page Not Found
        </Text>
        <Text color={'gray.500'} mb={6}>
          The page you&apos;re looking for does not seem to exist
        </Text>
        <Link href="/us" prefetch={false}>
          <CustomButton message="Go Home" className="btn-purple"></CustomButton>
        </Link>
      </Flex>
    </>
  );
}
