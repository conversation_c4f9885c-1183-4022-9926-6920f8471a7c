import { BsCheckCircle } from 'react-icons/bs';
import Image from 'next/image';
import whiteCar from '@/assets/home/<USER>/hyundai_b.webp';
import ReserveNowBtn from '../ReserveNowBtn';

export default function Benefits() {
  // const isMobile = useHandleResize({ breakpoint: 1400 });

  return (
    <div className="w-full flex pt-[40px] md:pt-0  px-6 md:px-8 ">
      <div className="w-full flex justify-center flex-col xl:flex-row xl:rounded-[20px] ">
        <div
          className={`
            w-full 
            xl:w-[58%]
            bg-purple-800er
            p-[35px]
            md:p-[50px] 
            flex flex-col 
            rounded-t-[20px] rounded-bl-[0px]
            xl:rounded-t-[0px] xl:rounded-tl-[20px] xl:rounded-bl-[20px]
            gap-[50px]
            2xl:gap-[60px] 
            font-[Plus-Jakarta-Sans] 
            text-white
          `}
          style={{ backgroundImage: 'linear-gradient(141deg, #39009E 0%, #742BFA 100%)' }}
        >
          <p className="text-[34px] 2xl:text-[40px] text-[700] 2xl:mt-[40px]">
            The Benefits of Getting an OCN
          </p>
          <div className="w-full flex flex-col xl:flex-row mt-[20px] xl:mt-0 gap-[30px] 2xl:gap-[60px]">
            <div className="flex flex-col gap-[30px]">
              {/* 1 */}
              <div className="flex flex-col gap-[5px]">
                <div className="flex items-center gap-2">
                  <BsCheckCircle color="white" />
                  <p className="font-bold text-[18px]">Easy Online Process</p>
                </div>
                <p>
                  Submit your online application that takes {'<'}10 minutes, and get your EV in 3-5 business
                  days.
                </p>
              </div>
              {/* 2 */}
              <div className="flex flex-col gap-[5px]">
                <div className="flex items-center gap-2">
                  <BsCheckCircle size={18} color="white" />
                  <p className="font-bold text-[18px]">Maintenance Included</p>
                </div>
                <p>You won’t worry about long term costs associated with your EV.</p>
              </div>
              {/* 3 */}
              <div className="flex flex-col gap-[5px]">
                <div className="flex items-center gap-2">
                  <BsCheckCircle size={16} color="white" />
                  <p className="font-bold text-[18px]">4,000 miles per month</p>
                </div>
                <p>For the best performing drivers ($.15/add’l mi).</p>
              </div>
            </div>
            {/* second column */}
            <div className="flex flex-col gap-[30px]">
              {/* 1 */}
              {/* <div className="flex flex-col gap-[5px]">
                <div className="flex items-center gap-2">
                  <BsCheckCircle size={18} color="white" />
                  <p className="font-bold text-[18px]">No Hidden Costs in your Fees</p>
                </div>
                <p>Taxes and insurance included in the plan, so there are no hidden costs</p>
              </div> */}
              {/* 2 */}
              <div className="flex flex-col gap-[5px]">
                <div className="flex items-center gap-2">
                  <BsCheckCircle size={18} color="white" />
                  <p className="font-bold text-[18px]">Easy Digital Form of Payment</p>
                </div>
                <p>
                  All payments done digitally with multiple online payment methods for the best experience.
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-center md:justify-start ">
            {/* <button
              className="w-[250px] h-[50px] bg-white text-[#742BFA] px-2 py-2 font-bold rounded-[10px] hover:bg-[#E9E9E9] "
              onClick={(e) => handleSmoothScroll(e, 'reserve-now')}
            >
              Reserve Now
            </button> */}
            <ReserveNowBtn />
          </div>
        </div>
        {/* second div (the car image) */}
        <div className="w-[full] h-[100%] justify-center overflow-hidden">
          <Image
            width={800}
            height={800}
            src={whiteCar.src}
            alt="modern"
            className="
              w-full h-full
              aspect-square 
              mt-[-20px] sm:mt-[-30px] md:mt-[-40px] l:mt-[-120px] xl:mt-0 
              object-cover 
              rounded-bl-[20px] rounded-br-[20px]
              xl:rounded-bl-[0px] xl:rounded-tl-[0px] 
              xl:rounded-br-[20px] xl:rounded-tr-[20px]
            "
          />
        </div>
      </div>
    </div>
  );
}
