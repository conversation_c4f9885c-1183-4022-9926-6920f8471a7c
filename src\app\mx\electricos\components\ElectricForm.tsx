'use client';
/* eslint-disable react-hooks/exhaustive-deps */
// 'use client';
// import React from 'react';
// import { Flex, Text } from '@chakra-ui/react';
// import electricBG from '@/assets/electricos/FondoElectrico.png';
// import ElectricInputs from './ElectricInputs';

// const ElectricForm = () => {
//   return (
//     <Flex
//       bgImage={electricBG.src}
//       bgRepeat={'no-repeat'}
//       bgSize={'cover'}
//       w="100%"
//       h="100%"
//       minH="760px"
//       bgColor="#F2F2F2"
//       pt="60px"
//       flexDir="column"
//       alignItems="center"
//       px={{ base: '30px', md: '150px' }}
//       gap="3vh"
//       color="#1A1A1A"
//       fontFamily="Plus-Jakarta-Sans"
//       textAlign="center"
//       pb="4vh"
//     >
//       <Text fontSize={{ md: '40px', base: '24px' }} fontWeight={700} color="#E5E5E5" mt="5vh">
//         RESERVA TU VEHÍCULO
//       </Text>
//       <Text
//         color={'#E5E5E5'}
//         w={{ md: '50%', base: '90%' }}
//         fontSize={{ md: '18px', base: '18px' }}
//         fontWeight={300}
//         mt="40px"
//         mb="2vh"
//       >
//         Adquiere tu OCN llenando el siguiente formulario. Nos contactaremos contigo para brindarte información
//         sobre tu reserva.{' '}
//       </Text>

//       <Flex w="100%">
//         <ElectricInputs />
//       </Flex>
//     </Flex>
//   );
// };

// export default ElectricForm;
/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Image } from '@chakra-ui/react';
import netaReserva from '@/assets/electricos/neta_reserva.png';
import { Formik, FormikHelpers, FormikValues } from 'formik';
import { contactEVMXSchema } from '@/schemas/contactEVMXSchema';
import axios from 'axios';
import Swal from 'sweetalert2';
import useHandleResize from '@/components/useHandleResize';
import ReserveNowBtnMX from '@/components/custom/ui/ReserveNowBtnMX';
import { CustomEVInputMX, SelectEVInputMX } from './InputsV2';
import useSelectedElectricCar from '@/store/zustand/useSelectedElectricCar';
import vehiclesData from './vehicleData';
import { useMemo } from 'react';

// ['OCN V', 'OCN U PRO', 'OCN GT', 'OCN S']

export default function ReserveForm() {
  const { option } = useSelectedElectricCar();

  const options = useMemo(() => {
    const allOptions = vehiclesData.map((v) => v.option);
    const lastOptions = allOptions.filter((o) => o.value !== option.value);
    // setDynamicOptions([option, ...lastOptions]);
    return [option, ...lastOptions];
  }, [option]);
  // console.log('options: ', options);

  async function sendToHubspot(form: FormikValues) {
    const url = '/api/hubspot/createContact';
    const data = {
      firstname: form.fullName,
      phone: form.phone,
      // city: form.state,
      email: form.email,
      model: form.model.value,
      fuente: 'Pagina OCN',
      source: localStorage.getItem('source'),
    };
    // console.log(data);
    const response = await axios.post(`${url}`, data);
    return response;
  }

  const onSubmit = async (
    values: any,
    helpers: FormikHelpers<{
      fullName: string;
      phone: string;
      email: string;
      model: {
        value: string;
        label: string;
      };
    }>
  ) => {
    try {
      // setSubmitting(false);
      // setDynamicOptions(vehiclesData.map((v) => v.option));
      // setOption(vehiclesData[0].option);
      // console.log(actualValues);
      await sendToHubspot(values);
      return await Swal.fire({
        title: 'Información enviada',
        text: 'Te contactáremos lo más pronto posible 😁',
        icon: 'success',
        confirmButtonText: 'Cerrar',
      });
    } catch (e: any) {
      // setSubmitting(true);
      if (e.response.data.message.includes('Ya has enviado tu solicitud'))
        return await Swal.fire({
          title: '¡Pronto te contactaremos! 😁',
          text: 'Hemos recibido tu solicitud, te puedes comunicar con nosotros vía WhatsApp.',
          icon: 'info',
          confirmButtonText: 'Cerrar',
          showCancelButton: true,
          cancelButtonText: `Whatsapp`,
          customClass: {
            cancelButton: 'whatsapp-swal-btn',
          },
        }).then((result) => {
          if (result.isDismissed) {
            window.open('https://api.whatsapp.com/send?phone=5215590632045', '_blank');
          }
        });
      return await Swal.fire({
        title: 'Algo salió mal',
        text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    } finally {
      helpers.resetForm();
      helpers.setFieldValue('model', option);
    }
  };

  const isMobile = useHandleResize({ breakpoint: 640 });

  const initialValues = useMemo(() => {
    return {
      fullName: '',
      phone: '',
      email: '',
      model: option,
    };
  }, [option]);

  return (
    <section
      id="reserve-now"
      className="w-full h-[max-content] min-h-[600px] py-[100px] px-6 lg:px-8 bg-[#F5F5FF]"
    >
      <div className="w-[100%] h-[100%] justify-center grid grid-cols-1 xl:grid-cols-2 gap-[30px]">
        <div className="w-[100%] h-[100%] flex justify-center items-center sticky order-last xl:order-first mt-[35px] xl:mt-0 ">
          <div className="w-[90%] h-[100%] xl:h-[70%] absolute">
            <svg
              className="absolute top-[-20px] left-[15%] "
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 22 22"
              fill="none"
            >
              <path
                d="M11 0L13.489 8.51098L22 11L13.489 13.489L11 22L8.51098 13.489L0 11L8.51098 8.51098L11 0Z"
                fill="#FFC130"
              />
            </svg>
            <div className="w-[12px] h-[12px] bg-[#742BFA] rounded-full absolute right-2 bottom-[25%] " />
            <div className="w-[17px] h-[17px] bg-[#BEFF6C] rounded-full absolute left-[40%] bottom-[-1rem] xl:bottom-[-2rem] " />
          </div>
          <Image
            src={netaReserva.src}
            w="90%"
            h={{ base: '100%', l: '70%' }}
            alt="teslaWhite"
            className="object-contain sticky "
          />
        </div>
        <div className="w-full xl:w-[95%] 2xl:w-[70%] lg:pl-[20px] xl:pl-0  h-[100%] grid xl:justify-between gap-[30px] text-[#464E5F] font-[Plus-Jakarta-Sans] order-first xl:order-last  ">
          <p className="text-[#5A00F8] text-[28px] xl:text-[40px] font-[700] text-center md:text-start ">
            Reserva tu vehículo
          </p>
          <p className="text-center md:text-start">
            Adquiere tu NETA llenando el siguiente formulario. Nos contactaremos contigo para brindarte
            información sobre tu reserva.
          </p>
          <Formik
            initialValues={initialValues}
            onSubmit={(values, helpers) => onSubmit(values, helpers)}
            validationSchema={contactEVMXSchema}
          >
            {({ errors, touched, handleSubmit, isSubmitting }) => (
              <form onSubmit={handleSubmit} className="flex flex-col w-full">
                <div className="flex flex-col md:w-[300px] 2xl:w-full gap-[20px] sm:gap-[30px] xl:gap-[30px] xl:justify-between mt-[30px] font-[600]">
                  <div className="flex flex-col sm:flex-row gap-[20px] l:gap-[30px] w-full">
                    <CustomEVInputMX
                      name="fullName"
                      label="Nombre completo"
                      errors={errors}
                      touched={touched}
                      type="text"
                    />

                    <CustomEVInputMX
                      name="phone"
                      label="Teléfono celular"
                      errors={errors}
                      touched={touched}
                      type="number"
                    />
                  </div>
                  <div className="flex flex-col sm:flex-row gap-[20px] l:gap-[30px]">
                    <CustomEVInputMX
                      name="email"
                      label="Email"
                      errors={errors}
                      touched={touched}
                      type="text"
                    />
                    <SelectEVInputMX
                      name="model"
                      label="Escoje tu EV"
                      errors={errors}
                      touched={touched}
                      options={options}
                      dynamicOption={option}
                    />
                  </div>
                </div>
                <div className="xl:w-[270px] mt-[35px] ">
                  <ReserveNowBtnMX
                    full={!!isMobile}
                    type="submit"
                    isSubmitting={isSubmitting}
                    className="w-[270px]"
                  >
                    Enviar
                  </ReserveNowBtnMX>
                </div>
                {/* <div className="sm:hidden md:w-[270px] ">
                  <ReserveNowBtn full text="Submit" type="submit" isSubmitting={isSubmitting} />
                </div> */}
              </form>
            )}
          </Formik>
        </div>
      </div>
    </section>
  );
}
