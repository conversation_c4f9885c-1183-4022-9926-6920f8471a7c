'use client';
/* eslint-disable array-bracket-newline */
import { Divider, Flex, Heading, Text } from '@chakra-ui/layout';
import { Accordion } from '@chakra-ui/react';
import React, { useState, useEffect } from 'react';
import Questions from './Questions';

const questions = [
  {
    question: 'What is OCN Renting?',
    answer: [
      "Renting is a medium or long-term rental of a new car that allows you to get a new car without down payments or security deposits to continue working on mobility platforms. In addition, with our ALL-INCLUSIVE service, you don't have to worry about high expenses and future costs.",
    ],
  },
  {
    question: 'How does OneCarNow work?',
    answer: [
      '● Choose your favorite car from the selection we have for you.',
      '● Complete all the steps and make it yours in less than 90 minutes*.',
      '● Receive it in 3 to 5 days.',
      '*minimum response time.',
    ],
  },
  {
    question: 'What does the all-inclusive service include?',
    answer: [
      '● Preventive maintenance',
      '● Insurance for PLATFORMS',
      '● Possession',
      '● Preventive spare parts',
      '● Placing',
      '● Verifications',
      '● Auto 24/7',
    ],
  },
  {
    question: 'What are the benefits?',
    answer: [
      '● New car without devaluations.',
      '● All inclusive.',
      '● No delayed procedures.',
      '● No down payments or security deposits.',
      '● Quick and easy process, completely online.',
      '● Auto 24/7',
      '● Insurance backed by platforms.',
    ],
  },
  {
    question: 'In which states can I hire?',
    answer: [
      'Our service is only available in Miami, Florida in the US right now, but is available in Mexico City, State of Mexico, Tijuana, Querétaro, Monterey, Guadalajara and Puebla in Mexico..',
    ],
  },
  {
    question: 'What are the minimum requirements to hire?',
    answer: [
      'Our platform requirements would be:',
      '● 3,000 trips made on a maximum of 3 platforms.',
      '● 4.8 stars on each of the platforms on which you are registered.',
    ],
  },
];

const FAQ = () => {
  const [isOpen, setIsOpen] = useState(() => {
    const initial = new Array(questions.length).fill(false);
    initial[0] = true;
    return initial;
  });

  const toggleOpen = (index: any) => {
    setIsOpen((prev) => {
      const newState = [...prev];
      newState[index] = !prev[index];
      return newState;
    });
  };

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Flex
        w={'100%'}
        h="100%"
        minH="100vh"
        direction="column"
        justifyContent="center"
        alignItems="center"
        bgColor="#FAFAFF"
      >
        <Flex w="90%" justifyContent="center" flexDir="column">
          <Flex
            w={{ base: '100%', md: '535px' }}
            sx={{ '> *:not(:last-child)': { mb: 30 } }}
            pt={{ base: '50px', md: '50px' }}
            flexDir={'column'}
            fontFamily="Plus-Jakarta-Sans"
            color="text.main"
          >
            <Heading fontSize="30px">Frequently Asked Questions</Heading>

            <Divider w="42px" border={'2px solid #9E8EFF'} borderRadius="10px" />

            <Text>
              Do you need help? Resolve your doubts with the questions that have arisen regarding Renting and
              our services
            </Text>
          </Flex>

          <Flex
            w="100%"
            h="100%"
            justifyContent="center"
            justify={'center'}
            mt="100px"
            transition="ease-in .1s"
            mb="100px"
          >
            <Accordion
              w="100%"
              display="flex"
              flexDir="column"
              alignItems="center"
              defaultIndex={[0]}
              allowMultiple
              sx={{ '> *:not(:last-child)': { mb: '30px' } }}
            >
              {/* Renderiza todas las preguntas */}
              {questions.map((q, i) => {
                return (
                  <Questions
                    key={i}
                    question={q.question}
                    answer={q.answer}
                    indexOpen={isOpen[i]}
                    toggleOpen={() => toggleOpen(i)}
                  />
                );
              })}
            </Accordion>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
};

export default FAQ;
