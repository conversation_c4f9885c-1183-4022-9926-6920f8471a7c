import CustomButton from '@/components/CustomButton';
import CarGrid from '@/components/CarCards/CarGrid';
import { redirect } from 'next/navigation';
import { platformVehiclesData } from '@/app/mx/vehicles.data';

const dots =
  'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fdots.png?alt=media&token=cfebdf85-b3c8-42b2-84b3-74e66b1c158f';

export default function CarPanels() {
  let platform = platformVehiclesData.slice(0, 3);

  const justifyClass = 'justify-center';

  return (
    <div className="w-full h-max-content lg:h-full pb-[50px] relative bg-[rgba(195,198,255,0.1)] flex justify-center overflow-hidden">
      {/* Purple Circle */}
      <div className="w-[70px] h-[70px] bg-[#9E8EFF] rounded-full absolute z-0 -right-[50px] top-0" />

      <div className="w-[90%] md:w-[90%] lg:w-[80%] h-full flex flex-col items-center z-[1] [&>*:not(:last-child)]:mb-10">
        {/* Header Section */}
        <div className="flex flex-col mt-[60px] self-start [&>*:not(:last-child)]:mb-2">
          <h2 className="text-purple-strong text-[30px] font-[Plus-Jakarta-Sans] font-bold">
            Autos destacados
          </h2>

          <p className="font-[Manrope] font-medium">Elige el que más te guste!</p>
        </div>

        {/* Cars Grid Section */}
        <div className={`w-full l:w-[85%] cards:w-full flex ${justifyClass}`}>
          <div className="flex flex-col items-center w-full">
            <div className={`flex ${justifyClass} p-0 w-full xxl:w-[90%] gap-6`}>
              <CarGrid plan={platform} />
            </div>
          </div>
        </div>

        {/* Button Section */}
        {
          /* if more than 3 vehicles, show button */
          platform.length > 3 && (
            <form
              className="flex justify-center mt-[30px]"
              action={async () => {
                'use server';
                redirect('/mx/autos');
              }}
            >
              <CustomButton message="Ver más" className="btn-purple" type="submit" />
            </form>
          )
        }
      </div>

      {/* Green Circle */}
      <div className="w-[124px] h-[124px] bg-[#47EB84] rounded-full absolute z-0 -left-[50px] bottom-0" />

      {/* Dots Background */}
      <div
        className="w-[77.3px] md:w-[116px] h-[32px] md:h-[48px] absolute right-[2px] bottom-[2px] bg-contain"
        style={{ backgroundImage: `url(${dots})` }}
      />
    </div>
  );
}
