'use client';
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Flex, Icon, Image, SimpleGrid } from '@chakra-ui/react';
import { useState } from 'react';
import Slider from 'react-slick';
import vehicleData, { DisplayVehicleProps } from './vehicleData';
import { SlArrowLeft, SlArrowRight } from 'react-icons/sl';
import ReserveNowBtnMX from '@/components/custom/ui/ReserveNowBtnMX';
import CarouselElectricVehicles from './Carrousel';
import { handleSmoothScroll } from '@/app/us/(components)/USNav/USNav';
import useSelectedElectricCar from '@/store/zustand/useSelectedElectricCar';

export default function Vehicles2() {
  const [slider, setSlider] = useState<Slider | null>(null);
  // These are the images used in the slide
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalData, setModalData] = useState<DisplayVehicleProps | null>(null);

  const toggleModal = (data?: DisplayVehicleProps) => {
    if (!isModalOpen && data) {
      setModalData(data);
    } else setModalData(null);
    setIsModalOpen(!isModalOpen);
  };

  // console.log(images.length)
  const settings = {
    // customPaging: function (i: any) {
    //   return (
    //     <Center key={i} w="100%" h="100%">
    //       <Image w={'90%'} h="90%" borderRadius="8px" src={images[i]} alt="image" objectFit="contain" />
    //     </Center>
    //   );
    // },
    dots: true,
    dotsToShow: 3,
    dotsClass: 'slick-dots slick-thumb',
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 1,
    slidesToScroll: 1,
  };
  return (
    <Flex
      w="100%"
      h="100%"
      minH="100vh"
      flexDir="column"
      alignItems="center"
      id="nuestros-vehiculos"
      justifyContent="center"
      className="bg-[#1A1A1A] py-[80px] sm:py-[50px] md:py-0 l:py-[100px] "
      position="relative"
      sx={{
        '@media only screen and (min-width: 1000px) and (max-width: 1350px)': {
          py: '80px',
        },
        // '@media only screen and (min-width: 500px) and (max-width: 768px)': {
        //   height: '110% !important',
        // },
      }}
    >
      {/* <Center w="!00%" position="absolute" zIndex={3}> */}
      {/* <div className="w-full flex "> */}
      <p className="text-[white] w-full text-center text-[36px] md:text-[40px] font-bold ">
        Nuestros vehículos
      </p>
      {/* </div> */}
      {isModalOpen && modalData && (
        <CarouselElectricVehicles
          shortName={modalData.shortName}
          toggleModal={toggleModal}
          modalInfo={modalData.modalInfo}
        />
      )}
      <Box
        w={'100%'}
        position="relative"
        px={{ base: '30px', md: '0px' }}
        sx={{
          '.slick-slider .slick-track': {
            height: '100% !important',
          },
          '.slick-thumb': {
            mb: '30px',
            // bottom: '-35px',
          },
          '@media(max-width: 1350px)': {
            '.slick-thumb': {
              mb: '0px',
            },
          },
          '.slick-dots li.slick-active button:before': {
            color: '#5A00F8 !important',
            fontSize: '12px',
            opacity: '1 !important',
          },
          '.slick-dots li button:before': {
            opacity: '1 !important',
            color: 'rgba(250, 250, 255, 0.5) !important',
            fontSize: '10px',
          },
          '@media (max-width: 450px)': {
            '.slick-slider .slick-list, .slick-slider .slick-track': {
              height: '100% !important',
            },
          },
        }}
      >
        {/* CSS files for react-slick */}
        <link
          rel="stylesheet"
          type="text/css"
          charSet="UTF-8"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
        />
        <link
          rel="stylesheet"
          type="text/css"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
        />
        <Flex
          as="button"
          className=" l:block hidden "
          w={{ base: '30px', md: '50px' }}
          h={{ base: '30px', md: '50px' }}
          justifyContent="center"
          alignItems="center"
          borderRadius="50%"
          // border="3px solid white"
          zIndex={10}
          p="0px"
          cursor="pointer"
          bgColor="transparent"
          position="absolute"
          left="40px"
          bottom="45%"
          color="rgba(250, 250, 255, 1)"
          onClick={() => {
            // const nextIndex = currentSlide === images.length - 1 ? currentSlide : currentSlide + 1; // Aumenta el índice actual en 1
            // setCurrentSlide(nextIndex); // Actualiza el índice actual
            // slider?.slickGoTo(nextIndex);
            return slider?.slickPrev();
            // console.log(nextIndex);
          }}
          _hover={{ bgColor: 'transparent' }}
        >
          <Icon
            as={SlArrowLeft}
            w={{ base: '30px', md: '35px', lg: '40px' }}
            h={{ base: '30px', md: '35px', lg: '40px' }}
          />
        </Flex>
        <Slider {...settings} ref={(slider) => setSlider(slider)} initialSlide={0}>
          {vehicleData.map((vehicle, i) => {
            return <Slide key={i} {...vehicle} toggleModal={toggleModal} />;
          })}
        </Slider>
        <Flex
          as="button"
          className=" l:block hidden "
          w={{ base: '30px', md: '50px' }}
          h={{ base: '30px', md: '50px' }}
          justifyContent="center"
          alignItems="center"
          borderRadius="50%"
          // border="3px solid white"
          zIndex={10}
          p="0px"
          cursor="pointer"
          bgColor="transparent"
          position="absolute"
          right="40px"
          bottom="45%"
          color="rgba(250, 250, 255, 1)"
          onClick={() => {
            // const nextIndex = currentSlide === images.length - 1 ? currentSlide : currentSlide + 1; // Aumenta el índice actual en 1
            // setCurrentSlide(nextIndex); // Actualiza el índice actual
            // slider?.slickGoTo(nextIndex);
            return slider?.slickNext();
            // console.log(nextIndex);
          }}
          _hover={{ bgColor: 'transparent' }}
        >
          <Icon
            as={SlArrowRight}
            w={{ base: '30px', md: '35px', lg: '40px' }}
            h={{ base: '30px', md: '35px', lg: '40px' }}
          />
        </Flex>
      </Box>
    </Flex>
  );
}

interface SlideProps extends DisplayVehicleProps {
  toggleModal: (data?: DisplayVehicleProps) => void;
}

function Slide(props: SlideProps) {
  const vn = props.shortName;
  const { setOption } = useSelectedElectricCar();

  return (
    <>
      <SimpleGrid
        columns={{ base: 1, lg: 2 }}
        h="100%"
        gap={{ base: '0px', lg: '50px' }}
        justifyContent="center"
        position="relative"
        className="px-[5px]"
      >
        <div className="w-full flex justify-center lg:justify-end">
          <Image
            w={{ base: '400px', l: '600px' }}
            h={{ base: '300px', l: '500px' }}
            src={props.mainImage}
            alt="img"
            objectFit="contain"
            // px={{ base: '3px', md: 0 }}
          />
        </div>
        <Flex
          className="text-[white] flex flex-col w-full gap-[30px] items-center lg:items-start justify-center mb-[10px] lg:mb-0"
          sx={{
            '@media only screen and (min-width: 500px) and (max-width: 768px)': {
              gap: '50px !important',
            },
          }}
        >
          <p className="text-[40px] font-bold  ">
            {vn.split(' ')[0]} <span className="text-[#5A00F8]">{vn.split(' ').slice(1).join(' ')}</span>
          </p>
          <p className="max-w-[500px]">{props.modalInfo.description}</p>
          <div className="w-full flex gap-2 md:gap-[30px] justify-center lg:justify-start mb-[20px] md:mb-0">
            <button
              className=" border-[2px] border-[white] rounded-[10px] w-[160px] cursor-pointer"
              onClick={() => props.toggleModal(props)}
            >
              Más información
            </button>
            <ReserveNowBtnMX
              className="w-[160px]"
              onClick={(e) => {
                setOption(props.option);
                handleSmoothScroll(e, 'reservar');
              }}
            >
              Reserva Ahora
            </ReserveNowBtnMX>
          </div>
        </Flex>
      </SimpleGrid>
    </>
  );
}
