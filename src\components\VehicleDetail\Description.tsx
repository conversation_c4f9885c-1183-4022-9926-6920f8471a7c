/* eslint-disable @typescript-eslint/no-use-before-define */
import { Heading, List, ListIcon, ListItem, Skeleton, Stack, Text } from '@chakra-ui/react';
import { BiCheckCircle } from 'react-icons/bi';

const personalBenefits = ['Todo incluido', 'Beneficios por suscripción'];
const platformBenefits = [
  'Todo incluido',
  'Sin pago inicial',
  'Beneficios por suscripción',
  '36 meses con opción a compra',
];

export default function Description({
  personal,
  platform,
  loading,
  name,
  description,
  durationMonths,
}: {
  personal?: boolean;
  platform?: boolean;
  loading: boolean;
  name: string;
  description: string;
  durationMonths?: number[];
}) {
  return (
    <>
      {loading ? (
        <Stack sx={{ '> *:not(:last-child)': { mb: 10 } }}>
          <Stack sx={{ '> *:not(:last-child)': { mb: '5px' } }}>
            <Text
              fontSize="20px"
              color={personal ? '#028CF3' : 'purple.strong'}
              fontFamily={'Plus-Jakarta-Sans'}
              fontWeight={700}
            >
              {personal && 'Plan personal'}
              {platform && 'Plan plataformas'}
            </Text>
            <Skeleton h="35px" w="150px" />
          </Stack>
          <Stack>
            {Array.from({ length: 4 }).map((el, i) => (
              <Skeleton key={i} h="16px" />
            ))}
          </Stack>

          {personal && <CustomList personal />}
          {platform && <CustomList platform />}
        </Stack>
      ) : (
        <Stack sx={{ '> *:not(:last-child)': { mb: 5 } }} w={{ base: '100%', md: '100%' }}>
          <Stack sx={{ '> *:not(:last-child)': { mb: 1 } }}>
            <Text
              fontSize="20px"
              color={personal ? '#028CF3' : 'purple.strong'}
              fontFamily="Plus-Jakarta-Sans"
              fontWeight={700}
            >
              {personal && 'Plan personal'}
              {platform && 'Plan plataformas'}
            </Text>
            <Heading fontSize="32px" fontFamily="Plus-Jakarta-Sans" fontWeight={700} color="text.title">
              {name}
            </Heading>
          </Stack>
          <Text
            fontFamily={'Plus-Jakarta-Sans'}
            color="text.description"
            textAlign="justify"
            lineHeight="30px"
            fontSize={'16px'}
            fontWeight={500}
          >
            {description}
          </Text>
          {personal && <CustomList personal durationMonths={durationMonths} />}
          {platform && <CustomList platform />}
        </Stack>
      )}
    </>
  );
}

function CustomList({
  personal,
  platform,
  durationMonths,
}: {
  personal?: boolean;
  platform?: boolean;
  durationMonths?: number[];
}) {
  return (
    <List spacing={2} m={0} fontFamily="Plus-Jakarta-Sans" fontWeight={400} fontSize="14px" color="text.main">
      {personal &&
        personalBenefits.map((benefit, i) => (
          <ListItem key={i} m="0" mt={0}>
            <ListIcon as={BiCheckCircle} w="20px" h="20px" color="#00DE53" />
            {benefit}
          </ListItem>
        ))}
      {durationMonths && durationMonths.length < 2 && (
        <ListItem m="0" mt={0}>
          <ListIcon as={BiCheckCircle} w="20px" h="20px" color="#00DE53" />
          48 meses con opción a compra
        </ListItem>
      )}
      {platform &&
        platformBenefits.map((benefit, i) => (
          <ListItem key={i} m="0" mt={0}>
            <ListIcon as={BiCheckCircle} w="20px" h="20px" color="#00DE53" />
            {benefit}
          </ListItem>
        ))}
    </List>
  );
}
