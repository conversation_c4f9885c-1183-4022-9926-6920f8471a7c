'use client';

import { FaCheckCircle } from 'react-icons/fa';
import Image from 'next/image';

type ItemProps = {
  title: string;
  description: string;
};

const items: ItemProps[] = [
  { title: 'Mantenimiento', description: 'Hacemos mantenimientos vehículares cada 15 mil km.' },
  { title: 'Tramites vehículares', description: 'Nosotros los realizamos por ti.' },
  { title: 'Refacciones', description: 'Cubrimos gastos de refacciones preventivas' },
  { title: 'Seguro incluido', description: 'Seguro de auto con cobertura más amplia' },
];

function Item({ props }: { props: ItemProps }) {
  return (
    <li className="h-[80px] text-purple-soft w-[300px]">
      <div className="flex items-center [&>*:not(:last-child)]:mr-3">
        <FaCheckCircle />
        <span className="font-[Plus-Jakarta-Sans] font-bold">{props.title}</span>
      </div>
      <p className="text-text-main mt-[10px] text-description text-[14px]">{props.description}</p>
    </li>
  );
}

export default function WhyUs() {
  return (
    <section
      id="whyUs"
      className="
        w-full 
        h-full lg:h-[650px] 
        overflow-hidden 
        flex 
        justify-center 
        mt-[50px] 
        mb-[50px] lg:mb-0
      "
    >
      <div
        className="
        w-[80%] 
        h-full 
        items-center 
        pt-[50px] lg:pt-0
        grid
        grid-cols-1 lg:grid-cols-[3fr_2fr]
        [&>*:not(:last-child)]:mb-[150px] lg:[&>*:not(:last-child)]:mb-5
      "
      >
        <div className="flex flex-col [&>*:not(:last-child)]:mb-10">
          <h2
            className="
            text-purple-strong 
            text-[26px] 
            font-[Plus-Jakarta-Sans] 
            font-bold
          "
          >
            ¿Por qué elegirnos?
          </h2>

          <p
            className="
            font-[Manrope]
            w-full md:w-[75%]
            font-semibold
            text-[15px]
            text-description
          "
          >
            OCN te ofrece un servicio de suscripción todo incluido, cubriendo gastos iniciales y a futuro
            asociados a la adquisición de un vehículo.
          </p>

          <ul
            className="
            grid 
            grid-cols-1 md:grid-cols-2 
            mt-[10px] 
            ml-0 
            gap-10
            text-[18px] 
            font-medium 
            font-[Plus-Jakarta-Sans]
            list-none
          "
          >
            {items.map((item, i) => (
              <Item key={i} props={item} />
            ))}
          </ul>
        </div>

        <div className="w-[310px] h-full justify-self-center flex items-center relative">
          <Image
            width={307}
            height={470}
            src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fchica.png?alt=media&token=d697977c-4299-4717-aef3-77d40a950383"
            alt="Professional woman"
            className="
              absolute 
              w-[307px] 
              h-[470px]
              transform -translate-y-[14.5%]
              right-[-5px] lg:right-[-40px]
            "
          />
          <div
            className="
            w-[305px] 
            h-[313px] 
            bg-[rgba(71,235,132,0.74)]
            shadow-[0px_4px_60px_rgba(71,235,132,0.5)]
            rounded-[50px_50px_0px_50px]
          "
          />
        </div>
      </div>
    </section>
  );
}
