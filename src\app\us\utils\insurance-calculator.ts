type AgeFactor = {
  min: number;
  max: number;
  factor: number;
};

type CreditScoreFactor = {
  min: number;
  max: number;
  factor: number;
};

type PriceFactor = {
  min: number;
  max: number;
  factor: number;
};

type TerritoryFactor = {
  name: string;
  factor: number;
};

// Factores desde el Excel (hoja 'Insurane calc')
const AGE_FACTORS: AgeFactor[] = [
  { min: 25, max: 26, factor: 1.34 },
  { min: 27, max: 27, factor: 1.29 },
  { min: 28, max: 28, factor: 1.24 },
  { min: 29, max: 29, factor: 1.21 },
  { min: 30, max: 30, factor: 1.15 },
  { min: 31, max: 35, factor: 1.06 },
  { min: 36, max: 39, factor: 1.0 },
  { min: 40, max: 49, factor: 0.97 },
  { min: 50, max: 59, factor: 0.9 },
  { min: 60, max: 69, factor: 0.87 },
  { min: 70, max: 74, factor: 0.93 },
  { min: 75, max: Infinity, factor: 1.1 },
];

const CREDIT_SCORE_FACTORS: CreditScoreFactor[] = [
  { min: 500, max: 639, factor: 1.65 }, // Peor score, factor más alto
  { min: 640, max: 649, factor: 1.32 },
  { min: 650, max: 674, factor: 1.22 },
  { min: 675, max: 699, factor: 1.11 },
  { min: 700, max: 724, factor: 1.0 },
  { min: 725, max: 749, factor: 0.9 },
  { min: 750, max: 774, factor: 0.75 },
  { min: 775, max: 799, factor: 0.68 },
  { min: 800, max: 824, factor: 0.61 },
  { min: 825, max: 849, factor: 0.54 },
  { min: 850, max: Infinity, factor: 0.51 }, // Mejor score, factor más bajo
];

const PRICE_FACTORS: PriceFactor[] = [
  { min: 0, max: 24999, factor: 0.97 },
  { min: 25000, max: 29999, factor: 0.97 },
  { min: 30000, max: 34999, factor: 0.98 },
  { min: 35000, max: 39999, factor: 0.98 },
  { min: 40000, max: 44999, factor: 0.99 },
  { min: 45000, max: 49999, factor: 1.0 },
  { min: 50000, max: 54999, factor: 1.01 },
  { min: 55000, max: 59999, factor: 1.04 },
  { min: 60000, max: 64999, factor: 1.05 },
  { min: 65000, max: 69999, factor: 1.07 },
  { min: 70000, max: 79999, factor: 1.11 },
  { min: 80000, max: 89999, factor: 1.13 },
  { min: 90000, max: 100000, factor: 1.18 },
];

const TERRITORY_FACTORS: TerritoryFactor[] = [
  { name: 'Miami', factor: 1.1 },
  { name: 'New York', factor: 1.15 },
  { name: 'Los Angeles', factor: 1.12 },
  { name: 'Chicago', factor: 1.08 },
  { name: 'Houston', factor: 0.95 },
  { name: 'Phoenix', factor: 0.92 },
  { name: 'Philadelphia', factor: 1.05 },
  { name: 'San Antonio', factor: 0.94 },
  { name: 'San Diego', factor: 1.1 },
  { name: 'Dallas', factor: 0.97 },
  { name: 'San Jose', factor: 1.14 },
  { name: 'Austin', factor: 0.96 },
  { name: 'Jacksonville', factor: 0.93 },
  { name: 'Fort Worth', factor: 0.95 },
  { name: 'Columbus', factor: 0.9 },
  { name: 'Charlotte', factor: 0.92 },
  { name: 'San Francisco', factor: 1.18 },
  { name: 'Indianapolis', factor: 0.89 },
  { name: 'Seattle', factor: 1.1 },
  { name: 'Denver', factor: 1.05 },
  { name: 'Washington', factor: 1.12 },
  { name: 'Boston', factor: 1.14 },
  { name: 'El Paso', factor: 0.9 },
  { name: 'Nashville', factor: 0.94 },
  { name: 'Detroit', factor: 1.02 },
  { name: 'Oklahoma City', factor: 0.88 },
  { name: 'Portland', factor: 1.08 },
  { name: 'Las Vegas', factor: 1.0 },
  { name: 'Memphis', factor: 0.92 },
  { name: 'Louisville', factor: 0.9 },
  { name: 'Baltimore', factor: 1.05 },
  { name: 'Milwaukee', factor: 0.95 },
  { name: 'Albuquerque', factor: 0.9 },
  { name: 'Tucson', factor: 0.9 },
  { name: 'Fresno', factor: 0.95 },
  { name: 'Sacramento', factor: 1.0 },
  { name: 'Kansas City', factor: 0.92 },
  { name: 'Mesa', factor: 0.9 },
  { name: 'Atlanta', factor: 1.0 },
  { name: 'Omaha', factor: 0.88 },
  { name: 'Colorado Springs', factor: 0.95 },
  { name: 'Raleigh', factor: 0.94 },
  { name: 'Long Beach', factor: 1.1 },
  { name: 'Virginia Beach', factor: 0.95 },
  { name: 'Oakland', factor: 1.12 },
  { name: 'Minneapolis', factor: 1.0 },
  { name: 'Tampa', factor: 1.0 },
  { name: 'Tulsa', factor: 0.88 },
  { name: 'Arlington', factor: 0.95 },
  { name: 'New Orleans', factor: 0.98 },
  { name: 'Other', factor: 0.92 },
];

export const CITY_OPTIONS = TERRITORY_FACTORS.map((t) => ({ value: t.name, label: t.name }));

const MVR_MULTIPLIERS: Record<number, Record<number, number>> = {
  0: { 0: 1.0, 1: 1.25, 2: 1.43, 3: 1.69 },
  1: { 0: 1.28, 1: 1.59, 2: 1.83, 3: 2.15 },
  2: { 0: 2.5, 1: 2.5, 2: 2.5, 3: 2.5 },
  3: { 0: 2.5, 1: 2.5, 2: 2.5, 3: 2.5 },
};

const MVR_MISSING_MULTIPLIER = 2.5;
export const BASE_RATE = 11.56; // Base diaria del Excel (C2)
const DAYS_PER_MONTH = 30.4; // Factor de conversión mensual del Excel

interface RentalInput {
  age: {
    value: number;
  };
  creditScore: {
    value: number;
  };
  majorViolations?: {
    value: number | null;
  };
  minorViolations?: {
    value: number | null;
  };
  territory: {
    value: string;
  };
  carPrice: {
    value: number;
  };
}

export function calculateInsurance(input: RentalInput): {
  dailyRate: number;
  weeklyRate: number;
  monthlyCost: number;
} {
  // Validaciones
  if (input.age.value < 25) throw new Error('Edad mínima: 25 años');
  if (input.creditScore.value < 300 || input.creditScore.value > 850)
    throw new Error('Credit score inválido');

  // 1. Calcular tarifa diaria con factores
  const ageFactor =
    AGE_FACTORS.find((f) => input.age.value >= f.min && input.age.value <= f.max)?.factor || 1.0;
  const creditFactor =
    CREDIT_SCORE_FACTORS.find((f) => input.creditScore.value >= f.min && input.creditScore.value <= f.max)
      ?.factor || 1.0;
  const priceFactor =
    PRICE_FACTORS.find((f) => input.carPrice.value >= f.min && input.carPrice.value <= f.max)?.factor || 1.0;
  const territoryFactor = TERRITORY_FACTORS.find((t) => t.name === input.territory.value)?.factor || 1.0;

  // 2. Factor MVR
  let mvrFactor = 1.0; // Valor por defecto
  if (input.majorViolations?.value === undefined || input.minorViolations?.value === undefined) {
    // Caso 1: Datos MVR faltantes → penalización máxima
    mvrFactor = MVR_MISSING_MULTIPLIER;
  } else {
    // Caso 2: Datos válidos
    const major = Math.min(Number(input.majorViolations.value), 3); // Trunca a máximo 3
    const minor = Math.min(Number(input.minorViolations.value), 3); // Trunca a máximo 3

    // Obtiene el factor de la matriz (si no existe, usa 2.5)
    mvrFactor = MVR_MULTIPLIERS[major]?.[minor] ?? MVR_MISSING_MULTIPLIER;
  }

  // 3. Cálculo final (diario a mensual)
  const dailyRate = BASE_RATE * ageFactor * creditFactor * priceFactor * territoryFactor * mvrFactor;
  const monthlyCost = dailyRate * DAYS_PER_MONTH;

  return {
    dailyRate: Number(dailyRate.toFixed(2)),
    weeklyRate: Number((dailyRate * 7).toFixed(2)),
    monthlyCost: Number(monthlyCost.toFixed(2)),
  };
}

// calculateInsurance({
//   age: { value: 30 },
//   creditScore: { value: 750 },
//   majorViolations: { value: 0 },
//   minorViolations: { value: 0 },
//   territory: { value: 'Miami' },
//   carPrice: { value: 25000 },
// });

export {
  AGE_FACTORS,
  CREDIT_SCORE_FACTORS,
  PRICE_FACTORS,
  TERRITORY_FACTORS,
  MVR_MULTIPLIERS,
  MVR_MISSING_MULTIPLIER,
  DAYS_PER_MONTH,
};
