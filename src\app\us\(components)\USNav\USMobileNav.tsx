'use client';
import { usePathname, useRouter } from 'next/navigation';
import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  Drawer<PERSON><PERSON>er,
  <PERSON>er<PERSON>ontent,
  DrawerCloseButton,
  useDisclosure,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
} from '@chakra-ui/react';
import { RiArrowDownSLine, RiMenu3Fill } from 'react-icons/ri';
import { /* RefObject */ useEffect /* useRef */ } from 'react';
import { COUNTRY_DATA } from '@/constants';
import { handleSmoothScroll } from './USNav';
import useHandleResize from '@/components/useHandleResize';
import { vehicleCards } from '../../[nameCar]/data/data';
import Link from 'next/link';
// import { customLogEvent } from '@/middlewares/firebase'

export default function DrawerExample() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  // const btnRef: RefObject<HTMLButtonElement> = useRef(null);
  const pathname = usePathname();
  const isVisible = useHandleResize({ breakpoint: 1000 });
  const router = useRouter();
  useEffect(() => {
    if (!isVisible) {
      onClose();
    }
  }, [isVisible, onClose]);

  return (
    <>
      {!isOpen && (
        <RiMenu3Fill
          size={32}
          color="#742BFA"
          onClick={onOpen}
          // style={{position: 'fixed', top: '10px', right: '10px'}}
          className="bg-transparent flex l:hidden hover:bg-transparent cursor-pointer"
          aria-label={'Open Menu'}
        />
      )}

      <Drawer isOpen={isOpen} placement="right" onClose={onClose} /* finalFocusRef={btnRef} */ size="custom">
        <DrawerContent p={0} position="relative">
          <DrawerCloseButton position="absolute" size={'lg'} top={4} right={5} />
          <DrawerHeader></DrawerHeader>
          <DrawerBody pr={5} display="flex" flexDir="column" alignItems="end">
            <div className="flex flex-col items-end mt-[50px] custom-mb-5">
              <Menu>
                <MenuButton>
                  <div className="flex gap-2 items-center text-[13px] font-[Plus-Jakarta-Sans] ">
                    <p>{COUNTRY_DATA.US.navigator.ourVehicles}</p>

                    <RiArrowDownSLine className="mt-[2px]" />
                  </div>
                </MenuButton>
                <MenuList className=" text-[13px] font-[Plus-Jakarta-Sans] ">
                  {vehicleCards.map((vehicle, index) => (
                    <Link href={'/us/' + vehicle.url} key={index} prefetch={false}>
                      <MenuItem>{vehicle.name}</MenuItem>
                    </Link>
                  ))}
                </MenuList>
              </Menu>
              <p
                /* fontFamily={'Plus-Jakarta-Sans'}
                minW="30px"
                textAlign="center"
                px="20px"
                fontWeight={pathname === '/us/uber' ? 700 : 500}
                color={pathname === '/us/uber' ? 'purple.soft' : 'text.description'}
                transition=".5s"
                fontSize={pathname === '/us/uber' ? '15px' : '13px'}
                lineHeight="17.55px"
                _hover={{
                  bgColor: 'transparent',
                  color: 'purple.strong',
                  fontWeight: 700,
                  fontSize: '15px',
                }} */
                className={`
                  font-[Plus-Jakarta-Sans] 
                  min-w-[30px]
                  text-center
                  px-[20px]
                  ${pathname === '/us/uber' ? 'font-bold' : 'font-[500]'}
                  ${pathname === '/us/uber' ? 'text-purple-soft' : 'text-description'}
                  transition-all duration-500
                  ${pathname === '/us/uber' ? 'text-[15px]' : 'text-[13px]'}
                  hover:text-purple-strong
                  hover:bg-transparent
                  hover:font-bold
                  hover:text-[15px]
                  `}
                onClick={(e) => {
                  if (pathname !== '/us') {
                    router.push('/us');
                    setTimeout(() => {
                      handleSmoothScroll(e, 'how-it-works');
                    }, 500);
                  } else {
                    handleSmoothScroll(e, 'how-it-works');
                  }
                }}
              >
                {COUNTRY_DATA.US.navigator.howItWorks}
              </p>
              <button
                className="py-2 px-2 text-white rounded text-[14px] w-[135px] "
                style={{
                  background: 'linear-gradient(111deg, #5A00F8 0%, #A74DF9 100%)',
                  textAlign: 'center',
                }}
                onClick={(e) => {
                  if (pathname !== '/us') {
                    router.push('/us');
                    setTimeout(() => {
                      handleSmoothScroll(e, 'reserve-now');
                    }, 500);
                  } else {
                    handleSmoothScroll(e, 'reserve-now');
                  }
                }}
              >
                {COUNTRY_DATA.US.navigator.reserveNow}
              </button>
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
}
