'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Fa<PERSON>nstagram, FaTwitter, FaYoutube, FaLinkedin, FaFacebook } from 'react-icons/fa';
import { BsTelephone } from 'react-icons/bs';
import { FiMail } from 'react-icons/fi';
import ocn from '@/assets/footer/ocn-footer.png';

interface FooterProps {
  footerBgColor: 'footer.personal' | 'footer.main' | 'footer.platform' | 'footer.electrics';
  logo?: string;
  colorText?: string;
}

export default function Footer({ footerBgColor, logo, colorText }: FooterProps) {
  const year = new Date().getFullYear();

  // Función para obtener la clase de color de fondo correcta
  const getBgColorClass = (color: string) => {
    const colorMap = {
      'footer.personal': 'bg-footer-personal',
      'footer.main': 'bg-footer-main',
      'footer.platform': 'bg-footer-platform',
      'footer.electrics': 'bg-footer-electrics',
    };
    return colorMap[color as keyof typeof colorMap] || 'bg-footer-main';
  };

  return (
    <footer
      // text-[#E5E5E5]
      className={`
      ${colorText ? `text-[${colorText}]` : 'text-white'}
        flex flex-col
      ${getBgColorClass(footerBgColor)}
      h-max-content md:h-[334px]
      w-full
      items-center
      pb-5 lg:pb-0
      transition-all duration-1000
    `}
    >
      <div className="w-[85%] md:w-[90%] lg:w-[80%] flex flex-col mt-[50px] md:mt-[80px]">
        <div
          className="
          flex
          flex-col md:flex-row
          transition-all duration-1000
          h-max-content md:h-[200px]
          items-center md:items-start
          [&>*:not(:last-child)]:mb-[30px] md:[&>*:not(:last-child)]:mb-0
          mb-[10px]
        "
        >
          <Link
            href="/mx"
            prefetch={false}
            onClick={() => window.scrollTo({ top: 0, left: 0 })}
            className="relative w-[120px] h-[56px]"
          >
            <Image src={logo || ocn} alt="logo-footer" fill className="object-contain object-center" />
          </Link>

          <div
            className="
            flex flex-col md:flex-row
            ml-0 md:ml-5 lg:ml-[200px]
            w-[90%] md:w-full
            [&>*:not(:last-child)]:mb-10 md:[&>*:not(:last-child)]:mb-0 md:[&>*:not(:last-child)]:mr-10
            mb-10 md:mb-0
            justify-center md:justify-evenly lg:justify-around
            items-center md:items-start
            py-10 md:py-0
            font-[Plus-Jakarta-Sans]
          "
          >
            {/* OCN Section */}
            <div className="flex flex-col [&>*:not(:last-child)]:mb-2 text-center md:text-start text-[15px]">
              <p className="font-bold text-[18px] mb-2 font-[Manrope] cursor-pointer">OCN</p>
              <Link href="/mx/nosotros" prefetch={false} onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <p className="mt-2">Nosotros</p>
              </Link>
              <Link
                href="https://blog.onecarnow.com/"
                prefetch={false}
                onClick={() => window.scrollTo({ top: 0, left: 0 })}
              >
                <p>Blog</p>
              </Link>
              <Link href="/mx/noticias" prefetch={false} onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <p>Noticias</p>
              </Link>
              <Link
                href="/mx/enviar-cv"
                prefetch={false}
                onClick={() => window.scrollTo({ top: 0, left: 0 })}
              >
                <p>Unete al equipo</p>
              </Link>
            </div>

            {/* Resources Section */}
            <div className="flex flex-col [&>*:not(:last-child)]:mb-2 text-center md:text-start text-[15px]">
              <p className="font-bold text-[18px]">Recursos</p>
              <Link href="/mx/faq" prefetch={false} onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <p className="mt-2">Preguntas frecuentes</p>
              </Link>
              <Link
                href="/mx/politica-de-privacidad"
                prefetch={false}
                onClick={() => window.scrollTo({ top: 0, left: 0 })}
              >
                <p>Aviso de privacidad</p>
              </Link>
            </div>

            {/* Contact Section */}
            <div className="flex flex-col [&>*:not(:last-child)]:mb-2 text-center md:text-start text-[15px]">
              <p className="font-bold text-[18px] mb-2">Contacto</p>
              <div className="flex items-center [&>*:not(:last-child)]:mr-2 mt-2">
                <FiMail className="w-5 h-5" />
                <Link href="mailto:<EMAIL>" prefetch={false}>
                  <EMAIL>
                </Link>
              </div>
              <div className="flex items-center justify-center md:justify-start [&>*:not(:last-child)]:mr-2">
                <BsTelephone className="w-5 h-5" />
                <Link href="tel:+525590632045" prefetch={false}>
                  55 9063 2045
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex flex-col-reverse md:flex-row justify-normal sm:justify-between w-full">
          <p className="mt-[50px] md:mt-0 text-center font-[Plus-Jakarta-Sans] text-[13px] font-medium">
            © Copyright {year} OCN
          </p>

          <div className="flex [&>*:not(:last-child)]:mr-3 justify-center">
            {[
              { icon: FaFacebook, href: 'https://www.facebook.com/onecarnow' },
              { icon: FaInstagram, href: 'https://www.instagram.com/onecarnow/' },
              { icon: FaTwitter, href: 'https://mobile.twitter.com/onecarnow' },
              { icon: FaYoutube, href: 'https://www.youtube.com/@onecarnow416' },
              { icon: FaLinkedin, href: 'https://www.linkedin.com/company/onecarnow/' },
            ].map((social, index) => (
              <Link
                key={index}
                href={social.href}
                target="_blank"
                prefetch={false}
                className="w-6 h-6"
                aria-label={'social-media-' + social.icon.name.toLowerCase()}
              >
                <social.icon className="w-full h-full" />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
