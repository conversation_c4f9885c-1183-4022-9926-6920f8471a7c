{"name": "ocn-principal-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5173", "build": "next build", "start": "next start -p 5173", "lint": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@chakra-ui/icons": "2.0.19", "@chakra-ui/layout": "^2.2.0", "@chakra-ui/next-js": "2.1.4", "@chakra-ui/react": "2.7.1", "@chakra-ui/theme-utils": "^2.0.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@firebase/firestore": "^3.13.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@reduxjs/toolkit": "1.9.5", "@types/materialize-css": "1.0.11", "@types/node": "20.3.1", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "@vercel/analytics": "^1.1.1", "autoprefixer": "10.4.14", "axios": "1.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "country-state-city": "^3.1.4", "cypress": "^12.15.0", "eslint": "8.42.0", "eslint-config-next": "13.5.5", "firebase": "9.16.0", "formik": "2.4.2", "framer-motion": "^10.12.16", "lucide-react": "^0.424.0", "next": "^14.2.5", "postcss": "8.4.24", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet-async": "1.3.0", "react-icons": "4.9.0", "react-redux": "8.1.1", "react-select": "^5.8.0", "react-simple-typewriter": "^5.0.1", "react-slick": "0.29.0", "sharp": "^0.33.4", "short-unique-id": "4.4.4", "sweetalert2": "11.7.12", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^2.4.0", "tailwindcss": "3.3.2", "tailwindcss-animate": "^1.0.7", "typescript": "5.0.4", "yup": "1.2.0", "zustand": "^4.4.4"}, "devDependencies": {"@next/eslint-plugin-next": "13.4.7", "@types/react-slick": "0.23.10", "@typescript-eslint/eslint-plugin": "5.60.0", "@typescript-eslint/parser": "5.60.0", "encoding": "0.1.13", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-base": "15.0.0", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-cypress": "2.13.3", "eslint-plugin-prettier": "4.2.1"}}