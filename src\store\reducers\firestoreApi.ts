import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { collection, DocumentData, getDocs } from 'firebase/firestore';
import { db } from '../../services/firebase';

export const firestoreApi = createApi({
  reducerPath: 'firestoreApi',
  baseQuery: fakeBaseQuery(),
  endpoints: (builder) => ({
    getAllBlogs: builder.query({
      async queryFn() {
        try {
          const data: DocumentData[] = [];
          const querySnapshot = await getDocs(collection(db, 'blogs/allBlogs/public'));

          querySnapshot.forEach((doc) => {
            data.push({ ...doc.data(), uid: doc.id });
          });
          /* let copy = data.sort((d: any, b: any) => d.createdAt.toDate() - b.createdAt.toDate())
                    console.log("filtrado", copy.sort((d: any, b: any) => d.createdAt.toDate() - b.createdAt.toDate())) */
          return { data: data };
        } catch (error) {
          if (error instanceof TypeError) {
            return { message: error.message };
          }
          return { error };
        }
      },
    }),
  }),
});

export const { useGetAllBlogsQuery } = firestoreApi;
