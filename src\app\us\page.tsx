import USHeader from './(components)/USHome/USHeader';
import Uber from './(components)/USHome/Uber';
import Vehicles from './(components)/USHome/Vehicles';
import EasyWay from './(components)/USHome/EasyWay';
import Benefits from './(components)/USHome/Benefits';
import ReserveForm from './(components)/USHome/ReserveForm';
import { SourceTrackingComponent } from '@/components/SourceTrackingComponent';

export const metadata = {
  title: 'OCN | Home',
  description: 'OCN offers you a customized new car, 100% online, fast, and secure.',
};

export default function HomeUS({ searchParams }: { searchParams: Record<string, any> }) {
  return (
    <>
      <SourceTrackingComponent source={searchParams.source} />
      <USHeader />
      <Uber />
      <Vehicles />
      <EasyWay />
      <Benefits />
      <ReserveForm />
    </>
  );
}
