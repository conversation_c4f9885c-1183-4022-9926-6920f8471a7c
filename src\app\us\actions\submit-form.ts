import axios from 'axios';
import { FormikValues } from 'formik';

export async function sendToHubspotUS(form: FormikValues) {
  const url = '/api/hubspot/createContact';

  const data = {
    ...form,
    firstname: form.fullName,
    phone: form.phone,
    electric_car_usa: form.electric_car_usa,
    email: form.email,
    country: 'us',
    fuente: 'Pagina OCN',
    source: localStorage.getItem('source') || 'U3K5X9M',
  };

  // console.log(data);
  const response = await axios.post(`${url}`, data);
  return response;
}
