/* eslint-disable consistent-return */
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  Flex,
  Spinner,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { Formik } from 'formik';
import Swal from 'sweetalert2';
import axios from 'axios';
import CustomField from '@/components/Form/FieldForm';
import SelectForm from '@/components/Form/SelectForm';
import { cityOptions, SignupSchema } from '@/schemas/contactSchema';

interface ModalButtonProps {
  car: string;
  plan: string;
  hasPlan: boolean;
  namePlan?: string;
  optionDetail?: string | null;
  selectedPlan?: number;
  backgroundColor?: string;
}

interface MyFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  message: string;
  plan: string;
  car: string;
}

export default function ModalButton({
  car,
  plan,
  hasPlan,
  namePlan,
  optionDetail,
  selectedPlan,
  backgroundColor,
}: ModalButtonProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [sending, setSending] = useState(false);
  const toast = useToast();

  // const FormId = localStorage.getItem("formModalId");

  // const [formId, setFormId] = useState<string>(FormId ? FormId : "")
  function checkHasPlan() {
    if (hasPlan && !selectedPlan?.toString())
      return toast({
        description: 'Porfavor selecciona un plan para continuar',
        duration: 3000,
        position: 'top',
        status: 'info',
      });
    onOpen();
  }

  // async function sendToNotion(form: any) {
  //   await axios.post(`${process.env.NEXT_PUBLIC_NOTION_API_URL}/submitFormToNotion`, form);
  // }
  async function sendToHubspot(form: MyFormValues) {
    const url = '/api/hubspot/createContact';

    const data = {
      phone: form.phone,
      firstname: form.firstName,
      lastname: form.lastName,
      ciudad__con_selector_: form.city,
      email: form.email,
      fuente: 'Pagina OCN',
      plan: form.plan,
      personal_payment: form.plan === 'Personal' ? optionDetail : '',
      source: localStorage.getItem('source'),
    };

    const response = await axios.post(`${url}`, data);
    return response;
  }

  // const handleBlur = async (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>, form: any) => {
  //     const { name: nameEvent, value } = event.target;
  //     if (form.value)
  //         if (nameEvent != "phone" && (!form.values[nameEvent] || form.values[nameEvent].length < 3 || form.values[nameEvent].trim() === "")) return

  //     if (!formId) {
  //         const uid = new ShortUniqueId({ length: 10 });
  //         const id = await uid()
  //         localStorage.setItem('formModalId', id)
  //         setFormId(id)
  //         const date = serverTimestamp()
  //         await sendFormToFirebase(id, form.values, false, date, car + " " + plan)
  //     } else {
  //         await sendFormToFirebase(formId, form.values, false, null, car + " " + plan)
  //     }
  // };

  return (
    <>
      <Button
        w="100%"
        h="50px"
        mt="10px"
        fontFamily="Plus-Jakarta-Sans"
        fontWeight={600}
        color="white"
        onClick={checkHasPlan}
        transition={'all .5s'}
        bgSize="120% auto"
        _focus={{
          bgImage: `${backgroundColor} !important`,
        }}
        _hover={{
          bgPosition: 'right',
        }}
        style={{ backgroundImage: backgroundColor }}
      >
        ¡Lo quiero!
      </Button>
      {/* <button
        onClick={checkHasPlan}
        className={`
          w-[100%] h-[50px]
          mt-[10px]
          font-[Plus-Jakarta-Sans]
          font-[600]
          text-white
          bg-[#742BFA]
        `}
      >
        ¡Lo quiero!
      </button> */}
      <Modal size={{ base: 'sm', md: 'xl' }} onClose={onClose} isOpen={isOpen} motionPreset="slideInBottom">
        <ModalOverlay />
        <ModalContent bgColor="#FAFAFF">
          <ModalHeader mt="30px">Rellena el Formulario</ModalHeader>
          <ModalCloseButton bgColor="tomato !important" color="#FAFAFF" />
          <ModalBody
            display="flex"
            flexDir="column"
            justifyContent="center"
            alignItems="center"
            position="relative"
          >
            {sending ? (
              <Spinner
                thickness="14px"
                speed=".8s"
                emptyColor="gray.200"
                color="gray.400"
                w="150px"
                position="absolute"
                h="150px"
                zIndex={3}
                top="200px"
              />
            ) : null}
            <Formik
              initialValues={{
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
                city: '',
                message: '',
                car,
                plan,
                planOption: namePlan || selectedPlan,
                optionDetail,
              }}
              validationSchema={SignupSchema}
              onSubmit={async (values, { resetForm }) => {
                try {
                  setSending(true);
                  // let carAndPlan = car + " " + plan
                  // await sendFormToFirebase(formId, values, true, null, carAndPlan)
                  // localStorage.removeItem("formModalId")
                  await sendToHubspot(values);
                  // await sendToNotion(values);
                  // setFormId("")
                  Swal.fire({
                    title: 'Información enviada',
                    text: 'Te contactáremos lo más pronto posible',
                    icon: 'success',
                    confirmButtonText: 'Cerrar',
                  });
                  setSending(false);
                  onClose();
                  resetForm();
                } catch (e: any) {
                  console.log(e);
                  onClose();
                  setSending(false);
                  resetForm();
                  if (e.response.data.message.includes('Ya has enviado tu solicitud'))
                    return Swal.fire({
                      title: 'Pronto te contactaremos 😁',
                      text: e.response.data.message,
                      icon: 'info',
                      confirmButtonText: 'Cerrar',
                    });
                  return Swal.fire({
                    title: 'Algo salió mal',
                    text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
                    icon: 'error',
                    confirmButtonText: 'Cerrar',
                  });
                }
              }}
            >
              {({ errors, touched, handleSubmit }) => (
                <form onSubmit={handleSubmit} style={{ width: '100%' }}>
                  <Flex flexDir="column" sx={{ '> *:not(:last-child)': { mb: 5 } }} alignItems="center">
                    <CustomField
                      name="firstName"
                      label="Nombre"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="text"
                      placeholder="Nombre"
                    />
                    <CustomField
                      name="lastName"
                      label="Apellidos"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="text"
                      placeholder="Apellidos"
                    />

                    <CustomField
                      name="email"
                      label="Correo electrónico"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="email"
                      placeholder="<EMAIL>"
                    />

                    <CustomField
                      name="phone"
                      label="Teléfono"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="number"
                      placeholder="Numero teléfonico"
                    />
                    <SelectForm
                      name="city"
                      label="Estado"
                      errors={errors}
                      touched={touched}
                      firstOptionDisabled="Ingresa tu ciudad"
                      optionFields={cityOptions}
                    />
                    <CustomField
                      name="message"
                      label="Mensaje"
                      touched={touched}
                      errors={errors}
                      type="text"
                      textarea
                      placeholder="Escribe tu duda (opcional)"
                    />
                    <Flex
                      w="100%"
                      color="white"
                      p="20px"
                      sx={{ '> *:not(:last-child)': { mr: 3 } }}
                      justifyContent="end"
                    >
                      <Button
                        // bgColor="tomato"
                        sx={{
                          bgColor: 'gray.500 !important',
                        }}
                        _hover={{ bgColor: 'tomato !important' }}
                        mr={3}
                        disabled
                        color="white"
                        onClick={onClose}
                      >
                        Cerrar
                      </Button>
                      <Button
                        type="submit"
                        bgColor="purple.soft !important"
                        color="white"
                        _hover={{ bgColor: 'purple.opaque !important' }}
                        isDisabled={!!sending}
                      >
                        Enviar
                      </Button>
                    </Flex>
                  </Flex>
                </form>
              )}
            </Formik>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
