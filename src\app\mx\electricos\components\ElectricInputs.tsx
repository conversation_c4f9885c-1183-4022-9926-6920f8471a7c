/* eslint-disable consistent-return */
import { Box, Button, Flex, Image, Text } from '@chakra-ui/react';
import { Formik } from 'formik';
import { useState } from 'react';
import { InputElectricBasic, InputElectricSelect, InputElectricSelect2 } from './Inputs';
import checkIcon from '@/assets/check-circle.svg';
import axios from 'axios';
import Swal from 'sweetalert2';
import { electricSchema } from '@/schemas/contactSchema';

interface MyFormValues {
  name: string;
  apellidos: string;
  phone: string;
  email: string;
  state: string;
  model: string;
}

const ElectricInputs = () => {
  async function sendToHubspot(form: MyFormValues) {
    const url = '/api/hubspot/createContact';

    const data = {
      firstname: form.name + ' ' + form.apellidos,
      phone: form.phone,
      ciudad__con_selector_: form.state,
      email: form.email,
      electric_car: form.model,
      fuente: 'Pagina OCN',
      source: localStorage.getItem('source'),
    };
    // console.log(data);
    const response = await axios.post(`${url}`, data);
    return response;
  }
  const [submitting, setSubmitting] = useState(true);
  if (!submitting) {
    return (
      <Flex w="100%" direction={'column'} align="center" rowGap={'2vh'} mt={{ md: '0', base: '5vh' }}>
        <Box boxSize={{ md: '200px', base: '150px' }}>
          <Image src={checkIcon.src} alt="mySvgImage" />
        </Box>
        <Text fontSize="26px" color="#E5E5E5">
          {' '}
          Gracias por enviarnos tu solicitud de reserva{' '}
        </Text>
        <Text fontSize="16px" color="white">
          {' '}
          Fuiste añadido a una lista de espera. Te contactaremos en cuanto tengamos tu vehículo disponible.{' '}
        </Text>
      </Flex>
    );
  }
  // function resetForm() {
  //   throw new Error('Function not implemented.');
  // }

  return (
    <Box w={'100%'}>
      <Formik
        initialValues={{
          name: '',
          apellidos: '',
          phone: '',
          email: '',
          state: '',
          model: '',
        }}
        validationSchema={electricSchema}
        onSubmit={async (values) => {
          try {
            setSubmitting(false);
            await sendToHubspot(values);
          } catch (e: any) {
            setSubmitting(true);
            if (e.response.data.message.includes('Ya has enviado tu solicitud'))
              return Swal.fire({
                title: '¡Pronto te contactaremos! 😁',
                text: 'Hemos recibido tu solicitud, te puedes comunicar con nosotros vía WhatsApp.',
                icon: 'info',
                confirmButtonText: 'Cerrar',
                showCancelButton: true,
                cancelButtonText: `Whatsapp`,
                customClass: {
                  cancelButton: 'whatsapp-swal-btn',
                },
              }).then((result) => {
                if (result.isDismissed) {
                  window.open('https://api.whatsapp.com/send?phone=5215590632045', '_blank');
                }
              });
            return Swal.fire({
              title: 'Algo salió mal',
              text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
              icon: 'error',
              confirmButtonText: 'Cerrar',
            });
          }
        }}
      >
        {({ errors, touched, handleSubmit }) => (
          <form onSubmit={handleSubmit}>
            <Flex flexDir="column" w={'100%'} justify={'center'} align="center">
              <Flex
                flexDir={{ base: 'column', md: 'row' }}
                w={{ base: '100%', md: '80%' }}
                justify="space-evenly"
              >
                <InputElectricBasic
                  type="text"
                  errors={errors.name}
                  label="Nombre"
                  name="name"
                  placeholder=" "
                  touched={touched.name}
                />
                <InputElectricBasic
                  type="text"
                  errors={errors.apellidos}
                  label="Apellidos"
                  name="apellidos"
                  placeholder=" "
                  touched={touched.apellidos}
                />
              </Flex>

              <Flex
                flexDir={{ base: 'column', md: 'row' }}
                w={{ base: '100%', md: '80%' }}
                justify="space-evenly"
              >
                <InputElectricBasic
                  type="number"
                  errors={errors.phone}
                  label="Telefono"
                  name="phone"
                  placeholder=" "
                  touched={touched.phone}
                />
                <InputElectricBasic
                  type="text"
                  placeholder=" "
                  errors={errors.email}
                  label="Correo"
                  name="email"
                  touched={touched.email}
                />
              </Flex>

              <Flex
                flexDir={{ base: 'column', md: 'row' }}
                w={{ base: '100%', md: '80%' }}
                justify="space-evenly"
              >
                <InputElectricSelect2
                  errors={errors.state}
                  label="Estado"
                  name="state"
                  touched={touched.state}
                  options={[
                    { label: 'CDMX/EDOMEX', value: 'cdmx' },
                    { label: 'Nuevo Leon', value: 'mty' },
                  ]}
                />
                <InputElectricSelect
                  errors={errors.model}
                  label="Modelo de interés"
                  name="model"
                  options={['OCN V', 'OCN U PRO', 'OCN GT', 'OCN S']}
                  touched={touched.model}
                />
              </Flex>
            </Flex>
            <Flex direction={'column'} w="100%" justify="center" align="center">
              <Button
                mt="5vh"
                type="submit"
                borderRadius="25px"
                bgColor="#5A00F8"
                w={{ base: '100px', md: '177px' }}
                h="50px"
                fontSize={{ base: '14px', md: '20px' }}
                textAlign="center"
                color="#E5E5E5"
                className="bg-[#5A00F8]"
              >
                RESERVAR
              </Button>
            </Flex>
          </form>
        )}
      </Formik>
    </Box>
  );
};

export default ElectricInputs;
