export interface IVehicles {
  uid?: string;
  id?: string;
  name: string;
  description: string;
  payment: number;
  images: string[];
  plan: string;
  seats: number;
  security: string;
  motor: number;
  transmission: string;
  liters: number;
  interior: string;
  exterior: string;
  specialDetails: string;
  aditionalData: string;
  durationMonths: number[];
  planOptions: {
    label: string;
    priceMonth: number;
    deposit: number;
  }[];
  paymentOptions: {
    label: string;
    biweeklyPay: number;
    deposit: number;
  }[];
}
