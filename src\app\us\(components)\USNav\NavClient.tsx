'use client';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
import { handleSmoothScroll } from './USNav';
import { COUNTRY_DATA } from '@/constants';
import { Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/react';
import { vehicleCards } from '../../[nameCar]/data/data';
import Link from 'next/link';
import { RiArrowDownSLine } from 'react-icons/ri';

export default function NavClient() {
  const pathname = usePathname();
  const router = useRouter();
  return (
    <>
      <Menu>
        <MenuButton as="li">
          <div className="flex gap-1 items-center text-[13px] text-[#464E5F] font-[Plus-Jakarta-Sans] cursor-pointer ">
            <p>{COUNTRY_DATA.US.navigator.ourVehicles}</p>

            <RiArrowDownSLine className="mt-[2px]" />
          </div>
        </MenuButton>
        <MenuList className=" text-[13px] text-[#464E5F] font-[Plus-Jakarta-Sans] w-[max-content] ">
          {vehicleCards.map((vehicle, index) => (
            <Link href={'/us/' + vehicle.url} key={index}>
              <MenuItem className="w-[max-content]">{vehicle.name}</MenuItem>
            </Link>
          ))}
        </MenuList>
      </Menu>

      <li
        // as="li"
        // cursor="pointer"
        onClick={(e: any) => {
          if (pathname !== '/us') {
            router.push('/us');
            setTimeout(() => {
              handleSmoothScroll(e, 'how-it-works');
            }, 500);
          } else {
            handleSmoothScroll(e, 'how-it-works');
          }
        }}
        // fontFamily={'Plus-Jakarta-Sans'}
        // minW="95px"
        // textAlign="center"
        // fontWeight={pathname === '/blog' ? 700 : 500}
        // color={pathname === '/blog' ? 'purple.soft' : 'text.description'}
        // transition=".5s"
        // fontSize={pathname === '/blog' ? '15px' : '13px'}
        // lineHeight="17.55px"
        // _hover={{
        //   color: 'purple.strong',
        //   fontWeight: 700,
        //   fontSize: '15px',
        // }}
        className={`
          cursor-pointer
          font-[Plus-Jakarta-Sans]
          min-w-[95px]
          text-center
          text-[13px]
          font-normal
          text-[#464E5F]
          transition-all
          duration-500
          hover:text-[#5A00F8]
          hover:font-bold
          hover:text-[15px]
          hover:line-[17.55px]
        `}
      >
        {/* <Link href="/blog"> */}
        {COUNTRY_DATA.US.navigator.howItWorks}
        {/* </Link> */}
      </li>
      <li>
        <button
          onClick={(e) => {
            if (pathname !== '/us') {
              router.push('/us');
              setTimeout(() => {
                handleSmoothScroll(e, 'reserve-now');
              }, 500);
            } else {
              handleSmoothScroll(e, 'reserve-now');
            }
          }}
          className="h-[40px] min-h-[40px] py-2 px-2 text-white rounded-[6px] font-bold font-[Plus-Jakarta-Sans] text-[14px] mr-[50px] w-[135px] "
          style={{
            background: 'linear-gradient(111deg, #5A00F8 0%, #A74DF9 100%)',
            textAlign: 'center',
          }}
        >
          <p className="mb-[3px] whitespace-nowrap ">{COUNTRY_DATA.US.navigator.reserveNow}</p>
        </button>
      </li>
    </>
  );
}
