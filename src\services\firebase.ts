// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app';
// import { getAnalytics } from "firebase/analytics";
import { getFirestore } from 'firebase/firestore';
import { getAuth } from 'firebase/auth';
import { getStorage } from 'firebase/storage';

// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.0.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_API_KEY_MAIN,
  authDomain: process.env.NEXT_PUBLIC_AUTH_DOMAIN_MAIN,
  projectId: process.env.NEXT_PUBLIC_PROJECT_ID_MAIN,
  storageBucket: process.env.NEXT_PUBLIC_STORAGE_BUCKET_MAIN,
  messagingSenderId: process.env.NEXT_PUBLIC_MESSAGING_SENDER_ID_MAIN,
  appId: process.env.NEXT_PUBLIC_APP_ID_MAIN,
  measurementId: process.env.NEXT_PUBLIC_MEASUREMENT_ID_MAIN,
};

// Initialize Firebase
const app = initializeApp(firebaseConfig, 'Main');
// export const analyticsMain = getAnalytics(app);
export const dbMain = getFirestore(app);
export const storageMain = getStorage(app);
export const authMain = getAuth(app);

const firebaseConfigDev = {
  apiKey: process.env.NEXT_PUBLIC_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_MEASUREMENT_ID,
};

const appDev = initializeApp(firebaseConfigDev, 'Dev');
export const auth = getAuth(appDev);
export const db = getFirestore(appDev);
export const storage = getStorage(appDev);
// export const analytics = getAnalytics(appDev);
