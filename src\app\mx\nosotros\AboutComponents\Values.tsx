import React from 'react';
import { Flex, Heading, Text, Icon } from '@chakra-ui/react';
import { TiLightbulb } from 'react-icons/ti';
import { RiTeamLine } from 'react-icons/ri';
import { SlBadge } from 'react-icons/sl';
import { BsCardText } from 'react-icons/bs';

const Values = () => {
  const values = [
    {
      title: 'Innovación',
      text: 'Buscamos constantemente formas creativas de mejora, para ofrecer mejores soluciones a nuestros clientes.',
      icon: TiLightbulb,
    },
    {
      title: 'Confianza',
      text: 'Generar confianza a través de una transparencia y honestidad en todas nuestras interacciones,',
      icon: RiTeamLine,
    },
    {
      title: 'Excelencia',
      text: 'Proporcionar un servicio excepcional y una buena atención por parte de los colaboradores.',
      icon: SlBadge,
    },
    {
      title: 'Eficiencia',
      text: 'Optimizar nuestros procesos para ofrecer una excelente experiencia a cada uno de nuestros clientes.',
      icon: BsCardText,
    },
  ];

  return (
    <>
      <Heading textAlign={'center'} color="text.main" fontSize="32px">
        Valores
      </Heading>

      <Flex
        flexDir={{ base: 'column', l: 'row' }}
        justifyContent="center"
        flexWrap="wrap"
        w="100%"
        align={'center'}
        sx={{ '> :nth-child(n+1)': { mb: { base: '50px' }, mr: { base: 0, l: '50px' } } }}
        mb={'5vh'}
      >
        {values.map((v, i) => {
          return (
            <Flex
              key={i}
              w={{ base: '100%', md: '490px' }}
              p={{ base: '20px', md: '30px' }}
              bgColor="rgba(195, 198, 255, 0.15)"
              borderRadius="20px"
              sx={{ '> *:not(:last-child)': { mr: '30px' } }}
            >
              <Flex
                w="100px"
                h="132px"
                justifyContent="center"
                alignSelf="center"
                alignItems="center"
                borderRadius="20px"
                bgColor="purple.strong"
              >
                <Icon as={v.icon} w="48px" h="48px" color="#FFFFFF" />
              </Flex>

              <Flex w="70%" flexDir="column" sx={{ '> *:not(:last-child)': { mb: '14px' } }}>
                <Text fontWeight={700} fontSize="20px">
                  {v.title}
                </Text>

                <Text fontSize={{ base: '12px', md: '16px' }}>{v.text}</Text>
              </Flex>
            </Flex>
          );
        })}
      </Flex>
    </>
  );
};

export default Values;
