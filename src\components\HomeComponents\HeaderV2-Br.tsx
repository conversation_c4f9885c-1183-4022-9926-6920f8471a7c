/* eslint-disable prettier/prettier */
import Image from 'next/image';
import CustomButton from '@/components/CustomButton';
import { COUNTRY_DATA } from '@/constants';
import { redirect } from 'next/navigation';
// import { headers } from 'next/headers';
import BrasilFlag from '@/assets/br/brasil.png';
import Vehicle from '@/assets/br/dolphin.avif';
import BackgroundShape from '@/assets/br/background-shape.avif';
import VehicleMobile from '@/assets/br/byd-dolphin.png';
import LogoBr from '@/assets/br/logo-br.png';
import VehicleMx from '@/assets/mx/sentra_.webp';
import DecoreMx from '@/assets/mx/decore.webp';


import ModalForm from '@/app/br/_components/modal-form';
import { cn } from '@/lib/utils';

interface HeaderV2Props {
  country?: 'MX' | 'US' | 'BR';
}

// const imagesMap: { [key in 'MX' | 'US' | 'BR']: string } = {
const imagesMap: { [key in 'MX' | 'US' | 'BR']: { vehicle: string; decoration: string, christ?: string; vehicleMobile?: string } } = {
  MX: {
    vehicle: VehicleMx?.src || 'https://onecarnow.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fsentra_.5221edb3.webp&w=1080&q=75&dpl=dpl_7TvB3Q8sCkwLkXLqDEDiePQ5bYkE',
    decoration: DecoreMx?.src || 'https://onecarnow.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdecore.52c633c1.webp&w=640&q=75&dpl=dpl_7TvB3Q8sCkwLkXLqDEDiePQ5bYkE',
  },
  BR: {
    vehicle: Vehicle?.src || 'https://onecarnow.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdolphin.2267bf55.avif&w=750&q=75&dpl=dpl_7TvB3Q8sCkwLkXLqDEDiePQ5bYkE',
    decoration: BackgroundShape?.src || 'https://onecarnow.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbackground-shape.108b4572.avif&w=1920&q=75&dpl=dpl_7TvB3Q8sCkwLkXLqDEDiePQ5bYkE',
    vehicleMobile: VehicleMobile?.src || 'https://onecarnow.com/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbyd-dolphin.aa39a1cb.png&w=750&q=75&dpl=dpl_7TvB3Q8sCkwLkXLqDEDiePQ5bYkE',
    // christ: Christ.src, // background at same level of decoration
  },
  US: {
    vehicle: '',
    decoration: '',
  },
}



export default function HeaderV2BR({ country = 'MX' }: HeaderV2Props) {

  const vehicleImg = imagesMap[country].vehicle;
  const decorationImg = imagesMap[country].decoration;
  const vehicleMobile = imagesMap[country].vehicleMobile;

  console.log('vehicleImg: ', vehicleImg);
  console.log('decorationImg: ', decorationImg);
  console.log('vehicleMobile: ', vehicleMobile);
  return (
    <>
      <div
        id='header'
        className={`
        flex
        w-full
        h-full md:h-[90vh]
        min-h-full md:min-h-[600px]
        py-20 md:py-0
        md:overflow-hidden
        flex-col md:flex-row
        items-center relative
        overflow-x-hidden
      `}
      >
        {
          country === 'BR' && (
            <>
              {/* If it's br, show the logo image on the top left of this container */}
              <div className="absolute top-4 left-6 md:top-8 md:left-14 lg:left-[100px]  z-[2]">
                <Image src={LogoBr} alt="Logo" width={100} height={100} />
              </div>
            </>
          )

        }
        <div
          className={`
        w-full md:w-[850px]
        flex flex-col
        z-[1]
        pl-5 md:pl-[50px] lg:pl-[100px]
        font-[Plus-Jakarta-Sans]
        mb-3 md:mb-10
        last:mb-0 md:last:mb-0
      `}
        >
          <div
            className={`
          bg-purple-light bg-opacity-20 w-[330px] md:w-[max-content] h-[40px]
          rounded-full text-purple-700 flex items-center justify-center
          mb-4 md:mb-10
        `}
            style={{
              backgroundColor: 'rgba(195, 198, 255, 0.7)',
              paddingLeft: '16px',
              paddingRight: '16px',
            }}
          >
            <h2 className="text-sm md:text-[15px] font-normal text-center font-sans flex items-center justify-center gap-2">
              {country === 'BR' && (
                <Image src={BrasilFlag} alt="Brasil flag" width={20} height={20} className="align-middle" />
              )}
              {COUNTRY_DATA[country].home.hero.text}
            </h2>
          </div>

          <h1
            className={`
          w-[80%] md:w-full
          text-main mb-4 md:mb-10
          text-[40px]
        `}
          >
            {/* {country === 'MX' ? COUNTRY_DATA.MX.home.hero.title : COUNTRY_DATA.US.home.hero.title} */}
            {COUNTRY_DATA[country].home.hero.title}
            {COUNTRY_DATA[country].home.hero.separation && <br />}
            <span className="font-semibold text-purple-strong">
              {' '}
              {COUNTRY_DATA[country].home.hero.purple}
            </span>
          </h1>

          <hr
            className={`
            w-[46px]
            hidden md:block
            rounded
            border-[2px]
            border-[#9E8EFF]
            opacity-60
            mb-4 font-sans
          `}
          />

          <p className="hidden mb-4 md:block text-main">{COUNTRY_DATA[country].home.hero.subtitle}</p>

          {country === 'BR' ? (
            <div className='hidden md:block'>
              <ModalForm />
            </div>
          ) : (
            <form
              className="hidden md:block w-[max-content]"
              action={async () => {
                'use server';
                // redirect('/mx/autos');
                if (country === 'MX') {
                  return redirect('/mx/autos');
                }
                return null;
              }}
            >
              <CustomButton
                message={COUNTRY_DATA[country].home.hero.button}
                className="w-full h-full btn-purple"
                aria-label="Ver autos"
                type="submit"
              >
                {COUNTRY_DATA[country].home.hero.button}
              </CustomButton>
            </form>
          )}
        </div>

        <div className="relative grid items-center w-full h-full">
          <div className="relative grid items-center w-full h-full">
            <div
              className={
                cn(
                  'w-full h-full z-[3]',
                  'top-0 md-top-[10%]',
                  'right-0 md:right-[-12%]',
                  'l:absolute',
                  country === 'BR' && ' l:right-0'
                )
              }
            >
              <div className="relative h-[300px] md:h-[90%] w-full">
                <Image
                  src={vehicleImg}
                  alt="Sentra"
                  fill
                  priority
                  sizes="(max-width: 768px) 100vw, 50vw"
                  // className="object-contain l:object-right "
                  // hidden l:block
                  className={cn(
                    'object-contain l:object-right',
                    vehicleMobile ? 'hidden l:block' : ''
                  )}
                />
                {
                  vehicleMobile && (
                    <Image
                      src={vehicleMobile}
                      alt="Sentra"
                      fill
                      priority
                      sizes="(max-width: 768px) 100vw, 50vw"
                      className="object-contain l:object-right block l:hidden"
                    />
                  )
                }

              </div>
            </div>
          </div>

          <div
            className="
          w-full
          h-[50vh] md:h-full
          absolute
          top-[-200px] lg:top-[-100px]
          right-0
          hidden l:block
        "
          >
            {/* {Christ && (
              <Image
                src={Christ}
                alt="Christ the Redeemer"
                fill
                priority={!isMobile} // If it's not mobile, load the image first, otherwise load it after the page is loaded
                sizes="(max-width: 768px) 100vw, 50vw"
                className="object-contain object-right"
              />
            )} */}
            <Image
              src={decorationImg}
              alt="Decorative background"
              fill
              // priority={!isMobile} // If it's not mobile, load the image first, otherwise load it after the page is loaded
              sizes="(max-width: 768px) 100vw, 50vw"
              className="object-contain object-right"
            />
          </div>
        </div>

        {/* <form
          className="block md:hidden "
          action={async () => {
            'use server';
            redirect(`/${country}/autos`);
          }}
        >
          <CustomButton
            message={COUNTRY_DATA[country].home.hero.button}
            className="btn-purple"
            aria-label="Ver autos"
            type="submit"
          />
        </form> */}
        {country === 'BR' ? (
          <div className='block md:hidden'>
            <ModalForm />
          </div>
        ) : (
          <form
            className="block md:hidden w-[max-content]"
            action={async () => {
              'use server';
              // redirect('/mx/autos');
              if (country === 'MX') {
                return redirect('/mx/autos');
              }
              return null;
            }}
          >
            <CustomButton
              message={COUNTRY_DATA[country].home.hero.button}
              className="btn-purple"
              aria-label="Ver autos"
              type="submit"
            >
              {COUNTRY_DATA[country].home.hero.button}
            </CustomButton>
          </form>
        )}


      </div>
    </>
  );
}
