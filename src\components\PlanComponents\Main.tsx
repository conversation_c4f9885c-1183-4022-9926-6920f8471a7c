import Link from 'next/link';
import { Box, Center, Divider, Flex, Heading, Text } from '@chakra-ui/react';
import CustomButton from '@/components/CustomButton';

const bgPersonal = 'plan.personal.bgColor';
const bgCirclePersonal = 'plan.personal.circles';
const bgPlataform = 'plan.platform.bgColor';
const bgCirclePlatform = 'plan.platform.circles';

export default function Main({ isPersonalPlan }: { isPersonalPlan: boolean }) {
  return (
    <Flex
      w="100%"
      h={{ xxl: '680px' }}
      minH={{ md: '640px' }}
      justifyContent="space-around"
      py={{ base: '50px', l: 0 }}
      sx={{
        '> *:not(:last-child)': {
          mb: { base: 3, l: 2 },
        },
        '> *:nth-last-of-type(-n+4)': {
          mb: 0,
        },
      }}
      position="relative"
      bg={isPersonalPlan ? bgPersonal : bgPlataform}
      overflow={'hidden'}
      color="#FFFFFF"
      flexDir={{ base: 'column', l: 'row' }}
      alignItems={'center'}
    >
      <Flex
        w={{ base: '100%', l: '90%', xxl: '70%' }}
        flexDir="column"
        zIndex={1}
        pr={{ base: '15px', l: 0 }}
        pl={{ base: '15px', l: '50px', lg: '100px' }}
        sx={{ '> *:not(:last-child)': { mb: { base: 3, l: 10 } } }}
      >
        <Box
          fontFamily="Plus-Jakarta-Sans"
          fontWeight={500}
          fontSize={{ base: '42px', l: '48px' }}
          lineHeight={{ base: '50px', l: '60px' }}
        >
          <Flex alignItems="center">
            <Heading as="h1" fontFamily="Plus-Jakarta-Sans" fontSize={{ base: '42px', l: '48px' }}>
              <b>{isPersonalPlan ? 'Plan personal' : 'Los Mejores Autos'}</b>
              <Text as="span">
                <b>...</b>
              </Text>
            </Heading>
          </Flex>
          <Text fontSize={{ base: '40px', l: '40px' }}>
            {isPersonalPlan ? 'pensado solo para ti!' : 'Para los Mejores Conductores'}
          </Text>
        </Box>
        <Divider
          w="42px"
          display={{ base: 'none', l: 'initial' }}
          border={'2px solid'}
          borderColor={isPersonalPlan ? '#60EFFF' : '#43D17D'}
          borderRadius="3px"
        />
        <Text
          w="80%"
          display={{ base: 'none', l: 'initial' }}
          fontFamily="Plus-Jakarta-Sans"
          fontWeight={500}
          fontSize={{ base: '14px', l: '15px' }}
        >
          En OCN evolucionamos para ofrecerte los mejores autos, con los mejores precios.
        </Text>

        <Box display={{ base: 'none', l: 'initial' }}>
          <Link href="/mx/autos">
            <CustomButton message="¡Quiero estrenar!" className={isPersonalPlan ? 'btn-blue' : 'btn-green'} />
          </Link>
        </Box>
      </Flex>
      <Box
        w="100%"
        h={{ base: '50%', md: '90%' }}
        display={'flex'}
        alignItems="center"
        pt={{ base: 0, l: 0 }}
        justifyContent="center"
        position="relative"
        overflow="hidden"
      >
        <Box
          w="100%"
          h="50%"
          display={'grid'}
          alignItems={{ base: 'center', l: 'center' }}
          position="relative"
          right={{ base: '-15px', xxl: '-10%' }}
          top={{ l: '50px', xxl: 0 }}
        >
          <Flex
            h={{ base: '300px', l: '450px' }}
            zIndex={2}
            bottom={0}
            justifyContent="end"
            backgroundImage={
              isPersonalPlan
                ? 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fpersonal%2Fpersonal.png?alt=media&token=18fe8375-5b1b-450b-a020-5a18fd1ca2a5'
                : 'https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/plan%2Fplatform%2FYaris.png?alt=media&token=8a156f3c-bc53-4c20-9eb7-f21feb7477bc'
            }
            alignItems="center"
            backgroundRepeat="no-repeat"
            backgroundPosition={{ base: 'center', l: 'center' }}
            backgroundSize="contain"
            transform={{ sm: 'none', l: 'scale(1.1)', xxl: isPersonalPlan ? 'scale(1.5)' : 'scale(1)' }}
          />
        </Box>
      </Box>
      <Center display={{ base: 'initial', l: 'none' }} zIndex={2}>
        <Link href="/mx/autos">
          <CustomButton message="¡Quiero estrenar!" className={isPersonalPlan ? 'btn-blue' : 'btn-green'} />
        </Link>
      </Center>
      <Box
        w="650px"
        h="670px"
        bg={isPersonalPlan ? bgCirclePersonal : bgCirclePlatform}
        borderRadius="50%"
        position="absolute"
        transform="matrix(0.85, -0.52, 0.51, 0.86, 0, 0)"
        backgroundBlendMode="multiply"
        left="40%"
        bottom="60%"
      />
      <Box
        w="606px"
        h="566px"
        bg={isPersonalPlan ? bgCirclePersonal : bgCirclePlatform}
        borderRadius="50%"
        position="absolute"
        transform="matrix(0.29, -0.96, 0.95, 0.31, 0, 0)"
        backgroundBlendMode="multiply"
        left="-150px"
        bottom="-450px"
      />
      <Box
        w="606px"
        h="566px"
        bg={isPersonalPlan ? bgCirclePersonal : bgCirclePlatform}
        borderRadius="50%"
        position="absolute"
        transform="matrix(0.24, -0.97, 0.97, 0.25, 0, 0)"
        backgroundBlendMode="multiply"
        right="-200px"
        bottom="-150px"
      />
    </Flex>
  );
}
