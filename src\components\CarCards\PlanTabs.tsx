// import React from 'react';
// import { Tab, TabList } from '@chakra-ui/react';
// import useTabPlanSelected from '@/store/zustand/planSelected';

const PlanTabs = () => {
  // const planSelected = useTabPlanSelected();
  //   return (
  //     // <TabList
  //     //   bgColor="rgba(116, 43, 250, 0.1)"
  //     //   border="1px solid #742BFA"
  //     //   color="#742BFA"
  //     //   fontSize="14px"
  //     //   fontFamily="Plus-Jakarta-Sans"
  //     //   fontWeight={500}
  //     //   borderRadius="30px"
  //     //   w={{ base: '336px', md: '466px' }}
  //     //   h="50px"
  //     // >
  //     //   <Tab
  //     //     fontWeight={500}
  //     //     color="#742BFA"
  //     //     borderRadius="25px"
  //     //     w="240px"
  //     //     h="100%"
  //     //     _selected={{
  //     //       color: 'white',
  //     //       bg: 'linear-gradient(95.48deg, #6210FF 9.55%, #A74DF9 128.92%)',
  //     //     }}
  //     //     onClick={() => planSelected.setPlanSelected(0)}
  //     //   >
  //     //     Uso plataformas
  //     //   </Tab>
  //     //   <Tab
  //     //     fontWeight={500}
  //     //     color="#742BFA"
  //     //     borderRadius="25px"
  //     //     w="240px"
  //     //     h="100%"
  //     //     _selected={{
  //     //       color: 'white',
  //     //       bg: 'linear-gradient(95.48deg, #6210FF 9.55%, #A74DF9 128.92%)',
  //     //     }}
  //     //     onClick={() => planSelected.setPlanSelected(1)}
  //     //   >
  //     //     Uso personal
  //     //   </Tab>
  //     // </TabList>
  //   // );
  // };
};
export default PlanTabs;
