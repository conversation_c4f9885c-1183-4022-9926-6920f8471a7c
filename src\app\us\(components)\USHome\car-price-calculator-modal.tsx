/* eslint-disable max-params */
/* eslint-disable @typescript-eslint/no-shadow */
'use client';
/* eslint-disable prettier/prettier */

import type React from 'react';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import { DoorOpen, FuelIcon, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Formik } from 'formik';
import { sendToHubspotUS } from '../../actions/submit-form';
import Swal from 'sweetalert2';
import { getStateByCity, vehicleCards } from '../../[nameCar]/data/data';
import { calculatorFormSchema } from '@/schemas/contactUSSchema';
import InputNumber from '@/components/Inputs/InputNumber';
import SelectInput from '@/components/Form/SelectInput';
import { AGE_FACTORS, CITY_OPTIONS, CREDIT_SCORE_FACTORS, PRICE_FACTORS, TERRITORY_FACTORS } from '../../utils/insurance-calculator';
import CustomInputUSV2 from '@/components/Inputs/CustomInputUSV2';

interface CarPriceCalculatorProps {
  carModel: string;
  carYear: string;
  carImage: string;
  carPrice: number;
  carSpecs: {
    gearBox: string;
    fuel: string;
    doors: number;
    airConditioner: boolean;
    seats: number;
    distance: number;
  };
  trigger?: React.ReactNode;
}

interface ReservationFormProps {
  onBack: () => void;
  carModel: string;
}

// Reservation form component for step 2
function ReservationForm({ onBack, carModel }: ReservationFormProps) {

  return (
    <div className="p-6 max-w-3xl ">
      <h1 className="text-4xl font-bold text-[#5A00F8] mb-4">Reserve your EV now</h1>
      <p className="text-gray-600 mb-8">Leave your contact information to start your application.</p>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>

        <div className="space-y-2">
          <CustomInputUSV2
            type="text"
            label="Full Name"
            name="fullName"
          />
        </div>

        <div className="space-y-2">
          <CustomInputUSV2
            type="number"
            label="Cell phone"
            name="phone"
          />
        </div>

        <div className="space-y-2">
          <CustomInputUSV2
            type="email"
            label="Email"
            name="email"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="carModel">Choose Your EV</Label>
          <Select defaultValue={carModel} disabled>
            <SelectTrigger id="carModel">
              <SelectValue placeholder="Select a model" />
            </SelectTrigger>
            <SelectContent>
              {/* <SelectItem value={carModel}>{carModel}</SelectItem> */}
              {/* <SelectItem value="Tesla Model 3">Tesla Model 3</SelectItem>
              <SelectItem value="Tesla Model Y">Tesla Model Y</SelectItem>
              <SelectItem value="Hyundai Ioniq5 Long Range">Hyundai Ioniq5 Long Range</SelectItem>
              <SelectItem value="Kia EV6">Kia EV6</SelectItem> */}
              {vehicleCards.map((v, i) => (
                <SelectItem key={i} value={v.name}>{v.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <SelectInput
            name="state"
            disabled
            label="State"
            options={[
              { label: 'Texas', value: 'Texas' },
              { label: 'Florida', value: 'Florida' },
              { label: 'California', value: 'California' },
              { label: 'New York', value: 'New York' },
              { label: 'Other', value: 'Other' },
            ]}
            className='space-y-3'
          />
        </div>

        <div className="space-y-2">
          <SelectInput
            name="city"
            label="City"
            disabled
            options={CITY_OPTIONS}
            className='space-y-3'

          />
        </div>

        <div className="space-y-2">

          <CustomInputUSV2
            type="number"
            label="Postal Code"
            name="postalCode"
          // defaultValue={postalCode}

          />
        </div>

        <div className="space-y-2">

          <SelectInput
            name="sourceOption"
            label="How did you hear about us?"
            className='space-y-2'
            inputClassName='h-[40px]'
            options={[
              { label: 'Select an option', value: '' },
              { label: 'Facebook', value: 'Facebook' },
              { label: 'Instagram', value: 'Instagram' },
              { label: 'Google', value: 'Google' },
              { label: 'Referral', value: 'Referral' },
              { label: 'LinkedIn', value: 'LinkedIn' },
              { label: 'Physical Flyer', value: 'Physical Flyer' },
              { label: 'Billboard', value: 'Billboard' },
              { label: 'Other', value: 'Other' },
            ]}

          />
        </div>

        <div className="md:col-span-2 flex gap-4 mt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="border-[#BC97FC] text-[#5A00F8]"
          >
            Back
          </Button>
          <Button type="submit" className="flex-1 bg-[#5A00F8] hover:bg-[#4800c7]">
            Submit
          </Button>
        </div>
      </div>
      {/* </form> */}
    </div>
  );
}

export default function CarPriceCalculatorModal({
  carModel,
  carYear,
  carPrice,
  carImage,
  carSpecs,
  trigger,
}: CarPriceCalculatorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState(1);
  const [age, setAge] = useState(30);
  const [creditScore, setCreditScore] = useState(750);
  const [city, setCity] = useState('Miami');

  interface PriceRange {
    min: number;
    max: number;
  }

  const calculatePriceRange = (
    age: number,
    creditScore: number,
    city: string,
    carPrice: number
  ): PriceRange => {
    // Función auxiliar para encontrar el factor correspondiente
    const getFactor = (value: number, factors: Array<{ min: number; max: number; factor: number }>) => {
      const factor = factors.find(f => value >= f.min && value <= f.max);
      return factor ? factor.factor : 1.0;
    };

    // Obtener factores específicos
    const ageFactor = getFactor(age, AGE_FACTORS);
    const creditFactor = getFactor(creditScore, CREDIT_SCORE_FACTORS);
    const priceFactor = getFactor(carPrice, PRICE_FACTORS);
    const territoryFactor = TERRITORY_FACTORS.find(t => t.name === city)?.factor || 1.0;

    // Factores MVR
    const minMVRFactor = 1.0;  // Mejor escenario (0 violaciones)
    const maxMVRFactor = 2.5;  // Peor escenario (MVR faltante)

    // Cálculo del rango
    const BASE_RATE = 11.56;
    const DAYS_PER_MONTH = 30.4;

    const minDaily = BASE_RATE * ageFactor * creditFactor * priceFactor * territoryFactor * minMVRFactor;
    const maxDaily = BASE_RATE * ageFactor * creditFactor * priceFactor * territoryFactor * maxMVRFactor;

    return {
      min: Math.floor(minDaily * DAYS_PER_MONTH),
      max: Math.ceil(maxDaily * DAYS_PER_MONTH),
    };
  };

  const estimatedPrice = useMemo(() => {
    try {
      if (!age || age < 25 || !city || !creditScore || creditScore < 500 || creditScore > 850) {
        return {
          range: { min: 0, max: 0 },
          isValid: false,
        };
      }

      const priceRange = calculatePriceRange(age, creditScore, city, carPrice); // Precio base del vehículo

      return {
        range: priceRange,
        isValid: true,
      };
    } catch (error) {
      console.error('Calculation error:', error);
      return {
        range: { min: 0, max: 0 },
        isValid: false,
      };
    }
  }, [age, creditScore, city]);

  const handleNextStep = async (formik: any) => {
    // Validar que los valores necesarios estén presentes y sean válidos
    if (!age || age < 25 || age > 65) {
      formik.setFieldError('age', 'Age must be between 25 and 65');
      return;
    }

    if (!city || city === 'Other') {
      formik.setFieldError('city', 'Please select a valid city');
      return;
    }

    if (!creditScore || creditScore < 300 || creditScore > 850) {
      formik.setFieldError('creditScore', 'Credit score must be between 300 and 850');
      return;
    }

    // Si todas las validaciones pasan, proceder al siguiente paso
    setStep(2);
  };

  const handleBackStep = () => {
    setStep(1);
  };

  const onSubmit = async (form: any, { resetForm }: any) => {
    try {
      form = {
        ...form,
        // vehicleSelected: vehiclesFormOptions.find((vehicle) => vehicle.value === form.electric_car_usa)!
        vehicleSelected: carModel,
      };

      await sendToHubspotUS(form);

      return await Swal.fire({
        title: 'Information sent',
        text: 'We will contact you as soon as possible',
        icon: 'success',
        confirmButtonText: 'Close',
      });
      // setSending(false);
    } catch (e: any) {
      // setSending(false);
      if (e.response.data.message.includes('Ya has enviado tu solicitud')) {
        return await Swal.fire({
          title: 'We will contact you soon 😁',
          // text: e.response.data.message,
          text: 'You have already sent your request, please wait for us to contact you',
          icon: 'info',
          confirmButtonText: 'Close',
        });
      }
      return await Swal.fire({
        title: 'Something went wrong',
        // text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
        text: 'Try again, if the problem persists please wait for us to fix it',
        icon: 'error',
        confirmButtonText: 'Close',
      });
    } finally {
      resetForm();
    }
  };

  useEffect(() => {
    setStep(1);
  }, [isOpen]);

  const defaultState = getStateByCity(CITY_OPTIONS[0].value);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || <Button variant="outline">Calculate Estimated Price</Button>}
      </DialogTrigger>
      <DialogContent
        className={
          cn(
            "max-h-[95vh] overflow-auto p-5",
            step === 1 ? "max-w-6xl" : "max-w-3xl"

          )
        }
      >
        <DialogHeader>
          <DialogTitle className="sr-only">Calculate Estimated Price</DialogTitle>
        </DialogHeader>
        <Formik
          initialValues={{
            fullName: '',
            phone: '',
            email: '',
            age: 25,
            postalCode: '',
            state: defaultState ? { value: defaultState, label: defaultState } : { value: 'Florida', label: 'Florida' },
            city: CITY_OPTIONS[0],
            creditScore: 750,
            sourceOption: { value: '', label: 'Select an option' },
            electric_car_usa: carModel,
          }}
          onSubmit={onSubmit}
          validationSchema={calculatorFormSchema}
        >
          {({ errors, touched, handleSubmit, isSubmitting, values, ...rest }) => {
            // console.log(errors, touched, values);
            // console.log('isSubmitting', isSubmitting);
            return (
              (
                <form onSubmit={handleSubmit} className="flex flex-col w-full">
                  {step === 1 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Car Information Section */}
                      <div className="border border-[#BC97FC] rounded-lg p-6 m-4 md:m-6">
                        <div className="text-lg font-semibold mb-2">
                          {carModel}
                          <div className="text-sm font-normal text-muted-foreground">Model {carYear}</div>
                        </div>

                        <div className="aspect-video relative mb-6">
                          <Image
                            src={carImage || '/placeholder.svg?height=200&width=300'}
                            alt={carModel}
                            fill
                            className="object-contain rounded-md"
                          />
                        </div>

                        <div className="pt-2 h-[60%]">
                          <h3 className="font-semibold mb-4">Technical Specifications</h3>

                          <div className="grid grid-cols-2 gap-4 ">
                            <div className="border-r border-b p-2">
                              <div className="flex justify-center mb-2">
                                <GearBoxIcon />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Gear Box</div>
                                <div className="text-xs text-muted-foreground">{carSpecs.gearBox}</div>
                              </div>
                            </div>

                            <div className="border-b p-2">
                              <div className="flex justify-center mb-2">
                                <FuelIcon />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Fuel</div>
                                <div className="text-xs text-muted-foreground">{carSpecs.fuel}</div>
                              </div>
                            </div>

                            <div className="border-r p-2">
                              <div className="flex justify-center mb-2">
                                <DoorOpen />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Doors</div>
                                <div className="text-xs text-muted-foreground">{carSpecs.doors}</div>
                              </div>
                            </div>

                            <div className="p-2">
                              <div className="flex justify-center mb-2">
                                <AirConditionerIcon />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Air Conditioner</div>
                                <div className="text-xs text-muted-foreground">
                                  {carSpecs.airConditioner ? 'Yes' : 'No'}
                                </div>
                              </div>
                            </div>

                            <div className="border-r border-t p-2">
                              <div className="flex justify-center mb-2">
                                <Users />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Seats</div>
                                <div className="text-xs text-muted-foreground">{carSpecs.seats}</div>
                              </div>
                            </div>

                            <div className="border-t p-2">
                              <div className="flex justify-center mb-2">
                                <DistanceIcon />
                              </div>
                              <div className="text-center">
                                <div className="text-sm font-semibold">Distance</div>
                                <div className="text-xs text-muted-foreground">{carSpecs.distance} km</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Lease Section */}
                      <div className="border border-[#BC97FC] rounded-lg p-6 m-4 md:m-6">
                        <div className="text-lg font-semibold mb-4">Lease</div>
                        <div className="text-sm text-muted-foreground mb-6">
                          Estimated Lease Payment: Enter your age, city and credit score,
                          and our payment calculator will estimate what you could pay each
                          month for your lease.
                          <div className="mt-2">
                            Monthly payment includes registration, insurance and servicing.
                          </div>
                        </div>

                        <div className="space-y-6">
                          <div className="space-y-4">


                            <InputNumber
                              label="Age"
                              name="age"
                              onChange={(value) => setAge(value)}
                              inputClassName='w-32'
                              direction='horizontal'
                            />
                          </div>


                          <SelectInput
                            direction='horizontal'
                            inputClassName='w-48'
                            name="city"
                            label="City"
                            // onChange={(option) => setCity(option.value)}
                            onChange={(option, form) => {

                              // get state of city selected
                              const state = getStateByCity(option.value);
                              if (state) {
                                const stateOption = { value: state, label: state };
                                form.setFieldValue('state', stateOption);
                              }
                              setCity(option.value);
                            }}
                            // options={getAllCityOptions()}
                            options={CITY_OPTIONS}
                            className='space-y-3'

                          />



                          <div className="space-y-4">
                            <div className="flex flex-col items-center space-y-3">
                              <Label className='text-md '>Estimated credit rating</Label>
                              <div className="w-24">
                                <Input
                                  type="number"
                                  value={creditScore}
                                  onChange={(e) => setCreditScore(Number(e.target.value))}
                                  className="text-center"
                                  // min={500}
                                  // max={850}
                                  disabled
                                />
                              </div>
                            </div>
                            <div className="text-center font-medium">
                              {creditScore >= 760
                                ? 'Excellent'
                                : creditScore >= 700
                                  ? 'Good'
                                  : creditScore >= 660
                                    ? 'Fair'
                                    : creditScore >= 620
                                      ? 'Poor'
                                      : 'Bad'}
                            </div>
                            <Slider
                              value={[creditScore]}
                              min={500}
                              max={850}
                              step={10}
                              onValueChange={(newValues) => setCreditScore(newValues[0])}
                            />

                          </div>

                          <div className="space-y-4 pt-4 border-t border-[#BC97FC]">
                            <div className="text-center">
                              <h3 className="text-lg font-semibold mb-2">Estimated Monthly Payment Range</h3>
                              {estimatedPrice.isValid ? (
                                <div className="space-y-2">
                                  <div className="text-3xl font-bold text-[#5A00F8]">
                                    ${estimatedPrice.range.min} - ${estimatedPrice.range.max}
                                  </div>
                                  {/* <div className="text-sm text-muted-foreground">
                                    Range based on driving history and background verification
                                  </div> */}
                                </div>
                              ) : (
                                <div className="text-sm text-gray-600">
                                  Please ensure all parameters are within valid ranges:
                                  <ul className="mt-2 text-xs">
                                    <li>• Age: 25 years or older</li>
                                    {/* <li>• Credit Score: 640-850</li> */}
                                    {/* <li>• Valid city selection</li> */}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                          {/* *Final pricing may depend on factors such as driving history and background checks.
 */}
                          <div className="text-sm text-center text-muted-foreground">
                            *Final pricing may depend on factors such as driving
                            history and background checks.
                          </div>

                          <Button className="w-full bg-[#5A00F8] hover:bg-[#4800c7] mt-4" onClick={() => handleNextStep(rest)}>
                            Continue to Reservation
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <ReservationForm
                      onBack={handleBackStep}
                      carModel={carModel}
                    />
                  )}

                </form>
              )
            )
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}

function GearBoxIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-cog"
    >
      <path d="M12 20a8 8 0 1 0 0-16 8 8 0 0 0 0 16Z" />
      <path d="M12 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z" />
      <path d="M12 2v2" />
      <path d="M12 22v-2" />
      <path d="m17 20.66-1-1.73" />
      <path d="M11 10.27 7 3.34" />
      <path d="m20.66 17-1.73-1" />
      <path d="m3.34 7 1.73 1" />
      <path d="M14 12h8" />
      <path d="M2 12h2" />
      <path d="m20.66 7-1.73 1" />
      <path d="m3.34 17 1.73-1" />
      <path d="m17 3.34-1 1.73" />
      <path d="m11 13.73-4 6.93" />
    </svg>
  )
}


function AirConditionerIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-fan"
    >
      <path d="M10.827 16.379a6.082 6.082 0 0 1-8.618-7.002l5.412 1.45a6.082 6.082 0 0 1 7.002-8.618l-1.45 5.412a6.082 6.082 0 0 1 8.618 7.002l-5.412-1.45a6.082 6.082 0 0 1-7.002 8.618l1.45-5.412Z" />
      <path d="M12 12v.01" />
    </svg>
  )
}

function DistanceIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="lucide lucide-route"
    >
      <circle cx="6" cy="19" r="3" />
      <path d="M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15" />
      <circle cx="18" cy="5" r="3" />
    </svg>
  )
}



