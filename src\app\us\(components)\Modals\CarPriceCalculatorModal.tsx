'use client';
import { useState } from 'react';
import { Image } from '@chakra-ui/react';
import { Formik, FormikErrors } from 'formik';
import * as Yup from 'yup';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import CustomInputUS from '../Inputs/CustomInputUS';
import SelectInput from '@/components/Form/SelectInput';
import Swal from 'sweetalert2';
import {
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  US_DEFAULT_STATE_OPTIONS,
} from '../../[nameCar]/data/data';

interface CarPriceCalculatorModalProps {
  price: number;
  vehicleName: string;
  image: string;
  trigger: React.ReactNode;
}

const sourceOptions = [
  { value: '', label: 'Select an option' },
  { value: 'Facebook', label: 'Facebook' },
  { value: 'Instagram', label: 'Instagram' },
  { value: 'Google', label: 'Google' },
  { value: 'Referral', label: 'Referral' },
  { value: 'LinkedIn', label: 'LinkedIn' },
  { value: 'Physical Flyer', label: 'Physical Flyer' },
  { value: 'Billboard', label: 'Billboard' },
  { value: 'Other', label: 'Other' },
];

const validationSchema = Yup.object().shape({
  monthlyIncome: Yup.string()
    .required('Required')
    .test('is-number', 'Must be a valid number', (value) => {
      const num = value.replace(/[,$]/g, '');
      return !isNaN(Number(num)) && Number(num) >= 1000;
    })
    .test('min', 'Monthly income must be at least $1,000', (value) => {
      const num = value.replace(/[,$]/g, '');
      return Number(num) >= 1000;
    }),
  creditScore: Yup.number()
    .required('Required')
    .min(300, 'Credit score must be at least 300')
    .max(850, 'Credit score cannot exceed 850'),
  fullName: Yup.string().when('step', {
    is: 2,
    then: () => Yup.string().required('Required'),
  }),
  phone: Yup.string().when('step', {
    is: 2,
    then: () => Yup.string().required('Required'),
  }),
  email: Yup.string().when('step', {
    is: 2,
    then: () => Yup.string().email('Invalid email').required('Required'),
  }),
  state: Yup.mixed().when('step', {
    is: 2,
    then: () => Yup.mixed().required('Required'),
  }),
  city: Yup.mixed().when('step', {
    is: 2,
    then: () => Yup.mixed().required('Required'),
  }),
  postalCode: Yup.string().when('step', {
    is: 2,
    then: () => Yup.string().required('Required'),
  }),
  sourceOption: Yup.mixed().when('step', {
    is: 2,
    then: () => Yup.mixed().required('Required'),
  }),
});

export default function CarPriceCalculatorModal({
  price,
  vehicleName,
  image,
  trigger,
}: CarPriceCalculatorModalProps) {
  const [step, setStep] = useState(1);
  const [monthlyPayment, setMonthlyPayment] = useState(0);
  const [downPayment, setDownPayment] = useState(0);

  const calculatePayments = (income: string, creditScore: number) => {
    const numericIncome = Number(income.replace(/[,$]/g, ''));
    let calculatedMonthly = price;
    if (creditScore > 700) calculatedMonthly *= 0.9;
    if (numericIncome > 5000) calculatedMonthly *= 0.95;

    const calculatedDown = price * 0.1;

    setMonthlyPayment(Math.round(calculatedMonthly));
    setDownPayment(Math.round(calculatedDown));
  };

  return (
    <Dialog>
      <DialogTrigger asChild onClick={(e) => e.stopPropagation()}>
        {trigger}
      </DialogTrigger>
      <DialogContent className="max-w-6xl">
        <Formik
          initialValues={{
            step: 1,
            monthlyIncome: '',
            creditScore: '',
            fullName: '',
            phone: '',
            email: '',
            state: US_DEFAULT_STATE_OPTIONS[0],
            city: { value: '', label: 'Select an option' },
            postalCode: '',
            sourceOption: sourceOptions[0],
          }}
          validationSchema={validationSchema}
          onSubmit={async (values, { setSubmitting }) => {
            if (step === 1) {
              calculatePayments(values.monthlyIncome, Number(values.creditScore));
              setStep(2);
              setSubmitting(false);
            } else {
              try {
                await fetch('/api/submit-application', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    ...values,
                    state: values.state.value,
                    city: values.city.value,
                    sourceOption: values.sourceOption.value,
                    vehicleName,
                    monthlyPayment,
                    downPayment,
                  }),
                });

                await Swal.fire({
                  title: 'Information sent',
                  text: 'We will contact you as soon as possible',
                  icon: 'success',
                  confirmButtonText: 'Close',
                });

                setStep(1);
                setSubmitting(false);
              } catch (error) {
                await Swal.fire({
                  title: 'Something went wrong',
                  text: 'Please try again later',
                  icon: 'error',
                  confirmButtonText: 'Close',
                });
                setSubmitting(false);
              }
            }
          }}
        >
          {({ handleSubmit, isSubmitting, values, setFieldValue, errors, touched }) => (
            <form onSubmit={handleSubmit} className="flex flex-col gap-6">
              {step === 1 ? (
                <>
                  <div className="text-center mb-4">
                    <h2 className="text-2xl font-bold mb-2">{vehicleName}</h2>
                    <p className="text-gray-600">Calculate your monthly payment</p>
                  </div>

                  <Image src={image} alt={vehicleName} className="w-full h-48 object-contain mb-4" />

                  <CustomInputUS
                    name="monthlyIncome"
                    label="Monthly Income ($)"
                    type="text"
                    errors={errors as FormikErrors<Record<string, string>>}
                    touched={touched as any}
                  />

                  <CustomInputUS
                    name="creditScore"
                    label="Credit Score"
                    type="number"
                    errors={errors as FormikErrors<Record<string, string>>}
                    touched={touched as any}
                  />
                </>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex flex-col gap-4">
                    <Image src={image} alt={vehicleName} className="w-full h-auto object-contain" />
                    <div className="text-2xl font-bold">{vehicleName}</div>
                    <div className="bg-gray-100 p-4 rounded-lg">
                      <div className="text-xl mb-2">Your Estimated Payments:</div>
                      <div className="text-2xl font-bold text-[#5A00F8]">${monthlyPayment}/mo</div>
                      <div className="text-sm text-gray-600">Down Payment: ${downPayment}</div>
                    </div>
                  </div>
                  <div className="flex flex-col gap-4">
                    <CustomInputUS
                      name="fullName"
                      label="Full Name"
                      type="text"
                      errors={errors as FormikErrors<Record<string, string>>}
                      touched={touched as any}
                    />
                    <CustomInputUS
                      name="phone"
                      label="Phone Number"
                      // type="tel"
                      errors={errors as FormikErrors<Record<string, string>>}
                      touched={touched as any}
                    />
                    <CustomInputUS
                      name="email"
                      label="Email"
                      // type="email"
                      errors={errors as FormikErrors<Record<string, string>>}
                      // touched={touched}
                      touched={touched as any}
                    />
                    <SelectInput
                      name="state"
                      label="Choose Your State"
                      options={getUSStatesOptions()}
                      // value={values.state}
                      onChange={(option) => {
                        setFieldValue('state', option);
                        setFieldValue('city', { value: '', label: 'Select an option' });
                      }}
                    />
                    <SelectInput
                      name="city"
                      label="Choose Your City"
                      options={values.state?.value ? getUSCitiesBasedOnState(values.state.value) : []}
                      // value={values.city}
                      onChange={(option) => setFieldValue('city', option)}
                    />
                    <CustomInputUS
                      name="postalCode"
                      label="Postal Code"
                      type="text"
                      errors={errors as FormikErrors<Record<string, string>>}
                      // touched={touched}
                      touched={touched as any}
                    />
                    <SelectInput
                      name="sourceOption"
                      label="How did you hear about us?"
                      options={sourceOptions}
                      // value={values.sourceOption}
                      onChange={(option) => setFieldValue('sourceOption', option)}
                    />
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[#5A00F8] text-white py-3 rounded-lg hover:bg-[#4800c7] disabled:opacity-50"
              >
                {isSubmitting ? 'Processing...' : step === 1 ? 'Calculate Payment' : 'Submit Application'}
              </button>
            </form>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  );
}
