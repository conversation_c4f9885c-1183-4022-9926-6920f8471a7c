/* eslint-disable jsx-a11y/alt-text */
import { Box, Card, CardBody, CardFooter, Heading, Image, Stack, Text } from '@chakra-ui/react';
import Link from 'next/link';

type CardProps = {
  title: string;
  image: string;
  createdAt: { nanoseconds: number; seconds: number };
  tags: string;
  description: string;
  id: string;
};

const BlogCard = ({ title, image, createdAt, description, id }: CardProps) => {
  const fecha = new Date(createdAt.seconds * 1000 + createdAt.nanoseconds / 1000000);

  // Obtenemos el día, el mes y el año de la fecha
  const dia = fecha.getDate();
  const mes = fecha.toLocaleString('es-ES', { month: 'long' }); // Se utiliza el objeto Date de JavaScript para obtener el mes en formato legible
  const year = fecha.getFullYear();

  const date = `${dia} de ${mes}, ${year}`;

  return (
    <Card
      w="350px"
      h="460px"
      boxShadow="0px 12px 32px rgba(195, 198, 255, 0.2)"
      background="#FFFFFFF"
      borderRadius="20px"
    >
      <CardBody
        w="100%"
        h="100%"
        display="flex"
        flexDir="column"
        sx={{ '> *:not(:last-child)': { mb: 3 } }}
        p="20px"
        overflow={'hidden'}
      >
        <Box w="100%" h="180px" maxH="200px">
          <Image src={image} w="100%" h="100%" borderRadius="15px" objectFit={'cover'} />
        </Box>

        <Stack mt="5px" spacing="3" overflow={'hidden'}>
          <Text fontSize="11px">{date}</Text>
          <Heading color={'card.title'} size="md">
            {title}
          </Heading>
          <Text color="#797979" fontSize="13px">
            {description}
          </Text>
        </Stack>
      </CardBody>

      <CardFooter>
        <Link href={'/blog/' + id}>
          <Text color={'purple.opaque'} fontSize="13px">
            Leer nota completa ➟
          </Text>
        </Link>
      </CardFooter>
    </Card>
  );
};

export default BlogCard;
