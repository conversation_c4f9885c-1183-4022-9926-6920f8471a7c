import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';

export const customerReferSchema = Yup.object().shape({
  referrerName: Yup.string()
    .min(3, 'Ingresar nombre valido!')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),
  referrerPhone: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  referrerEmail: Yup.string().email('Email invalido').required('Campo obligatorio!'),
  name: Yup.string()
    .min(3, 'Ingresar nombre completo')
    .max(50, 'Demasiados')
    .trim()
    .required('Campo obligatorio!'),
  email: Yup.string().matches(
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    'Email invalido'
  ),
  phone: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  city: createSelectInputValidator('Selecciona tu ciudad'),
});
