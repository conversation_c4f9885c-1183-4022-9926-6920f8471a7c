/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-unused-vars */
import logo from '@/assets/nav-us-logo.webp';
import Link from 'next/link';
import USMobileNav from './USMobileNav';
import NavClient from './NavClient';
import Image from 'next/image';

export const handleSmoothScroll = (
  event: React.MouseEvent<HTMLParagraphElement | HTMLButtonElement | HTMLLIElement>,
  targetId: string
): void => {
  event.preventDefault();
  const targetElement = document.getElementById(targetId);
  if (targetElement) {
    const offset = window.innerWidth < 500 ? 60 : 70;
    window.scrollTo({
      top: targetElement.offsetTop - offset,
      behavior: 'smooth',
    });
    if (targetId === 'how-it-works') {
      window.history.pushState(null, '', `/us#${targetId}`);
    } else {
      window.history.pushState(null, '', `#${targetId}`);
    }
  }
};

const USNav = () => {
  return (
    <nav
      className="
      w-full h-[70px] bg-white px-6 sticky top-0 z-20 shadow-[0_2px_25px_0_rgba(0,0,0,0.15)]
      "
    >
      <div className="w-full h-full items-center justify-between flex">
        <Link href={'/us'} prefetch={false}>
          <div className="w-[73px] h-[30px]  md:ml-[30px] lg:ml-[50px]">
            <Image
              // src={logo.src} w="100%" h="100%" alt="logo" boxSize="100%"
              src={logo}
              width={1000}
              height={1000}
              priority
              alt="logo"
              className="cursor-pointer "
            />
          </div>
        </Link>

        <div className="flex h-full ">
          <ul
            className="
              w-full h-full items-center hidden l:flex transition-all duration-500 custom-margin
            "
          >
            <NavClient />
          </ul>
        </div>
        <div className="block l:hidden">
          <USMobileNav />
        </div>
      </div>
    </nav>
  );
};

export default USNav;
