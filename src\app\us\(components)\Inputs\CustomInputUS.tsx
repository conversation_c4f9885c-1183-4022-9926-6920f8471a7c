import { FormControl, FormErrorMessage } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikErrors, FormikTouched, FormikValues } from 'formik';

interface CustomInputProps {
  name: string;
  label: string;
  errors: FormikErrors<Record<string, string>>;
  touched: FormikTouched<Record<string, string>>;
  type?: 'text' | 'number';
}

export default function CustomInputUS({ name, label, errors, touched, type }: CustomInputProps) {
  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <>
          <FormControl
            className="grid gap-3"
            isInvalid={Boolean(errors ? errors[name] : '') && touched && touched[name]}
          >
            <label htmlFor={name}>{label}</label>
            <div className="relative ">
              <input
                id={name}
                className="border-[2px] w-full sm:w-[270px] h-[46px] border-gray-[#CED4DA] rounded-md px-3 focus:outline-[#742BFA] font-normal"
                type={type || 'text'}
                {...field}
                // placeholder={placeholder}
                // onBlur={(e) => handleBlur(e, form)}
              />
              <FormErrorMessage
                className="absolute bottom-[-24px] "
                fontSize="13px"
                fontWeight={600}
                letterSpacing=".03rem"
              >
                {form.errors[name]}
              </FormErrorMessage>
            </div>
          </FormControl>
        </>
      )}
    </Field>
  );
}
