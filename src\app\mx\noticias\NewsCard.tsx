import { Flex, Heading, Text, Icon, Img, Link } from '@chakra-ui/react';
import { HiArrowRight } from 'react-icons/hi';

type CardProps = {
  title: string;
  date: string;
  image: string;
  link: string;
};

export default function NewsCard({ title, date, image, link }: CardProps) {
  return (
    <Flex w="100%" h="100%" justifyContent="center" mb="80px">
      <Flex
        w="330px"
        h="250px"
        bgColor="#FFFFFF"
        flexDir="column"
        py={'30px'}
        px={8}
        sx={{ '> *:not(:last-child)': { mb: 5 } }}
        position="relative"
        borderRadius="25px"
        shadow="0px 12px 32px rgba(195, 198, 255, 0.2)"
        fontFamily="Manrope"
        fontSize="13px"
      >
        <Img w="100px" h="40px" bgSize="contain" src={image} objectFit="contain" alt="not found" />
        <Heading as="h3" fontSize="16px" fontFamily="Manrope" fontWeight={700}>
          {title}
        </Heading>
        <Text color="#818181">{date}</Text>
        <Flex
          position="absolute"
          bottom={'25px'}
          alignItems="center"
          sx={{ '> *:not(:last-child)': { mr: 3 } }}
          color="purple.opaque"
        >
          <Text>
            <Link href={link} target="_Blank">
              Leer nota completa
            </Link>
          </Text>
          <Icon as={HiArrowRight} />
        </Flex>
      </Flex>
    </Flex>
  );
}
