import { FormControl, FormErrorMessage, FormLabel, Input, Textarea } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikErrors, FormikTouched, FormikValues } from 'formik';

interface FieldProps<T> {
  handleBlur?: (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>, form: any) => Promise<void>;
  errors: FormikErrors<T>;
  // touched: any;
  touched: FormikTouched<T>;
  name: string;
  textarea?: boolean;
  label: string;
  type: 'number' | 'text' | 'email' | 'password';
  placeholder: string;
}

export default function CustomField<T>({
  errors,
  touched,
  name,
  label,
  type,
  placeholder,
  textarea,
}: FieldProps<T>) {
  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl isInvalid={Boolean(errors[name as keyof T]) && <PERSON><PERSON>an(touched[name as keyof T])}>
          {' '}
          {/* Utilizamos types de datos genéricos */}
          <FormLabel>{label}</FormLabel>
          {!textarea && <Input h="45px" type={type} {...field} placeholder={placeholder} />}
          {textarea && <Textarea h="45px" {...field} placeholder={placeholder} />}
          <FormErrorMessage fontSize="13px" fontWeight={600} letterSpacing=".03rem">
            {form.errors[name]}
          </FormErrorMessage>
        </FormControl>
      )}
    </Field>
  );
}
