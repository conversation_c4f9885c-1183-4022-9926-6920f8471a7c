'use client';

// import Navbar from '@/components/NavBar/Navbar';
import Link from 'next/link';
import { Heading, Text, Flex } from '@chakra-ui/react';
import CustomButton from '@/components/CustomButton';

export default function NotFoundMX() {
  return (
    <>
      {/* <Navbar /> */}
      <Flex
        flexDir="column"
        justifyContent="center"
        bgColor="bgColor"
        h={{ base: '80vh', md: '90vh' }}
        flex={1}
        w="100%"
        alignItems="center"
        textAlign="center"
      >
        <Heading display="inline-block" as="h2" size="2xl" color={'purple.strong'}>
          404
        </Heading>
        <Text fontSize="18px" mt={3} mb={2}>
          Pagina no encontrada
        </Text>
        <Text color={'gray.500'} mb={6}>
          La pagina que estas buscando no existe
        </Text>
        <Link href="/mx" prefetch={false}>
          <CustomButton message=" Volver al inicio" className="btn-purple"></CustomButton>
        </Link>
      </Flex>
    </>
  );
}
