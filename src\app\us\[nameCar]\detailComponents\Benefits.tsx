import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

const exclusiveBenefits = [
  {
    title: 'Easy Online Process',
    description:
      'Submit your online application that takes <10 minutes, and get your EV in 3-5 business days.',
  },
  {
    title: 'Maintenance Included',
    description: 'You won’t worry about long term costs associated with your EV.',
  },
  {
    title: '4,000 miles per month',
    description: 'For the best performing drivers ($.15/add’l mi).',
  },
  {
    title: 'Limited insurance included',
    description: 'You have the option to cover the risks of damage to your EV.',
  },
  // {
  //   title: 'No Hidden Costs in your Fees',
  //   description: 'Taxes and insurance included in the plan, so there are no hidden costs ',
  // },
  {
    title: 'Easy Digital Form of Payment',
    description: 'All payments done digitally with multiple online payment methods for the best experience.',
  },
];

export default function Benefits() {
  return (
    <div className="w-full h-full flex flex-col justify-center transition-all ease-in duration-100">
      <div className="flex">
        <p className="text-description text-[24px] font-[700] mb-[3px]">OCN exclusive benefits</p>
      </div>
      {/* <Accordion
        w="100%"
        display="flex"
        flexDir="column"
        alignItems="center"
        defaultIndex={[0]}
        allowMultiple
        sx={{ '> *:not(:last-child)': { mb: '15px' } }}
      >
        {exclusiveBenefits.map((b, i) => (
          <BenefitCard
            key={i}
            title={b.title}
            description={b.description}
          // indexOpen={isOpen[i]} // Ya no es necesario
          // toggleOpen={() => toggleOpen(i)} // Ya no es necesario
          />
        ))}
      </Accordion> */}
      {/* px={{ base: 'none', md: 5 }} */}
      <Accordion type="single" collapsible className="w-full px-0 md:p-5" defaultValue="item-1">
        {exclusiveBenefits.map((b, i) => (
          <AccordionItem key={i} value={`item-${i + 1}`} className="no-underline mb-3  border-none">
            <AccordionTrigger className="hover:no-underline flex-1 text-purple-strong text-[16px] font-[600] text-left border-none">
              {b.title}
            </AccordionTrigger>
            <AccordionContent>
              <ul className="list-disc ml-2 text-[16px]">
                <li>{b.description}</li>
              </ul>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
}
