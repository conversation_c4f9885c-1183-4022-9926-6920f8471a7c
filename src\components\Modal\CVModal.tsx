/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  <PERSON>lex,
  Spinner,
} from '@chakra-ui/react';
import { useState } from 'react';
import { Formik } from 'formik';
import Swal from 'sweetalert2';
import CustomField from '@/components/Form/FieldForm';
import SelectForm from '@/components/Form/SelectForm';
import { cityOptions, SignupSchema } from '@/schemas/contactSchema';

interface CVMmodalProps {
  backgroundColor?: string;
}

export default function CVMmodal({ backgroundColor }: CVMmodalProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [sending, setSending] = useState(false);

  // const FormId = localStorage.getItem("formModalId");

  // const [formId, setFormId] = useState<string>(FormId ? FormId : "")

  async function sendToNotion(_: any) {
    // await axios.post(`${import.meta.env.VITE_NOTION_API_URL}/submitFormToNotion`, form)
  }

  // const handleBlur = async (event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>, form: any) => {
  //     const { name: nameEvent, value } = event.target;
  //     if (form.value)
  //         if (nameEvent != "phone" && (!form.values[nameEvent] || form.values[nameEvent].length < 3 || form.values[nameEvent].trim() === "")) return

  //     if (!formId) {
  //         const uid = new ShortUniqueId({ length: 10 });
  //         const id = await uid()
  //         localStorage.setItem('formModalId', id)
  //         setFormId(id)
  //         const date = serverTimestamp()
  //         await sendFormToFirebase(id, form.values, false, date, car + " " + plan)
  //     } else {
  //         await sendFormToFirebase(formId, form.values, false, null, car + " " + plan)
  //     }
  // };

  return (
    <>
      <Button
        w="100%"
        h="50px"
        mt="10px"
        fontFamily="Plus-Jakarta-Sans"
        fontWeight={600}
        color="white"
        bgImage={backgroundColor}
        onClick={onOpen}
        transition={'all .5s'}
        bgSize="120% auto"
        _focus={{
          bgImage: backgroundColor,
        }}
        _hover={{
          bgPosition: 'right',
        }}
      >
        ¡Lo quiero!
      </Button>
      <Modal size={{ base: 'sm', md: 'xl' }} onClose={onClose} isOpen={isOpen} motionPreset="slideInBottom">
        <ModalOverlay />
        <ModalContent bgColor="#FAFAFF">
          <ModalHeader mt="30px">Rellena el Formulario</ModalHeader>
          <ModalCloseButton bgColor="tomato" color="#FAFAFF" />
          <ModalBody
            display="flex"
            flexDir="column"
            justifyContent="center"
            alignItems="center"
            position="relative"
          >
            {sending ? (
              <Spinner
                thickness="14px"
                speed=".8s"
                emptyColor="gray.200"
                color="gray.400"
                w="150px"
                position="absolute"
                h="150px"
                zIndex={3}
                top="200px"
              />
            ) : null}
            <Formik
              initialValues={{
                name: '',
                email: '',
                phone: '',
                cv: '',
                message: '',
              }}
              validationSchema={SignupSchema}
              onSubmit={async (values, { resetForm }) => {
                try {
                  setSending(true);
                  // let carAndPlan = car + " " + plan
                  // await sendFormToFirebase(formId, values, true, null, carAndPlan)
                  // localStorage.removeItem("formModalId")
                  await sendToNotion(values);
                  // console.log(values)
                  // setFormId("")
                  Swal.fire({
                    title: 'Información enviada',
                    text: 'Te contactáremos lo más pronto posible',
                    icon: 'success',
                    confirmButtonText: 'Cerrar',
                  });
                  setSending(false);
                  onClose();
                  resetForm();
                } catch (error) {
                  onClose();
                  Swal.fire({
                    title: 'Algo salió mal',
                    text: 'Porfavor espera a que lo solucionemos',
                    icon: 'error',
                    confirmButtonText: 'Cerrar',
                  });
                  setSending(false);
                }
              }}
            >
              {({ errors, touched, handleSubmit }) => (
                <form onSubmit={handleSubmit} style={{ width: '100%' }}>
                  <Flex flexDir="column" sx={{ '> *:not(:last-child)': { mb: 5 } }} alignItems="center">
                    <CustomField
                      name="name"
                      label="Nombre completo"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="text"
                      placeholder="Nombres..."
                    />

                    <CustomField
                      name="email"
                      label="Correo electrónico"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="email"
                      placeholder="<EMAIL>"
                    />

                    <CustomField
                      name="phone"
                      label="Teléfono"
                      touched={touched}
                      // handleBlur={handleBlur}
                      errors={errors}
                      type="number"
                      placeholder="Numero teléfonico"
                    />
                    <SelectForm
                      name="city"
                      label="Estado"
                      errors={errors}
                      touched={touched}
                      firstOptionDisabled="Ingresa tu ciudad"
                      optionFields={cityOptions}
                    />
                    <CustomField
                      name="message"
                      label="Mensaje"
                      touched={touched}
                      errors={errors}
                      type="text"
                      textarea
                      placeholder="Escribe tu duda (opcional)"
                    />
                    <Flex
                      w="100%"
                      color="white"
                      p="20px"
                      sx={{ '> *:not(:last-child)': { mr: 3 } }}
                      justifyContent="end"
                    >
                      <Button
                        bgColor="gray.500"
                        _hover={{ bgColor: 'tomato' }}
                        mr={3}
                        disabled
                        onClick={onClose}
                      >
                        Close
                      </Button>
                      <Button type="submit" bgColor="purple.soft" isDisabled={!!sending}>
                        Enviar
                      </Button>
                    </Flex>
                  </Flex>
                </form>
              )}
            </Formik>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
