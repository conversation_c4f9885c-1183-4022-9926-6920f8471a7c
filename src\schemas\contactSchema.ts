import * as Yup from 'yup';

export const SignupSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(3, 'Ingresar nombre valido!')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),

  lastName: Yup.string()
    .min(3, 'Ingresar nombre valido!')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Email invalido'
    )
    .required('Campo obligatorio!'),
  phone: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  message: Yup.string(),
  city: Yup.string().required('Selecciona tu ciudad'),
});

export const cityOptions = [
  { key: 'CDMX/EDOMEX', label: 'Ciudad de México / Estado México' },
  { key: 'Guadalajara', label: 'Guadalajara' },
  { key: 'Monterrey', label: 'Monterrey' },
  { key: 'Puebla', label: 'Puebla' },
  { key: 'Tijuana', label: 'Tijuana' },
  { key: 'Queretaro', label: 'Queretaro' },
  { key: 'Cuernavaca', label: 'Cuernavaca' },
  { key: 'Otro', label: 'Otro' },
];

export const contactSchema = Yup.object().shape({
  name: Yup.string()
    .min(3, 'Ingresar nombre valido!')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Email invalido'
    )
    .required('Campo obligatorio!'),
  phone: Yup.string()
    .min(10, 'Minimo 10 caracteres')
    .max(10, 'Maximo 10 caracteres')
    .required('Ingresa tu número de telefono'),
  message: Yup.string(),
  // plan: Yup.string().required('Selecciona un plan'),
  car: Yup.string(),
  city: Yup.string().required('Selecciona tu ciudad'),
});

export const cvSchema = Yup.object().shape({
  name: Yup.string()
    .min(14, 'Por favor ingresa tu nombre completo')
    .max(45, 'Demasiado largo!')
    .trim()
    .required('Campo obligatorio!'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Email invalido'
    )
    .required('Campo obligatorio!'),
  birthday: Yup.string().length(10).required('Campo obligatorio!'),
  cv: Yup.mixed()
    .test('fileSize', 'El archivo no debe pesar más de 1mb', (value) => {
      if (!value) return true;
      return value && (value as File).size <= 1024 * 1024; // Tamaño máximo de 1MB
    })
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .required('Debe seleccionar un archivo'),
  country: Yup.string().required('Selecciona tu país'),
  state: Yup.string().required('Selecciona tu estado'),
  city: Yup.string().required('Selecciona tu ciudad'),
});

export const electricSchema = Yup.object().shape({
  name: Yup.string().required('Favor indicar un Nombre!'),
  apellidos: Yup.string().required('Favor de indicar un apellido'),
  phone: Yup.number().required('Favor de indicar numero de telefono'),
  email: Yup.string().required('Favor de indicar un Correo!'),
  state: Yup.string().required('Favor de indicar un estado!'),
  model: Yup.string().required('Favor de indicar un modelo!'),
});

export const planOptions = [
  { key: 'Plataforma', label: 'Plataforma' },
  { key: 'Personal', label: 'Personal' },
];
