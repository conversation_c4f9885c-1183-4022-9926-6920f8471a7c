import { Steps } from '@/components/HomeComponents';
import BRFooter from './_components/br-footer';
import { TopBar } from './_components/top-bar';
import HeaderV2BR from '@/components/HomeComponents/HeaderV2-Br';

export const metadata = {
  title: 'OCN | Iar',
  description: 'OCN oferece um novo carro personalizado, 100% online, rápido e seguro.',
};

export default function HomeBR() {
  const country = 'BR';
  return (
    <>
      <TopBar />
      {/* <TopBar2 /> */}
      <main>
        <HeaderV2BR country={country} />

        {/* Container main del contenido home */}
        <Steps country={country} />
      </main>
      <BRFooter />
    </>
  );
}

// function TopBar2() {
//   // using marquee component

//   return (
//     <>
//       <div className="bg-[#E3D7FF]">
//         <Marquee pauseOnHover className="[--duration:20s]">
//           <div className="flex items-center space-x-12">
//             <span className="text-sm md:text-base pl-10">Grandes novidades para o Brasil!</span>
//             <span className="flex-shrink-0">
//               <Image
//                 src={LogoSvg}
//                 alt="top flag"
//                 width={33}
//                 height={40}
//                 className="min-w-[20px] min-h-[24px] object-contain"
//               />
//             </span>
//             <span className="text-sm md:text-base">Veículos 100% elétricos</span>
//             <span className="flex-shrink-0">
//               <Image
//                 src={WhiteCarSvg}
//                 alt="white car"
//                 width={33}
//                 height={40}
//                 className="min-w-[20px] min-h-[24px] object-contain"
//               />
//             </span>
//             <span className="text-sm md:text-base">Maiores ganhos para motoristas</span>
//             <span className="flex-shrink-0">
//               <Image
//                 src={Dollar}
//                 alt="dollar"
//                 width={20}
//                 height={20}
//                 className="min-w-[16px] min-h-[16px] object-contain"
//               />
//             </span>
//             <span className="text-sm md:text-base">A OCN te oferece um carro novo</span>
//             <span className="flex-shrink-0">
//               <Image
//                 src={BlackCarSvg}
//                 alt="black car"
//                 width={33}
//                 height={40}
//                 className="min-w-[20px] min-h-[24px] object-contain"
//               />
//             </span>
//             <span className="text-sm md:text-base">Ecológico e econômico</span>
//             <span className="flex-shrink-0">
//               <Image
//                 src={RocketIcon}
//                 alt="rocket"
//                 width={18}
//                 height={20}
//                 className="min-w-[14px] min-h-[16px] object-contain"
//               />
//             </span>
//           </div>
//         </Marquee>
//       </div>
//     </>
//   );
// }

// function TopBar() {
//   return (
//     <div className="relative flex overflow-x-hidden bg-[#E3D7FF]">
//       <div className="py-2 !pl-12 animate-marquee whitespace-nowrap space-x-12 flex items-center">
//         <span className="text-sm md:text-base">Grandes novidades para o Brasil!</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={LogoSvg}
//             alt="top flag"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Veículos 100% elétricos</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={WhiteCarSvg}
//             alt="white car"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Maiores ganhos para motoristas</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={Dollar}
//             alt="dollar"
//             width={20}
//             height={20}
//             className="min-w-[16px] min-h-[16px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">A OCN te oferece um carro novo</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={BlackCarSvg}
//             alt="black car"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Ecológico e econômico</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={RocketIcon}
//             alt="rocket"
//             width={18}
//             height={20}
//             className="min-w-[14px] min-h-[16px] object-contain"
//           />
//         </span>
//       </div>
//       <div className="absolute top-0 py-2 !pl-12 animate-marquee2 whitespace-nowrap space-x-12 flex items-center">
//         <span className="text-sm md:text-base">Grandes novidades para o Brasil!</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={LogoSvg}
//             alt="top flag"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Veículos 100% elétricos</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={WhiteCarSvg}
//             alt="white car"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Maiores ganhos para motoristas</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={Dollar}
//             alt="dollar"
//             width={20}
//             height={20}
//             className="min-w-[16px] min-h-[16px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">A OCN te oferece um carro novo</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={BlackCarSvg}
//             alt="black car"
//             width={33}
//             height={40}
//             className="min-w-[20px] min-h-[24px] object-contain"
//           />
//         </span>
//         <span className="text-sm md:text-base">Ecológico e econômico</span>
//         <span className="flex-shrink-0">
//           <Image
//             src={RocketIcon}
//             alt="rocket"
//             width={18}
//             height={20}
//             className="min-w-[14px] min-h-[16px] object-contain"
//           />
//         </span>
//       </div>
//     </div>
//   );
// }
