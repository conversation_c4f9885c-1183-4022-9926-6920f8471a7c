'use client';
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { StaticImageData } from 'next/image';
import Img from 'next/image';
// import { Image } from '@chakra-ui/react';
import { useState } from 'react';
import Slider from 'react-slick';
import { SlArrowLeft, SlArrowRight } from 'react-icons/sl';

export default function SimpleSlider({ images }: { images: StaticImageData[] }) {
  const [slider, setSlider] = useState<Slider | null>(null);
  // These are the images used in the slide

  const settings = {
    dots: false,
    dotsToShow: 3,
    dotsClass: 'slick-dots slick-thumb',
    infinite: true,
    speed: 500,
    arrows: false,
    slidesToShow: 1,
    slidesToScroll: 1,
  };

  return (
    <div className="w-full !h-[max-content] flex justify-center relative">
      {/* <Center w="!00%" position="absolute" zIndex={3}> */}

      <div className="w-full relative !h-[max-content] ">
        {/* CSS files for react-slick */}
        <link
          rel="stylesheet"
          type="text/css"
          charSet="UTF-8"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
        />
        <link
          rel="stylesheet"
          type="text/css"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
        />
        <button
          onClick={() => {
            slider?.slickPrev();
          }}
          className="
            flex 
            w-[30px] md:w-[50px]
            h-full
            justify-center
            items-center
            rounded-[50%]
            z-[10]
            p-0
            bg-transparent
            absolute
            hover:bg-transparent
            left-[2px]
            cursor-pointer
            text-white
          "
        >
          <SlArrowLeft className="w-[20px] md:w-[25px] lg:w-[30px] h-[20px] md:h-[25px] lg:h-[30px]" />
        </button>
        <button
          onClick={() => {
            slider?.slickNext();
          }}
          className="
          flex 
          w-[30px] md:w-[50px]
          h-full
          justify-center
          items-center
          rounded-[50%]
          z-[10]
          p-0
          bg-transparent
          absolute
          hover:bg-transparent
          right-[2px]
          cursor-pointer
          text-white
        "
          // _hover={{ bgColor: 'transparent' }}
        >
          <SlArrowRight className="w-[20px] md:w-[25px] lg:w-[30px] h-[20px] md:h-[25px] lg:h-[30px]" />
        </button>
        <style>
          {`
          .slick-slider .slick-list, .slick-slider .slick-track  {
            display: flex;
            align-items: center;
            height: max-content !important;
          },
          .slick-initialized, .slick-slide {
            height: max-content !important;
          }
        `}
        </style>
        <Slider {...settings} ref={(slider) => setSlider(slider)}>
          {images.map((e, i) => {
            return <Slide key={i} img={e.src} i={i} />;
          })}
        </Slider>
      </div>
    </div>
  );
}

function Slide({ img, i }: { img: string; i: number }) {
  return (
    <div className="w-full !h-[max-content] flex justify-center items-center">
      <Img
        width={1000}
        height={500}
        src={img}
        // alt="img"
        // placeholder="blur"
        alt={`img-mobile-${i}`}
        priority={i === 0}
        className="w-full !h-full rounded-[1.3rem] object-contain bg-no-repeat px-[3px] md:px-0"
      />
      {/* <Image
        src={img}
        alt={`img-mobile-${i}`}
        className="w-full !h-full rounded-[1.3rem] object-contain bg-no-repeat px-[3px] md:px-0"
      /> */}
    </div>
  );
}
