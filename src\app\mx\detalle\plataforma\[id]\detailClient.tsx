'use client';
/* eslint-disable react-hooks/rules-of-hooks */
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { useEffect } from 'react';
// import { useParams } from 'next/navigation';
import ModalButton from '@/components/Modal/Modal';
import AdditionalData from '@/components/VehicleDetail/AdditionalData';
import Description from '@/components/VehicleDetail/Description';
import PlatformSlider from '@/components/VehicleDetail/SliderCarImages';
import useTabPlanSelected from '@/store/zustand/planSelected';

export default function VehicleDetailPlatform({ data }: { data: any }) {
  const planTabSelected = useTabPlanSelected();

  useEffect(() => {
    planTabSelected.setPlanSelected(0);
    window.scrollTo({ top: 0, left: 0 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Flex as="main" w="100%" h={'auto'} justifyContent="center" bgColor="#FAFAFF">
        <Flex
          minH={'100vh'}
          w={{ base: '85%', l: '90%', lg: '85%' }}
          h="100%"
          flexDir={'column'}
          alignItems="center"
        >
          <Flex
            flexDir={{ base: 'column', l: 'row' }}
            w={{ base: '100%', lg: '100%' }}
            sx={{ '> *:not(:last-child)': { mb: { base: 5, l: 0 }, mr: { base: 0, l: 10 } } }}
            mt={{ base: '0px', md: '30px' }}
          >
            <Flex w={{ base: '100%', l: '52%' }} flexDir="column">
              <PlatformSlider images={data.images} borderColor="#6210FF" />
            </Flex>
            <Flex
              w={{ base: '100%', l: '44%' }}
              direction="column"
              justify="space-around"
              mt={{ base: '110px', md: '100px', l: 0 }}
            >
              <Description
                platform
                loading={false}
                name={data?.name as string}
                description={data?.description as string}
              />

              <Stack mt="30px" sx={{ '> *:not(:last-child)': { mb: 3 } }}>
                <Box>
                  <>
                    <Text
                      color="purple.strong"
                      fontSize="32px"
                      fontFamily="Plus-Jakarta-Sans"
                      fontWeight={700}
                    >
                      $ {data?.payment}
                      <Text as="span" fontSize="16px" fontWeight={600} fontStyle="italic">
                        -semanal
                      </Text>
                    </Text>
                    <Text mt="5px" color="gray.400">
                      * Promociones especiales para tu plataforma
                    </Text>
                  </>
                </Box>

                <Flex justify={'center'}>
                  <ModalButton
                    backgroundColor="linear-gradient(95.31deg, #6210FF 35.27%, #A74DF9 100%)"
                    hasPlan={false}
                    car={data?.name}
                    plan={data?.plan}
                    selectedPlan={0}
                  />
                </Flex>
              </Stack>
            </Flex>
          </Flex>

          <AdditionalData
            loading={false}
            aditionalData={data?.aditionalData}
            security={data?.security}
            interior={data?.interior}
            exterior={data?.exterior}
            specialDetails={data?.specialDetails}
            borderColor="btn-purple"
          />
        </Flex>
      </Flex>
    </>
  );
}
