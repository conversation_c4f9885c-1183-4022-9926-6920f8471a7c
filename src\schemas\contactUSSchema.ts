import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';

export const contactUSSchema = Yup.object().shape({
  fullName: Yup.string().min(3, 'Invalid name').max(45, 'To large!').trim().required('Name required'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Invalid email'
    )
    .required('Email required'),
  phone: Yup.string()
    .min(10, 'At least 10 characters')
    .max(10, 'No more than 10 characters')
    .required('Phone required'),
  // age: Yup.
  // Choose a valid age, not less than 25 and not more than 65
  // age: Yup.number()
  //   .min(25, 'Age must be at least 25')
  //   .max(65, 'Age must be at most 65')
  //   .required('Age required'),
  electric_car_usa: Yup.string().required('Choose an EV'),
  state: Yup.string().required('Choose a state'),
  // city: createSelectInputValidator('Choose a city'),
  city: Yup.string().required('Choose a city'),
  postalCode: Yup.string().required('Postal code is required'),
  sourceOption: Yup.string().required('Source is required'),
});

export const calculatorFormSchema = Yup.object().shape({
  fullName: Yup.string().min(3, 'Invalid name').max(45, 'To large!').trim().required('Name required'),
  email: Yup.string()
    .matches(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
      'Invalid email'
    )
    .required('Email required'),
  phone: Yup.string()
    .min(10, 'At least 10 characters')
    .max(10, 'No more than 10 characters')
    .required('Phone required'),
  state: createSelectInputValidator('Choose a state'),
  city: createSelectInputValidator('Choose a city'),
  age: Yup.number()
    .min(25, 'Age must be at least 25')
    .max(65, 'Age must be at most 65')
    .required('Age required'),
  postalCode: Yup.string().required('Postal code is required'),
  // sourceOption: Yup.string().required('Source is required'),
  sourceOption: createSelectInputValidator('Source is required'),
  creditScore: Yup.number()
    .min(500, 'Credit score must be at least 500')
    .max(850, 'Credit score cannot exceed 850')
    .required('Credit score is required'),
});
