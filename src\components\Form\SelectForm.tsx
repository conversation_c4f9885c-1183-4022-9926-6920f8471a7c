/* eslint-disable @typescript-eslint/no-shadow */
import { FormControl, FormErrorMessage, FormLabel, Select } from '@chakra-ui/react';
import { Field, FormikErrors, FormikValues, FieldInputProps } from 'formik';
import { Dispatch } from 'react';

interface CustomSelectProps {
  errors: FormikErrors<{
    name: string;
    email: string;
    phone: string;
    city: string;
    message: string;
    [key: string]: string | undefined;
  }>;
  touched: any;
  name: string;
  label: string;
  optionFields: {
    key: string;
    label: string;
  }[];
  firstOptionDisabled: string;
  setCountrySelected?: Dispatch<React.SetStateAction<string>>;
}

export default function SelectForm({
  errors,
  touched,
  name,
  label,
  optionFields,
  firstOptionDisabled,
  setCountrySelected,
}: CustomSelectProps) {
  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <FormControl isInvalid={Boolean(errors[name]) && touched[name]}>
          <FormLabel>{label}</FormLabel>
          <Select
            {...field}
            onChange={(e) => {
              const value = e.currentTarget.value;

              form.setFieldValue(name, value);

              if (name === 'country' && setCountrySelected) {
                setCountrySelected(value);
                form.setFieldValue('state', '');
              }
            }}
          >
            <option value="" disabled>
              {firstOptionDisabled}
            </option>
            {optionFields.map((field) => {
              return (
                <option key={field.key} value={field.key}>
                  {field.label}
                </option>
              );
            })}
          </Select>
          <FormErrorMessage fontSize="13px" fontWeight={600} letterSpacing=".03rem">
            {form.errors[name]}
          </FormErrorMessage>
        </FormControl>
      )}
    </Field>
  );
}
