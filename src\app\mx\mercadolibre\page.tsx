'use client';
/* eslint-disable consistent-return */
/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { Box, Flex, Image, Spinner, Text } from '@chakra-ui/react';
import React, { Dispatch, useEffect, useState } from 'react';
import CustomButton from '@/components/CustomButton';
import Navbar from '@/components/NavBar/Navbar';
import blob from '@/assets/contact/blob.png';
import { Formik } from 'formik';
import Swal from 'sweetalert2';
import axios from 'axios';
import CustomField from '@/components/Form/FieldForm';
import SelectForm from '@/components/Form/SelectForm';
import { cityOptions, contactSchema } from '@/schemas/contactSchema';
import { REQUEST_URL } from '@/constants';

const Contacto = () => {
  const [sending, setSending] = useState(false);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <>
      <Navbar country={'MX'} />
      <Box
        w="100%"
        h="93.6vh"
        minH="93.6vh"
        display="grid"
        gridTemplateColumns={{ base: '100%', l: '60fr 40fr' }}
        position="relative"
        justifyContent="center"
        alignItems="center"
      >
        {sending ? (
          <>
            <Flex
              w="100%"
              h="100%"
              justifyContent="center"
              alignItems="center"
              position="absolute"
              zIndex={3}
            >
              <>
                <Spinner
                  thickness="15px"
                  speed="0.65s"
                  emptyColor="gray.200"
                  color="gray.400"
                  w="200px"
                  h="200px"
                />
              </>
            </Flex>
          </>
        ) : null}
        <Flex
          w="100%"
          h="100%"
          // bgColor="purple.soft"
          bgColor="#7D23FF"
          display={{ base: 'none', l: 'flex' }}
          flexDir="column"
          px="7%"
          pt="14%"
          sx={{ '> *:not(:last-child)': { mb: 5 } }}
          color="white"
          overflow="hidden"
          position="relative"
          fontFamily="Plus-Jakarta-Sans"
          // add a background image
          bgImage={'/meli-ocn.avif'}
          bgRepeat={'no-repeat'}
          bgSize={'contain'}
          bgPosition={'center'}
        >
          {/* <Heading fontFamily="Plus-Jakarta-Sans">MeLi {'<>'} OneCarNow!</Heading> */}
          {/* <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-329px"
            bottom="-22%"
          /> */}
          {/* <Box
            w="458px"
            h="458px"
            border="1px solid #FFFFFF"
            borderRadius="50%"
            position="absolute"
            left="-80px"
            bottom="-33%"
          /> */}
        </Flex>
        <Flex
          w={{ base: '100%', l: '100%' }}
          h="100%"
          justifyContent="center"
          alignItems={{ base: 'start', l: 'center' }}
          bgColor="bgColor"
          py="50px"
          position="relative"
        >
          <Image
            w="12rem"
            h="15rem"
            src={blob.src}
            position="absolute"
            right={0}
            top={'-4rem'}
            zIndex={10}
            display={{ l: 'none' }}
          />
          <Flex
            w={{ base: '90%', l: '70%' }}
            flexDir="column"
            sx={{ '> *:not(:last-child)': { mb: { base: '25px', l: '30px' } } }}
          >
            <FormContact2
              sending={sending}
              setSending={setSending}
              isReady={isReady}
              setIsReady={setIsReady}
            />
          </Flex>
        </Flex>
      </Box>
    </>
  );
};

export default Contacto;

interface MyFormValues {
  name: string;
  email: string;
  phone: string;
  city: string;
  message: string;
  plan: string;
  car: string;
}

function FormContact2({
  sending,
  setSending,
  isReady,
  setIsReady,
}: {
  sending: boolean;
  setSending: Dispatch<React.SetStateAction<boolean>>;
  isReady: boolean;
  setIsReady: Dispatch<React.SetStateAction<boolean>>;
}) {
  const [addPhone, setAddPhone] = useState(false);
  const [requestId, setRequestId] = useState<string | null>(null);
  const [hubspotId, setHubspotId] = useState<string | null>(null);
  // const FormId = localStorage.getItem("formId");

  // const [formId, setFormId] = useState<string>(FormId ? FormId : "")

  // async function sendToNotion(form: any) {
  //   await axios.post(`${process.env.NEXT_PUBLIC_NOTION_API_URL}/submitFormToNotion`, form);
  // }

  async function sendToHubspot(form: MyFormValues) {
    const url = '/api/hubspot/createContact';
    const data = {
      phone: form.phone,
      firstname: form.name,
      ciudad__con_selector_: form.city,
      email: form.email,
      fuente: 'Mercado Libre',
      source: 'JrFLJqT',
      personal_payment: '',
    };
    const response = await axios.post(`${url}`, data);
    setHubspotId(response.data.hubspotId);
    setRequestId(response.data.requestId);
    return response;
  }

  async function updateContact(form: MyFormValues) {
    const url = '/api/hubspot/updateContact';
    const data = {
      requestId,
      hubspotId,
      phone: form.phone,
      firstname: form.name,
      ciudad__con_selector_: form.city,
      email: form.email,
      source: localStorage.getItem('source'),
    };

    const response = await axios.post(`${url}`, data);
    return response;
  }

  return (
    <>
      {!isReady ? (
        <>
          <Text color="text.main" fontSize="30px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
            Contáctanos
          </Text>
          <Formik
            initialValues={{
              name: '',
              email: '',
              phone: '',
              city: '',
              message: '',
              plan: '',
              car: 'Formulario desde apartado de contacto',
            }}
            validationSchema={contactSchema}
            onSubmit={async (values, { resetForm }) => {
              try {
                setSending(true);
                await updateContact(values);

                Swal.fire({
                  title: 'Información enviada',
                  text: 'Te contactáremos lo más pronto posible',
                  icon: 'success',
                  confirmButtonText: 'Cerrar',
                });
                setSending(false);
                resetForm();
                setIsReady(true);
              } catch (e: any) {
                setSending(false);
                resetForm();
                if (e.response.data.message.includes('Ya has enviado tu solicitud'))
                  return Swal.fire({
                    title: 'Pronto te contactaremos 😁',
                    text: e.response.data.message,
                    icon: 'info',
                    confirmButtonText: 'Cerrar',
                  });
                return Swal.fire({
                  title: 'Algo salió mal',
                  text: 'Intenta de nuevo, si el problema persiste porfavor espera a que lo solucionemos',
                  icon: 'error',
                  confirmButtonText: 'Cerrar',
                });
              }
            }}
          >
            {({ errors, touched, handleSubmit, values }) => (
              <form
                onSubmit={handleSubmit}
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <Flex
                  flexDir="column"
                  sx={{ '> *:not(:last-child)': { mb: { base: 3, l: 5 } } }}
                  w={{ base: '90%', l: '100%' }}
                  alignItems="center"
                  fontFamily="Plus-Jakarta-Sans"
                >
                  {!addPhone && (
                    <>
                      <CustomField
                        name="phone"
                        label="Teléfono"
                        touched={touched}
                        errors={errors}
                        type="number"
                        placeholder="Número telefónico"
                      />
                      <CustomButton
                        message="Siguiente"
                        className="btn-purple"
                        disabled={!!sending || !(values.phone && values.phone.toString().length === 10)}
                        handleClick={async () => {
                          // console.log(await sendToHubspot(values));
                          //setAddPhone(true);
                          try {
                            setSending(true);
                            await sendToHubspot(values);
                            setSending(false);
                            setAddPhone(true);
                          } catch (e: any) {
                            setSending(false);
                            if (e.response.data.message.includes('Ya has enviado tu solicitud'))
                              return Swal.fire({
                                title: 'Pronto te contactaremos 😁',
                                text: e.response.data.message,
                                icon: 'info',
                                confirmButtonText: 'Cerrar',
                              });
                          }
                        }}
                      />
                    </>
                  )}

                  {addPhone && (
                    <>
                      <CustomField
                        name="name"
                        label="Nombre completo"
                        touched={touched}
                        // handleBlur={handleBlur}
                        errors={errors}
                        type="text"
                        placeholder="Nombres..."
                      />

                      <CustomField
                        name="email"
                        label="Correo electrónico"
                        touched={touched}
                        // handleBlur={handleBlur}
                        errors={errors}
                        type="email"
                        placeholder="<EMAIL>"
                      />

                      <SelectForm
                        name="city"
                        label="Estado"
                        errors={errors}
                        touched={touched}
                        firstOptionDisabled="Ingresa tu ciudad"
                        optionFields={cityOptions}
                      />
                      <CustomButton
                        message="Enviar"
                        className="btn-purple"
                        type="submit"
                        disabled={!!sending}
                      />
                    </>
                  )}
                </Flex>
              </form>
            )}
          </Formik>
        </>
      ) : (
        <Flex flexDir="column" alignItems="center" gap={15}>
          <Text color="text.main" fontSize="25px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
            ¡Continúa tu aplicación!
          </Text>
          <Text color="text.main" fontSize="15px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
            Comienza tu proceso ahora mismo y estrena en cuanto antes.
          </Text>
          <CustomButton
            message="Comenzar aplicación"
            className="btn-purple"
            handleClick={() => {
              window.open(`${REQUEST_URL}/?id=${requestId}`, '_blank');
            }}
          />
        </Flex>
      )}
    </>
  );
}
