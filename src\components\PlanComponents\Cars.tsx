/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/rules-of-hooks */
import { Button, Center, Flex, SimpleGrid, Text } from '@chakra-ui/react';
import Link from 'next/link';
import CarCard from '../CarCard';

const buttonBlue = 'linear-gradient(95.48deg, #028CF3 9.55%, #6FF7E8 128.92%)';
const buttonGreen = 'linear-gradient(95.48deg, #43D17D 9.55%, #9EF01A 128.92%)';

interface CarsProps {
  personal: any[];
  platform: any[];
  isPersonalPlan?: boolean;
}

export default function Cars({ platform, isPersonalPlan }: CarsProps) {
  // Determinar las columnas basado en la cantidad de elementos
  const getColumns = () => {
    const itemCount = platform.length;
    if (itemCount <= 2) {
      return { base: 1, md: itemCount, lg: itemCount };
    }
    return { base: 1, md: 2, lg: 3 };
  };

  // Determinar si usar flex centrado para pocos elementos
  const shouldUseFlex = platform.length <= 2;

  return (
    <Flex w="100%" h="100%" justifyContent="center" bgColor="bgColor">
      <Flex w="90%" h="100%" flexDir="column" alignItems="center" sx={{ '> *:not(:last-child)': { mb: 20 } }}>
        <Flex w="100%" flexDir={'column'} alignItems="center" mt="100px">
          <Text
            color={isPersonalPlan ? '#028CF3' : 'purple.strong'}
            fontFamily="Plus-Jakarta-Sans"
            fontSize="20px"
            fontWeight={500}
          >
            Vehículos
          </Text>
          <Text fontSize="36px" fontFamily="Plus-Jakarta-Sans" fontWeight={700}>
            Autos destacados
          </Text>
        </Flex>
        {shouldUseFlex ? (
          <Flex h="max-content" justifyContent="center" alignItems="center" gap={10} flexWrap="wrap">
            {!isPersonalPlan &&
              platform.map((e, i) => {
                return (
                  <CarCard
                    key={i}
                    title={e.name}
                    image={e.images[0]}
                    price={e.payment}
                    seats={e.seats}
                    transmission={e.transmission}
                    plan={e.plan}
                    id={e.id}
                    cardBtnColor="purple.strong"
                    hoverColor="purple.soft"
                  />
                );
              })}
          </Flex>
        ) : (
          <SimpleGrid
            h="max-content"
            columns={getColumns()}
            justifyContent="center"
            sx={{ '> *:not(:last-child)': { mr: 10, mb: { base: 10, lg: 0 } } }}
          >
            {!isPersonalPlan &&
              platform.map((e, i) => {
                return (
                  <CarCard
                    key={i}
                    title={e.name}
                    image={e.images[0]}
                    price={e.payment}
                    seats={e.seats}
                    transmission={e.transmission}
                    plan={e.plan}
                    id={e.id}
                    cardBtnColor="purple.strong"
                    hoverColor="purple.soft"
                  />
                );
              })}
            </SimpleGrid>
        )}
        <Center mt="30px">
          <Link href="/mx/autos">
            <Button
              w="210px"
              h="50px"
              transition=".5s"
              backgroundSize="200% auto"
              fontFamily="Plus-Jakarta-Sans"
              fontWeight={700}
              className="btn-green"
              bgImage={isPersonalPlan ? buttonBlue : buttonGreen}
              color="white"
              borderRadius={'25px'}
              _hover={{
                bgPosition: 'right center',
              }}
            >
              {' '}
              Ver más
            </Button>
          </Link>
        </Center>
      </Flex>
    </Flex>
  );
}
