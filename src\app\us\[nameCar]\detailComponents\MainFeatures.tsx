import { IVehicleData } from '../data/data';

export default function MainFeatures({ mainFeatures }: { mainFeatures: IVehicleData['mainFeatures'] }) {
  return (
    <div className="w-full h-[max-content] flex flex-col gap-[20px] font-[Plus-Jakarta-Sans]">
      <p className="text-[24px] text-[#3F404C] font-bold ">Main features</p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-y-[20px] gap-x-[25px] ">
        <div className="flex flex-col gap-[20px] text-[#464E5F] ">
          <p className="text-[#742BFA] text-[18px] ">Interior</p>
          <ul className="grid gap-[20px] ml-0 list-disc">
            {mainFeatures.inside.map((t, i) => (
              <li key={i}>{t}</li>
            ))}
          </ul>
        </div>
        <div className="flex flex-col gap-[20px] ">
          <p className="text-[#742BFA] text-[18px]">Exterior</p>
          <ul className="grid gap-[20px] ml-0 list-disc  ">
            {mainFeatures.abroad.map((t, i) => (
              <li key={i}>{t}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
