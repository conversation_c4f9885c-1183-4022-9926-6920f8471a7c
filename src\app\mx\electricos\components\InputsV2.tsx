import { FormControl, FormErrorMessage, Select } from '@chakra-ui/react';
import { Field, FieldInputProps, FormikErrors, FormikTouched, FormikValues, useFormikContext } from 'formik';
import { useEffect } from 'react';

interface CustomInputProps {
  name: string;
  label: string;
  errors: FormikErrors<Record<string, string | Object>>;
  touched: FormikTouched<Record<string, string | Object>>;
  type?: 'text' | 'number';
}

export function CustomEVInputMX({ name, label, errors, touched, type }: CustomInputProps) {
  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <>
          <FormControl
            className="grid gap-3"
            isInvalid={Boolean(errors ? errors[name] : '') && touched && touched[name]}
          >
            <label htmlFor={name}>{label}</label>
            <div className="relative ">
              <input
                className="border-[2px] w-full sm:w-[270px] h-[46px] border-gray-[#CED4DA] rounded-md px-3 focus:outline-[#742BFA] font-normal"
                type={type || 'text'}
                {...field}
                // placeholder={placeholder}
                // onBlur={(e) => handleBlur(e, form)}
              />
              <FormErrorMessage
                className="absolute bottom-[-24px] "
                fontSize="13px"
                fontWeight={600}
                letterSpacing=".03rem"
              >
                {form.errors[name]}
              </FormErrorMessage>
            </div>
          </FormControl>
        </>
      )}
    </Field>
  );
}

type Option = {
  label: string;
  value: string;
};
interface CustomSelectInputProps {
  name: string;
  label: string;
  errors: FormikErrors<Record<string, string | Object>>;
  touched: FormikTouched<Record<string, string | Object>>;
  options: Option[];
  dynamicOption?: {
    value: string;
    label: string;
  };
  onChange?: (option: Option) => void;
}

export function SelectEVInputMX({
  name,
  label,
  errors,
  touched,
  options,
  dynamicOption,
}: // onChange,
CustomSelectInputProps) {
  const { setFieldValue } = useFormikContext();

  useEffect(() => {
    if (dynamicOption) {
      setFieldValue(name, dynamicOption);
    }
  }, [dynamicOption, name, setFieldValue]);

  return (
    <Field name={name}>
      {({ field, form }: { field: FieldInputProps<any>; form: any }) => {
        return (
          <>
            <FormControl
              className="grid gap-3"
              isInvalid={Boolean(errors ? errors[name] : '') && touched && touched[name]}
            >
              <label htmlFor={name}>{label}</label>
              <div className="relative w-[full] sm:w-[270px]">
                <Select
                  {...field}
                  _focus={{
                    borderColor: '#742BFA',
                  }}
                  // onChange={(e) => {
                  //   const find = options.find((op) => op.value === e.target.value);
                  //   form.setFieldValue(name, e.target.value);
                  //   console.log(find);
                  //   if (onChange) {
                  //     console.log(find);
                  //     // onChange(value);
                  //   }
                  // }}
                  className="border-[2px] h-[46px] font-normal border-gray-[#CED4DA] bg-white rounded-md px-3 sm:pr-[50px] md:pr-[30px]  "
                  style={{
                    textOverflow: 'ellipsis', // Agrega este estilo para recortar el texto
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {options?.map((op) => (
                    <option key={op.value} value={op.value}>
                      {op.label}
                    </option>
                  ))}
                  {/* <option value="Tesla Model Y">Tesla Model Y Long Range</option>
                <option value="Mustan March">Mustang March-E Premium</option>
                <option value="Kia EV6">KIA EV6 Wind (RWD)</option> */}
                </Select>
                <FormErrorMessage
                  className="absolute bottom-[-24px] "
                  fontSize="13px"
                  fontWeight={600}
                  letterSpacing=".03rem"
                >
                  {form.errors[name]}
                </FormErrorMessage>
              </div>
            </FormControl>
          </>
        );
      }}
    </Field>
  );
}
