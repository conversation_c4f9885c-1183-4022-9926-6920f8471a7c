'use client';
/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react-hooks/rules-of-hooks */
import { useEffect, useMemo, useState } from 'react';
import { Flex, SimpleGrid, Stack } from '@chakra-ui/react';
import ModalButton from '@/components/Modal/Modal';
import Footer from '@/components/Footer';
import AdditionalData from '@/components/VehicleDetail/AdditionalData';
import PlatformSlider from '@/components/VehicleDetail/SliderCarImages';
import Description from '@/components/VehicleDetail/Description';
import InputPlan from '@/components/VehicleDetail/InputPlan';
import useTabPlanSelected from '@/store/zustand/planSelected';

// const emptyPlans = [{ label: '', biweeklyPay: 0, deposit: 0 }];

export type PlanOption = {
  label: string;
  biweeklyPay: number;
  deposit: number;
};

export default function VehicleDetailPersonal({ data }: { data: any }) {
  const [selectedPlan, setSelectedPlan] = useState(1);
  const [namePlan, setNamePlan] = useState('');
  const [planOptions, setPlanOptions] = useState<PlanOption[]>(data.paymentOptions);

  const optionDetail = useMemo(() => {
    const option = planOptions.find((option) => option.label === namePlan);
    if (!option) return null;
    const { label, deposit, biweeklyPay } = option;

    // @formatter:off
    const info = `${label}
                  Pago quincenal de $${biweeklyPay.toLocaleString()}
                  con pago inicial de $${deposit.toLocaleString()}`;

    const message = info.replace(/^\s+/gm, '');
    return message;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [planOptions, namePlan]);
  const planTabSelected = useTabPlanSelected();
  // optionDetail()

  useEffect(() => {
    planTabSelected.setPlanSelected(1);
    setPlanOptions(data.paymentOptions);

    // setNamePlan(data.paymentOptions[0].label);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  const paymentOptions = data.paymentOptions;
  const parsedPaymentOptions = paymentOptions.map((option: any) => ({
    label: option.label,
    biweeklyPay: option.biweeklyPay.toLocaleString(),
    deposit: option.deposit.toLocaleString(),
  }));
  // if (!loading && !filteredData[0]) return <PageNotFound />;
  // const vehicle = filteredData[0]

  return (
    <>
      <Flex as="main" w="100%" h={'100%'} justifyContent="center" bgColor="#FAFAFF">
        <Flex
          minH={'100vh'}
          w={{ base: '85%', l: '90%', lg: '85%' }}
          h="100%"
          flexDir={'column'}
          alignItems="center"
        >
          <Flex
            flexDir={{ base: 'column', l: 'row' }}
            w={{ base: '100%', lg: '100%' }}
            sx={{ '> *:not(:last-child)': { mb: { base: 5, l: 0 }, mr: { base: 0, l: 10 } } }}
            mt={{ base: '0px', md: '50px' }}
            height="100%"
          >
            <Flex flexDir="column" w={{ base: '100%', l: '52%' }} height="100%">
              <PlatformSlider images={data.images} borderColor="#3563E9" />
            </Flex>
            <Flex
              direction="column"
              w={{ base: '100%', l: '50%' }}
              justify="space-around"
              mt={{ base: '110px', md: '100px', l: 0 }}
            >
              <Description
                personal
                loading={false}
                name={data.name as string}
                durationMonths={data.durationMonths}
                description={data.description as string}
              />

              <Stack mt="30px" sx={{ '> *:not(:last-child)': { mb: 3 } }}>
                <SimpleGrid
                  w="max-content"
                  columns={{ base: 1, md: 2, l: 1, cards: 2 }}
                  sx={{
                    '> *:not(:last-child)': { mr: '14px' },
                  }}
                >
                  {(parsedPaymentOptions as any[]).map((p, i) => {
                    return (
                      <InputPlan
                        key={p.label}
                        selectedPlan={selectedPlan}
                        setSelectedPlan={setSelectedPlan}
                        index={i + 1}
                        data={p}
                        setNamePlan={setNamePlan}
                        namePlan={p.label}
                      />
                    );
                  })}
                </SimpleGrid>

                <Flex justify={'center'}>
                  <ModalButton
                    backgroundColor="linear-gradient(90deg, #028CF3 39.3%, #6FF7E8 111.6%)"
                    car={data.name}
                    plan={data.plan}
                    selectedPlan={selectedPlan}
                    namePlan={namePlan}
                    hasPlan={true}
                    optionDetail={optionDetail}
                  />
                </Flex>
              </Stack>
            </Flex>
          </Flex>

          <AdditionalData
            loading={false}
            aditionalData={data.aditionalData}
            security={data.security}
            interior={data.interior}
            exterior={data.exterior}
            specialDetails={data.specialDetails}
            borderColor="btn-blue"
          />
        </Flex>
      </Flex>
      <Footer footerBgColor="footer.personal" />
    </>
  );
}
