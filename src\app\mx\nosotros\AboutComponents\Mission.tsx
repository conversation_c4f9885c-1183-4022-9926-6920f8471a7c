import { Box, Flex, Heading, Image, Text } from '@chakra-ui/react';
import frame from '@/assets/about/frame.png';

const Mission = () => {
  return (
    <Flex
      sx={{
        '> *:not(:last-child)': { mb: { base: 12, l: 0 }, mr: { base: 0, l: 5 } },
      }}
      py="70px"
      h="100%"
      justify={'center'}
      flexDir={{ base: 'column', l: 'row' }}
      align="center"
    >
      <Flex direction={'column'} sx={{ '> *:not(:last-child)': { mb: 5 } }} w={{ base: '100%', l: '80%' }}>
        <Heading fontSize={'32px'} color="text.description">
          Nuestra mision
        </Heading>

        <Text color={'text.description'} fontSize="15px" w={{ base: '100%', l: '90%' }}>
          Creemos que transformar la forma de estrenar un auto puede impulsar el desarrollo económico y
          tecnológico.
          <br />
          <br />
          Trabajamos para hacer una diferencia positiva comprometidos con prácticas éticas y sostenibles que
          nos ayudarán a mejorar la experiencia de movilidad de las personas.
        </Text>
      </Flex>

      <Box w={{ base: '90%', l: '40%' }} position="relative">
        <Box
          w={{ base: '50px', md: '70px' }}
          h={{ base: '50px', md: '70px' }}
          borderRadius="50%"
          border={{ base: '11px solid #47EB84', md: '15px solid #47EB84' }}
          position="absolute"
          left={{ base: '-20px', md: '-30px' }}
          top={{ base: '-20px', md: '-30px' }}
          zIndex={0}
        />

        <Image
          src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/about%2Fcine.jpg?alt=media&token=eada7350-7f4f-4d51-a209-228b6086c6e4"
          borderRadius="1em"
          objectFit="contain"
          position="sticky"
          zIndex={1}
          alt="-"
        />

        <Box
          w={{ base: '30%', md: '40%' }}
          h={{ base: '30%', md: '40%' }}
          backgroundImage={frame.src}
          position="absolute"
          right={{ base: '-17%', md: '-22%' }}
          bottom="-15%"
          backgroundSize="contain"
          backgroundRepeat="no-repeat"
        />
      </Box>
    </Flex>
  );
};

export default Mission;
