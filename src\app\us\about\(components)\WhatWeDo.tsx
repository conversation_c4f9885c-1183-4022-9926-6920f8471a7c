import { Flex, Heading, Text } from '@chakra-ui/react';
import React from 'react';

const WhatWeDo = () => {
  return (
    <Flex
      py={{ base: '60px', md: '120px' }}
      justify={'center'}
      align={{ base: 'start', l: 'center' }}
      flexDir={{ base: 'column', l: 'row' }}
      sx={{ '> *:not(:last-child)': { mb: { base: '10vw', l: 0 }, mr: { base: 0, l: '10vw' } } }}
    >
      <Heading fontSize={'32px'} color="text.description">
        What do we do?
      </Heading>

      <Text fontSize={'15px'} color="text.description" lineHeight="30px" w={{ base: '100%', l: '60%' }}>
        At OCN we strive to provide a different and innovative solution for getting a new car, an easy and
        simple option that includes everything except gasoline. <br />
        <br />
        That is why we created the first car subscription service in Mexico, allowing our customers to get a
        new car in the medium or long term. We strive every day to be industry leaders, adapting to the needs
        of our customers, offering flexible solutions and a completely digital experience.
      </Text>
    </Flex>
  );
};

export default WhatWeDo;
