import { BsCheckCircle } from 'react-icons/bs';

export default function HowQualify() {
  return (
    <div className="w-full flex justify-start xl:justify-center font-[Plus-Jakarta-Sans] text-[#464E5F] ">
      <div className="flex flex-col w-[600px] gap-[25px] ">
        <p className="text-[#742BFA] text-[24px] ">How do I qualify?</p>
        <div className="flex gap-3 items-center">
          <BsCheckCircle color="#00DE53" />
          <p>Complete at least 800 rides on ride sharing platforms (i.e. Uber, Lyft).</p>
        </div>
        <div className="flex gap-3 items-center">
          <BsCheckCircle color="#00DE53" />
          <p>Maintain a minimum rating of 4.8 stars.</p>
        </div>
        <div className="flex gap-3 items-center">
          <BsCheckCircle color="#00DE53" />
          <p>Have more than 1 year in ride share experience.</p>
        </div>
      </div>
    </div>
  );
}
