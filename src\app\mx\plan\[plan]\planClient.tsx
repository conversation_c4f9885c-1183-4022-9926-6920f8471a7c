'use client';
import { Box } from '@chakra-ui/react';
import { useEffect } from 'react';
import {
  Benefits,
  CarLogos,
  Cars,
  ContactUs,
  Main,
  Requirements,
  SectionPlan,
} from '@/components/PlanComponents';
import useTabPlanSelected from '@/store/zustand/planSelected';

interface PlansClientProps {
  isPersonal: boolean;
  personal: any[];
  platform: any[];
}

export default function PlansClient({ isPersonal, personal, platform }: PlansClientProps) {
  const planTabSelected = useTabPlanSelected();
  useEffect(() => {
    if (!isPersonal) planTabSelected.setPlanSelected(0);
    if (isPersonal) planTabSelected.setPlanSelected(1);
    window.scrollTo({ top: 0, left: 0 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPersonal]);

  return (
    <>
      <Box as="main" w="100%" h="max-content" transition="1s">
        <Main isPersonalPlan={isPersonal} />
      </Box>
      {/* Logotipos */}
      <CarLogos />

      <SectionPlan isPersonalPlan={isPersonal} />

      <Benefits isPersonalPlan={isPersonal} />

      <Requirements isPersonalPlan={isPersonal} />

      {/* Autos destacados */}

      <Cars personal={personal} platform={platform} isPersonalPlan={isPersonal} />

      <ContactUs isPersonalPlan={isPersonal} />
    </>
  );
}
