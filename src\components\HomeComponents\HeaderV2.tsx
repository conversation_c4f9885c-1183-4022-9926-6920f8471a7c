import Image from 'next/image';
import CustomButton from '@/components/CustomButton';
import { COUNTRY_DATA } from '@/constants';
import { redirect } from 'next/navigation';
import { headers } from 'next/headers';

export default function HeaderV2({ country }: { country?: string }) {
  console.log('country', country);
  const headersList = headers();

  const headersObj = Object.fromEntries(headersList.entries());

  const isMobile = headersObj['user-agent']?.toLowerCase().includes('mobile');

  return (
    <div
      className={`
        flex
        w-full
        h-full md:h-[90vh]
        min-h-full md:min-h-[600px]
        py-20 md:py-0
        md:overflow-hidden
        flex-col md:flex-row
        items-center relative
        overflow-x-hidden
      `}
    >
      <div
        className={`
        w-full md:w-[850px]
        flex flex-col
        z-[1]
        pl-5 md:pl-[50px] lg:pl-[100px]
        font-[Plus-Jakarta-Sans]
        mb-3 md:mb-10
        last:mb-0 md:last:mb-0
      `}
      >
        <div
          className={`
          bg-purple-light bg-opacity-20 w-[330px] md:w-[360px] h-[40px]
          rounded-full text-purple-700 flex items-center justify-center
          mb-4 md:mb-10
        `}
          style={{
            backgroundColor: 'rgba(195, 198, 255, 0.7)',
          }}
        >
          <h2 className="text-sm md:text-[15px] font-normal text-center font-sans">
            {/* {country === 'MX' ? COUNTRY_DATA.MX.home.hero.text : COUNTRY_DATA.US.home.hero.text} */}
            {COUNTRY_DATA.MX.home.hero.text}
          </h2>
        </div>

        <h1
          className={`
          w-[80%] md:w-full
          text-main mb-4 md:mb-10
          text-[40px]
        `}
        >
          {/* {country === 'MX' ? COUNTRY_DATA.MX.home.hero.title : COUNTRY_DATA.US.home.hero.title} */}
          {COUNTRY_DATA.MX.home.hero.title}
          <span className="font-semibold text-purple-strong"> {COUNTRY_DATA.MX.home.hero.purple}</span>
        </h1>

        <hr
          className={`
            w-[46px]
            hidden md:block
            rounded
            border-[2px]
            border-[#9E8EFF]
            opacity-60
            mb-4 font-sans
          `}
        />

        <p className="hidden mb-4 md:block text-main">
          {/* {country === 'MX' ? COUNTRY_DATA.MX.home.hero.subtitle : COUNTRY_DATA.US.home.hero.subtitle} */}
          {COUNTRY_DATA.MX.home.hero.subtitle}
        </p>

        <form
          className="hidden md:block w-[max-content]"
          action={async () => {
            'use server';
            redirect('/mx/autos');
          }}
        >
          <CustomButton
            // message={country === 'MX' ? COUNTRY_DATA.MX.home.hero.button : COUNTRY_DATA.US.home.hero.button}
            message={COUNTRY_DATA.MX.home.hero.button}
            className="w-full h-full btn-purple"
            aria-label="Ver autos"
            type="submit"
          >
            {/* {country === 'MX' ? COUNTRY_DATA.MX.home.hero.button : COUNTRY_DATA.US.home.hero.button} */}
            {COUNTRY_DATA.MX.home.hero.button}
          </CustomButton>
        </form>
      </div>

      <div className="relative grid items-center w-full h-full">
        <div className="relative grid items-center w-full h-full">
          <div
            className={`
            w-full h-full
            z-[3]
            top-0 md-top-[10%]
            right-0 md:right-[-12%]
            l:absolute
          `}
          >
            <div className="relative h-[300px] md:h-[90%] w-full">
              <Image
                src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fsentra.webp?alt=media&token=704d4dc8-1a6b-4565-88b5-6fb7a85bce4d"
                alt="Sentra"
                fill
                priority
                sizes="(max-width: 768px) 100vw, 50vw"
                className="object-contain l:object-right"
              />
            </div>
          </div>
        </div>

        <div
          className="
          w-full
          h-[50vh] md:h-full
          absolute
          top-[-200px] lg:top-[-100px]
          right-0
          hidden l:block
        "
        >
          <Image
            src="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fdecore.webp?alt=media&token=08b31d1c-13d1-4ea9-8a4b-2d5af5a713f9"
            alt="Decorative background"
            fill
            priority={!isMobile} // If it's not mobile, load the image first, otherwise load it after the page is loaded
            sizes="(max-width: 768px) 100vw, 50vw"
            className="object-contain object-right"
          />
        </div>
      </div>

      <form
        className="block md:hidden "
        action={async () => {
          'use server';
          redirect('/mx/autos');
        }}
      >
        <CustomButton
          // message={country === 'MX' ? COUNTRY_DATA.MX.home.hero.button : COUNTRY_DATA.US.home.hero.button}
          message={COUNTRY_DATA.MX.home.hero.button}
          className="btn-purple"
          aria-label="Ver autos"
          type="submit"
          // handleClick={async () => {
          //   'use server';
          //   redirect('/mx/autos');
          // }}
        />
      </form>
    </div>
  );
}
