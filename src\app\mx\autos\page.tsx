import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import Catalogue from './autosClient';
// import { API_URL } from '@/constants';
// import axios from 'axios';
import { getAllPersonalVehicles, getAllPlatformVehicles } from '@/middlewares/getVehiclesFirebase';

// async function getPersonal() {
//   try {
//     const platformVehicles = await axios(API_URL + '/vehicles/get/personal');
//     return platformVehicles.data.vehicles;
//   } catch (error) {
//     console.error(error);
//     return null;
//   }
// }

// async function getPlatform() {
//   try {
//     const platformVehicles = await axios(API_URL + '/vehicles/get/platform');
//     return platformVehicles.data.vehicles;
//   } catch (error) {
//     console.error(error);
//     return null;
//   }
// }

export default async function AutosPage() {
  const platformVehicles = await getAllPlatformVehicles();
  const personalVehicles = await getAllPersonalVehicles();
  return (
    <>
      <Navbar country={'MX'} />
      <Catalogue personal={personalVehicles} platform={platformVehicles} />
      <Footer footerBgColor="footer.main" />
    </>
  );
}
