'use client';
// import { useEffect, useState } from 'react';

// export default function useHandleResize({ breakpoint }: { breakpoint: number }) {
//   const [showImg, setShowImg] = useState(false);
//   useEffect(() => {
//     const handleResize = () => {
//       setShowImg(window.innerWidth < breakpoint);
//     };
//     window.addEventListener('resize', handleResize);
//     handleResize();
//     return () => window.removeEventListener('resize', handleResize);
//   }, [breakpoint]);
//   return showImg;
// }

import { useEffect, useState } from 'react';

export default function useHandleResize({ breakpoint }: { breakpoint: number }) {
  const [showImg, setShowImg] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(`(max-width: ${breakpoint - 1}px)`);

    const handleResize = (event: MediaQueryListEvent | MediaQueryList) => {
      setShowImg((event as MediaQueryListEvent).matches);
    };

    // Agregar el oyente de eventos para el media query
    mediaQuery.addEventListener('change', handleResize);

    // Llamar a handleResize una vez para inicializar el estado
    handleResize(mediaQuery);

    return () => {
      // Eliminar el oyente de eventos cuando el componente se desmonte
      mediaQuery.removeEventListener('change', handleResize);
    };
  }, [breakpoint]);

  return showImg;
}
// Este código utiliza window.matchMedia para crear un objeto MediaQueryList, que te permite verificar si la ventana coincide con la media query especificada. Luego, se agrega un oyente de eventos para el cambio de estado del media query, y cuando cambia, se llama a la función handleResize para actualizar el estado showImg en función de si el media query coincide o no. El último paso es asegurarse de eliminar el oyente de eventos cuando el componente se desmonta para evitar posibles problemas de fugas de memoria.
