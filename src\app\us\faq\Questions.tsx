import {
  AccordionButton,
  AccordionIcon,
  AccordionItem,
  AccordionPanel,
  Box,
  Flex,
  Text,
} from '@chakra-ui/react';
import React from 'react';

const Questions = ({
  question,
  answer,
  indexOpen,
  toggleOpen,
}: {
  question: string;
  answer: string[];
  indexOpen: boolean;
  toggleOpen: (index: any) => void;
}) => {
  return (
    <Flex
      h="max-content"
      minH="132px"
      w={{ base: '95%', md: '60%' }}
      alignItems="center"
      boxShadow="0px 6px 16px rgba(74, 58, 255, 0.19)"
      border={indexOpen ? '2.5px solid #742BFA' : 'transparent'}
      borderRadius="15px"
      justifyContent="space-between"
      px="20px"
      mb="30px"
      bgColor="#FFFFFF"
    >
      <AccordionItem w="100%" border="none" fontFamily="Plus-Jakarta-Sans" color="text.main">
        {({ isExpanded }) => (
          <>
            <AccordionButton
              w="100%"
              h="100%"
              minH="70px"
              _hover={{ bgColor: 'white', borderRadius: '30px' }}
              sx={{ '> *:not(:last-child)': { mr: { base: 2, md: 0 } } }}
              px={{ base: 'none', md: 5 }}
              onClick={toggleOpen}
            >
              <Box
                as="span"
                flex="1"
                textAlign="left"
                fontSize={{ base: '15px', md: '16px' }}
                fontWeight={600}
              >
                {question}
              </Box>

              {isExpanded ? (
                <Box
                  borderRadius={'50%'}
                  bgColor="purple.strong"
                  boxShadow={'2px 5px 16px rgba(8, 15, 52, 0.06)'}
                >
                  <AccordionIcon boxSize={'30px'} color="white" />
                </Box>
              ) : (
                <Box borderRadius={'50%'} boxShadow={'2px 5px 16px rgba(8, 15, 52, 0.06)'}>
                  <AccordionIcon boxSize={'30px'} color="purple.strong" />
                </Box>
              )}
            </AccordionButton>

            <AccordionPanel pb={5} sx={{ '> *:not(:last-child)': { mb: '8px' } }}>
              {answer.map((e, i) => {
                return (
                  <Text key={i} fontSize={{ base: '14px', md: '15px' }} color="#6F6C90">
                    {e}
                  </Text>
                );
              })}
            </AccordionPanel>
          </>
        )}
      </AccordionItem>
    </Flex>
  );
};

export default Questions;
