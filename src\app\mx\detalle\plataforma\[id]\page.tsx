import Navbar from '@/components/NavBar/Navbar';
import Footer from '@/components/Footer';
import VehicleDetailPlatform from './detailClient';
import NotFoundMX from '@/app/NotFoundMX';
// import { getByIdPlatformVehicle } from '@/app/getters/getByIdPlatform';
// import { getPlatformById } from '@/middlewares/getVehiclesFirebase';
import { platformVehiclesData } from '../../../vehicles.data';

interface DetailPlatformProps {
  params: {
    id: string;
  };
}

// Función helper para buscar vehículo por ID en los datos hardcodeados
function getPlatformByIdFromData(id: string) {
  return platformVehiclesData.find((vehicle) => vehicle.id === id) || null;
}

export async function generateMetadata({ params }: DetailPlatformProps) {
  try {
    const vehicle = getPlatformByIdFromData(params.id);
    if (!vehicle)
      return {
        title: 'Not found',
        description: 'Page not found',
      };
    return {
      title: vehicle.name + ' | OCN',
      description: vehicle.description,
      openGraph: {
        images: [vehicle.images[0]],
      },
    };
  } catch (error) {
    return {
      title: 'Not found',
      description: 'Page not found',
    };
  }
}

export default function DetailPlatform({ params }: DetailPlatformProps) {
  const vehicle = getPlatformByIdFromData(params.id);
  if (!vehicle) return <NotFoundMX />;
  return (
    <>
      <Navbar country={'MX'} />
      <VehicleDetailPlatform data={vehicle} />
      <Footer footerBgColor="footer.platform" />
    </>
  );
}
