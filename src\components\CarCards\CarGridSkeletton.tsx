import React from 'react';
import { Card, Center, Flex, Skeleton } from '@chakra-ui/react';

const CarGridSkeletton = () => {
  return (
    <Center w="100%">
      <Card
        w="320px"
        h="348px"
        borderRadius="10px"
        fontFamily="Plus-Jakarta-Sans"
        p="25px"
        boxShadow="0px 12px 32px rgba(195, 198, 255, 0.2)"
        position="relative"
      >
        <Skeleton h="24px" w="100px" />

        <Flex w="100%" justifyContent={'center'} alignItems="center">
          <Skeleton mt="15px" w="250px" h="160px" />
        </Flex>

        <Flex
          justifyContent="end"
          color="#90A3BF"
          sx={{ '> *:not(:last-child)': { mr: 3 } }}
          fontSize="13px"
          mt="5px"
        >
          <Skeleton w="70px" h="16px" mt="5px" />
        </Flex>

        <Flex alignItems="center" sx={{ '> *:not(:last-child)': { mr: 1 } }}>
          <Skeleton w="70px" h="16px" mt="5px" />
        </Flex>

        <Flex alignItems="center" sx={{ '> *:not(:last-child)': { mr: 1 } }}>
          <Skeleton w="70px" h="16px" mt="5px" />
        </Flex>

        <Flex
          mt="30px"
          justifyContent="space-between"
          alignItems="center"
          w="85%"
          position="absolute"
          bottom={'15px'}
        >
          <Flex>
            <Skeleton h="28px" w="100px" />
          </Flex>

          <Skeleton h="35px" w="90px" />
        </Flex>
      </Card>
    </Center>
  );
};

export default CarGridSkeletton;
