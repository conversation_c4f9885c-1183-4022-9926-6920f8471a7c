/* eslint-disable @typescript-eslint/no-use-before-define */
import { useState } from 'react';
import Slider from 'react-slick';
import { AiOutlineCloseCircle } from 'react-icons/ai';
import { SlArrowLeft, SlArrowRight } from 'react-icons/sl';
import Image, { StaticImageData } from 'next/image';

interface CarouselElectricVehiclesProps {
  toggleModal: () => void;
  images: StaticImageData[];
  initialSlide: number;
}

export default function USCarrouselDetail({
  toggleModal,
  images,
  initialSlide,
}: CarouselElectricVehiclesProps) {
  const [slider, setSlider] = useState<Slider | null>(null);
  const [currentSlide, setCurrentSlide] = useState(initialSlide);

  const settings = {
    dots: true,
    dotsToShow: 3,
    arrows: false,
    fade: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    customPaging: (i: any) => {
      return (
        <div key={i} className="w-[100%] h-[100%] flex justify-center items-center">
          <Image
            width={1000}
            height={1000}
            src={images[i]}
            // alt="image"
            alt={`image-${i}`}
            className="w-full h-full object-contain rounded-[8px] aspect-[16/9]"
          />
        </div>
      );
    },
    dotsClass: 'slick-dots slick-thumb',
  };

  return (
    <>
      <div
        onClick={(event) => {
          if (event.target === event.currentTarget) {
            // Cerrar el modal solo si se hace clic en el fondo blanco
            toggleModal();
          }
        }}
        className="
          flex flex-col
          justify-center items-center
          w-full h-[100svh]
          fixed top-0 left-0
          z-[990]

        "
        style={{
          backgroundImage: 'linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7))',
        }}
      >
        <style>
          {
            /* put some css using backticks */
            `
          .slick-slider,
.slick-initialized,
.slick-track,
.slick-slide {
  align-self: center !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}


.slick-dots,
.slick-thumb {
  margin-bottom: 5px;
  width: 100%;
  min-height: 70px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  background-color: #ffffff;
  border-radius: 0px 0px 15px 15px;
  padding-top: 4px !important;
  padding-bottom: 4px !important;
  padding-left: 6% !important;
  padding-right: 6% !important;
}



.slick-thumb li {
  width: 90% !important;
  height: 90% !important;

}

.slick-thumb .slick-active {
  border: 3px solid #00b3d9;
  border-radius: 8px;
}

`
          }
        </style>
        <div id="global-slider" className="w-[85%] md:w-[70%] bg-transparent relative">
          <link
            rel="stylesheet"
            type="text/css"
            charSet="UTF-8"
            href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
          />
          {/* <link rel="stylesheet" href="./slick-styles.css" type="text/css" /> */}
          <link
            rel="stylesheet"
            type="text/css"
            href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
          />
          <button
            aria-label="Previous"
            className="
              flex
              w-[30px] h-[30px]
              rounded-[50%]
              justify-center items-center
              cursor-pointer
              z-[10]
              p-0
              bg-transparent
              absolute
              left-2
              md:bottom-[-1.3%] 
              xl:bottom-[-1%]
              2xl:bottom-[-1%]
              3xl:bottom-[-.1%]
              text-black
              hover:bg-transparent
            "
            onClick={() => {
              const prevIndex = currentSlide === 0 ? 0 : currentSlide - 1; // Aumenta el índice actual en 1
              setCurrentSlide(prevIndex); // Actualiza el índice actual
              slider?.slickGoTo(prevIndex);
            }}
            // _hover={{ bgColor: 'transparent' }}
          >
            <SlArrowLeft className="w-[20px] h-[20px] md:w-[25px] md:h-[25px] lg:w-[30px] lg:h-[30px]" />
          </button>
          <button
            aria-label="Next"
            className="
              flex
              w-[30px] h-[30px]
              rounded-[50%]
              justify-center items-center
              cursor-pointer
              z-[10]
              p-0
              bg-transparent
              absolute
              right-2 
              md:bottom-[-1.3%] 
              xl:bottom-[-1%]
              2xl:bottom-[-1%]
              3xl:bottom-[-.1%]
              text-black
              hover:bg-transparent
            "
            onClick={() => {
              const nextIndex = currentSlide === images.length - 1 ? currentSlide : currentSlide + 1; // Aumenta el índice actual en 1
              setCurrentSlide(nextIndex); // Actualiza el índice actual
              slider?.slickGoTo(nextIndex);
              console.log(nextIndex);
            }}
          >
            <SlArrowRight className="w-[20px] h-[20px] md:w-[25px] md:h-[25px] lg:w-[30px] lg:h-[30px]" />
          </button>

          {/* SLIDER */}
          {/* eslint-disable-next-line @typescript-eslint/no-shadow */}
          <Slider {...settings} ref={(slider) => setSlider(slider)} initialSlide={initialSlide}>
            {/* IMAGES */}
            {images.map((img, i) => (
              <Slide img={img} key={i} toggleModal={toggleModal} i={i} />
            ))}
          </Slider>
        </div>
      </div>
    </>
  );
}

interface SlideProps {
  img: StaticImageData;
  toggleModal: () => void;
  i: number;
}

function Slide({ img, toggleModal, i }: SlideProps) {
  return (
    <div
      className="
        flex
        justify-center
        w-full h-full
        sticky
        px-[1px]
      "
    >
      <div
        onClick={toggleModal}
        className="
          absolute right-[15px] top-[15px]
          cursor-pointer
          text-black
          rounded-[50%]
          p-1
          bg-white
          z-[100]
        "
      >
        <AiOutlineCloseCircle className="w-[30px] h-[30px]" />
      </div>
      <Image
        src={img}
        // alt="img"
        alt={`img-dsk-${i}`}
        priority={i === 0}
        className="w-[1500px] h-[full]  justify-self-center rounded-t-[15px] object-contain"
      />
    </div>
  );
}
