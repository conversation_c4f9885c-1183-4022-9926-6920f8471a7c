'use client';
import { Box, Divider, Flex, Grid, Heading, Text } from '@chakra-ui/react';
import Link from 'next/link';
import CustomButton from '@/components/CustomButton';
import { COUNTRY_DATA } from '@/constants';

export default function Header({ country }: { country: string }) {
  return (
    <Flex
      w="100%"
      h={{ base: '100%', md: '90vh' }}
      minH={{ base: '100%', md: '600px' }}
      py={{ base: 35, md: 0 }}
      sx={{ '> *:not(:last-child)': { mb: { base: 3, md: 0 } } }}
      position="relative"
      overflow={'hidden'}
      flexDir={{ base: 'column', md: 'row' }}
      alignItems={'center'}
      overflowX={'hidden'}
    >
      <Flex
        w={{ base: '100%', md: '850px' }}
        flexDir="column"
        zIndex={1}
        pl={{ base: 5, md: '50px', lg: '100px' }}
        fontFamily="Plus-Jakarta-Sans"
        sx={{
          '> *:not(:last-child)': {
            mb: { base: 3, md: 10 },
          },
        }}
      >
        <Flex
          w={{ base: '330px', md: '360px' }}
          h="40px"
          bgColor="rgba(195, 198, 255, 0.2)"
          borderRadius="20px"
          color={'purple.opaque'}
          alignItems="center"
          justifyContent="center"
        >
          <Heading as="h2" fontSize={{ base: '14px', md: '15px' }} fontWeight={400} lineHeight="20.49px">
            {country === 'MX' ? COUNTRY_DATA.MX.home.hero.text : COUNTRY_DATA.US.home.hero.text}
          </Heading>
        </Flex>

        <Heading
          as="h1"
          w={{ sm: '80%', md: '100%' }}
          lineHeight={'55px'}
          fontWeight={500}
          color="text.main"
          fontSize="40px"
          fontFamily="Plus-Jakarta-Sans"
        >
          {country === 'MX' ? COUNTRY_DATA.MX.home.hero.title : COUNTRY_DATA.US.home.hero.title}
          <Text as={'span'} color={'purple.strong'} fontWeight={600}>
            {' '}
            {country === 'MX' ? COUNTRY_DATA.MX.home.hero.purple : COUNTRY_DATA.US.home.hero.purple}
          </Text>
        </Heading>

        <Divider
          display={{ base: 'none', md: 'initial' }}
          w="42px"
          border={'2px solid #9E8EFF'}
          borderRadius="3px"
          fontFamily={'Plus Jakarta Sans'}
        />
        <Text display={{ base: 'none', md: 'initial' }} color="text.main">
          {country === 'MX' ? COUNTRY_DATA.MX.home.hero.subtitle : COUNTRY_DATA.US.home.hero.subtitle}
        </Text>

        <Box display={{ base: 'none', md: 'initial' }}>
          <Link prefetch={false} href="/mx/autos">
            <CustomButton
              message={country === 'MX' ? COUNTRY_DATA.MX.home.hero.button : COUNTRY_DATA.US.home.hero.button}
              className="btn-purple"
            />
          </Link>
        </Box>
      </Flex>
      <Box w="100%" h="100%" display={'grid'} alignItems="center" position="relative">
        <Grid
          w="100%"
          h="100%"
          zIndex={3}
          top={{ base: 0, md: '10%' }}
          right={{ base: '0px', md: '-12%' }}
          position={{ base: 'inherit', l: 'absolute' }}
        >
          <Flex
            h={{ base: '300px', md: '90%' }}
            alignItems="start"
            backgroundImage="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fsentra.webp?alt=media&token=704d4dc8-1a6b-4565-88b5-6fb7a85bce4d"
            backgroundRepeat="no-repeat"
            backgroundPosition={{ base: 'center', l: 'right' }}
            backgroundSize="contain"
            aria-label="sentra"
          />
        </Grid>

        <Box
          w="100%"
          h={{ base: '50vh', md: '100%' }}
          position="absolute"
          backgroundImage="https://firebasestorage.googleapis.com/v0/b/onecarnow-dcf84.appspot.com/o/home%2Fdecore.webp?alt=media&token=08b31d1c-13d1-4ea9-8a4b-2d5af5a713f9"
          backgroundRepeat="no-repeat"
          top={-100}
          right={0}
          backgroundPosition="right"
          display={{ base: 'none', l: 'initial' }}
          backgroundSize={'contain'}
        />
      </Box>
      <Box display={{ base: 'initial', md: 'none' }}>
        <Link href="/mx/autos">
          <CustomButton
            message={country === 'MX' ? COUNTRY_DATA.MX.home.hero.button : COUNTRY_DATA.US.home.hero.button}
            className="btn-purple"
          />
        </Link>
      </Box>
    </Flex>
  );
}
