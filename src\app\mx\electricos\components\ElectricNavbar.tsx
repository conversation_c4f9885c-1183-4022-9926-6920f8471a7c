'use client';
/* eslint-disable @typescript-eslint/no-use-before-define */
import {
  Box,
  Flex,
  Image,
  List,
  ListItem,
  // Button,
  useDisclosure,
  Drawer,
  Drawer<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>er<PERSON><PERSON>,
  IconButton,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { RiMenu3Fill } from 'react-icons/ri';
import logoOcn from '@/assets/logo.webp';
import { useRouter } from 'next/navigation';
import ReserveNowBtnMX from '@/components/custom/ui/ReserveNowBtnMX';
import useHandleResize from '@/components/useHandleResize';

const handleSmoothScroll = (
  event: React.MouseEvent<HTMLButtonElement | HTMLLIElement>,
  targetId: string
): void => {
  event.preventDefault();
  const targetElement = document.getElementById(targetId);
  if (targetElement) {
    const offset = window.innerWidth < 768 ? 60 : 90;
    window.scrollTo({
      top: targetElement.offsetTop - offset,
      behavior: 'smooth',
    });
    window.history.pushState(null, '', `#${targetId}`);
  }
};

export default function ElectricNavbar() {
  const router = useRouter();
  const [navbarStyle, setNavbarStyle] = useState<React.CSSProperties>({
    backgroundColor: 'white',
    color: 'black',
    transition: 'all 0.3s',
  });
  const isMobile = useHandleResize({ breakpoint: 768 });
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const vehiclesSection = document.getElementById('nuestros-vehiculos')!.offsetTop;
      const windowHeight = window.innerHeight;
      const navbarHeight = isMobile ? 61 : 91;

      if (
        scrollPosition > vehiclesSection - navbarHeight &&
        scrollPosition < vehiclesSection - navbarHeight + windowHeight
      ) {
        // Si está en la sección "vehicles", cambia el estilo de la navbar
        setNavbarStyle({
          backgroundColor: '#1A1A1A',
          color: '#FAFAFF',
          transition: 'all 0.3s',
        });
      } else {
        // Si no está en la sección "vehicles", restaura el estilo original de la navbar
        setNavbarStyle({
          backgroundColor: 'white',
          color: 'black',
          transition: 'all 0.3s',
        });
      }
    };

    // Agrega el evento de desplazamiento al montar el componente
    window.addEventListener('scroll', handleScroll);

    // Limpia el evento al desmontar el componente
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile]);

  useEffect(() => {
    window.scrollTo({ top: 0, left: 0 });
  }, []);

  return (
    <Flex
      as="nav"
      w="100%"
      h={{ base: '60px', md: '90px' }}
      zIndex={20}
      position="sticky"
      top={0}
      // bgColor="transparent"
      // bgColor="#C3C3C3"
      style={navbarStyle}
      className="bg-white bg-opacity-50"
      justifyContent="space-between"
      alignItems="center"
      pl={{ base: '30px', md: '70px' }}
      pr={{ base: '10px', md: '70px' }}
    >
      <Box
        w={{ base: '70px', md: '100px' }}
        h="35px"
        mt={{ base: '15px', md: 0 }}
        cursor="pointer"
        onClick={() => router.push('/mx')}
      >
        <Image src={logoOcn.src} alt="logo" />
      </Box>
      <List
        display={{ base: 'none', md: 'flex' }}
        alignItems="center"
        fontSize={14}
        gap="50px"
        fontFamily="Plus-Jakarta-Sans"
      >
        <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'nuestros-vehiculos')}>
          Nuestros vehículos
        </ListItem>
        {/* <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'ocn-electricos')}>
          Ahorros potenciales
        </ListItem> */}
        <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'about')}>
          Todo sobre tu NETA
        </ListItem>
        {/* <ListItem> */}
        <ReserveNowBtnMX
          // style={{ backgroundColor: '#5A00F8, #A74DF9' }}
          className="w-[max-content] px-[20px] "
          onClick={(e) => {
            handleSmoothScroll(e, 'reservar');
          }}
        >
          Reservar
        </ReserveNowBtnMX>
        {/* </ListItem> */}
      </List>
      <Menu navbarStyle={navbarStyle} />
    </Flex>
  );
}

function Menu({ navbarStyle }: { navbarStyle: React.CSSProperties }) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  return (
    <>
      <IconButton
        size={'md'}
        icon={<RiMenu3Fill size={32} color={navbarStyle.color === 'black' ? 'rgb(116, 43, 250)' : 'white'} />}
        aria-label={'Open Menu'}
        bgColor="transparent"
        _hover={{
          bgColor: 'transparent',
        }}
        // style={navbarStyle.color === 'black' ? { color: 'black' } : { color: 'white' }}
        display={{ base: 'block', md: 'none' }}
        onClick={onOpen}
      />
      {/* ))} */}

      <Drawer isOpen={isOpen} placement="right" onClose={onClose} size="custom">
        <DrawerOverlay />
        <DrawerContent bgColor="#C3C3C3">
          <DrawerCloseButton position="absolute" size={'lg'} top="30px" right="40px" />
          <DrawerHeader> </DrawerHeader>
          <DrawerBody pt="50px">
            <List display="flex" flexDir="column" mt="20px" justifyContent="end" textAlign="end" gap="20px">
              <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'nuestros-vehiculos')}>
                NUESTROS VEHÍCULOS
              </ListItem>
              {/* <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'ocn-electricos')}>
                OCN ELÉCTRICOS
              </ListItem> */}
              <ListItem m="0" cursor="pointer" onClick={(e) => handleSmoothScroll(e, 'about')}>
                SOBRE TU OCN
              </ListItem>
              <ReserveNowBtnMX
                onClick={(e) => {
                  handleSmoothScroll(e, 'reservar');
                }}
              >
                Reservar
              </ReserveNowBtnMX>
            </List>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </>
  );
}
