/* eslint-disable @typescript-eslint/no-use-before-define */
import { TbArrowsLeftRight, TbEngine } from 'react-icons/tb';
import { IoMdBatteryCharging } from 'react-icons/io';
import { AiOutlineDashboard } from 'react-icons/ai';
import { BsFillLightningChargeFill } from 'react-icons/bs';
import Airbag from '@/assets/icons/Airbag.svg';
// import { Image } from '@chakra-ui/react';
import { IconType } from 'react-icons';
import Image from 'next/image';
// import { useEffect } from 'react';

export interface IAtributes {
  range: string;
  acceleration: string;
  battery: string;
  engine: string;
  charge: string;
  airbags: string;
  fuel: string;
  doors: number;
  airConditioner: boolean;
  seats: number;
  distance: number;
}

const atributes = (feature: IAtributes) => {
  return [
    { atr: 'Range', value: feature.range, icon: TbArrowsLeftRight },
    { atr: 'Acceleration', value: feature.acceleration, icon: AiOutlineDashboard },
    { atr: 'Battery', value: feature.battery, icon: IoMdBatteryCharging },
    { atr: 'Engine', value: feature.engine, icon: TbEngine },
    { atr: 'AC Charge *', value: feature.charge, icon: BsFillLightningChargeFill },
    { atr: 'Airbags', value: feature.airbags, svg: Airbag },
  ];
};

export default function Description({
  carName,
  description,
  features,
}: {
  description: string;
  features: IAtributes;
  carName: string;
}) {
  const attr = atributes(features);
  // useEffect(() => {
  //   window.scrollTo({ top: 0, left: 0 });
  // }, []);

  return (
    <div className="w-full h-[max-content] flex flex-col">
      <p className="text-[#742BFA] text-[28px] xl:text-[40px] font-bold ">{carName}</p>
      <p className="mt-[20px] mb-[40px]">{description}</p>
      <div className="w-full grid grid-cols-2  md:grid-cols-3 text-[#742BFA] gap-y-[30px] ">
        {attr.map((a, i) => (
          <Atributes key={i} atr={a.atr} value={a.value} svg={a.svg && a.svg} icon={a.icon && a.icon} />
        ))}
      </div>
      <p className="mt-[20px] text-[12px] xl:text-[14px]">* Charging installed in your house</p>
    </div>
  );
}

interface AtributesProps {
  atr: string;
  value: string | number;
  icon?: IconType;
  svg?: string;
}

function Atributes({ atr, value, icon: Icon, svg }: AtributesProps) {
  return (
    <div className="flex gap-3">
      <div className="w-[50px] h-[50px] min-w-[50px] flex justify-center items-center rounded-md bg-[#F2EAFF] ">
        {Icon && <Icon size={24} />}
        {svg && <Image src={svg} alt="icon" />}
      </div>
      <div className="flex flex-col">
        <p className=" text-[14px] md:text-[16px] ">{atr}</p>
        <p className=" text-[12px] md:text-[14px] text-[#464E5F] pr-[4px] ">{value}</p>
      </div>
    </div>
  );
}
