/* eslint-disable @typescript-eslint/no-shadow */
/* eslint-disable max-params */
import {
  collection,
  doc,
  DocumentData,
  DocumentReference,
  getDocs,
  setDoc,
  updateDoc,
} from 'firebase/firestore';
import { db, dbMain } from '@/services/firebase';
import { IVehicles } from './interfaces';

// export const customLogEvent = (name: string, content: string) => {
//   logEvent(, name, {
//     description: content,
//   });
// };

export const getAllVehiclesMain = async () => {
  const data: IVehicles[] = [];
  const querySnapshot = await getDocs(collection(dbMain, 'vehiclesMain'));
  querySnapshot.forEach((doc) => {
    const vehicle = doc.data();
    const vehicleData: IVehicles = {
      name: vehicle.name,
      description: vehicle.description,
      payment: vehicle.payment,
      images: vehicle.images,
      plan: vehicle.plan,
      security: vehicle.security,
      seats: vehicle.seats,
      motor: vehicle.motor,
      transmission: vehicle.transmission,
      liters: vehicle.liters,
      interior: vehicle.interior,
      exterior: vehicle.exterior,
      aditionalData: vehicle.aditionalData,
      specialDetails: vehicle.specialDetails,
      uid: doc.id.trim(),
      durationMonths: vehicle.durationMonths,
      planOptions: vehicle.planOptions,
      paymentOptions: vehicle.paymentOptions,
    };
    data.push(vehicleData);
  });

  // console.log(data)
  return data;
};

export const sendFormToFirebase = async (
  id: string,
  values: any,
  isSent: boolean,
  dt?: any,
  vehicle?: string
) => {
  let docRef: DocumentReference<DocumentData>;
  if (process.env.NODE_ENV === 'production') {
    docRef = doc(dbMain, `/submittedForms/${id}`);
  } else docRef = doc(dbMain, `/submittedDev/${id}`);

  if (dt) {
    await setDoc(docRef, {
      name: values.name,
      city: values.city,
      phone: values.phone,
      email: values.email,
      message: values.message,
      isSent,
      status: 'pendiente',
      date: dt,
      vehicle: vehicle ? vehicle : '',
    });
  } else {
    updateDoc(docRef, {
      name: values.name,
      city: values.city,
      phone: values.phone,
      email: values.email,
      message: values.message,
      isSent,
      vehicle: vehicle ? vehicle : '',
    });
  }
};

export const getAllBlogs = async () => {
  const data: any[] = [];
  const querySnapshot = await getDocs(collection(db, 'blogs/allBlogs/public'));

  querySnapshot.forEach((doc) => {
    data.push({ ...doc.data(), uid: doc.id });
  });

  return data;
};
