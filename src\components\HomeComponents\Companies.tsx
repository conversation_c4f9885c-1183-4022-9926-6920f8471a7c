import LogoCompanies from '@/components/Cards/aboutUsCard';
import { FiCheckCircle } from 'react-icons/fi';

export default function Companies() {
  return (
    <>
      <div
        className="
        w-full 
        h-max-content md:h-full 
        py-[50px] 
        flex 
        flex-col 
        items-center 
        justify-center
      "
      >
        <div
          className="
          w-[90%] md:w-[70%] lg:w-[45%]
          h-full 
          flex 
          items-center 
          text-center 
          flex-col 
          font-[Plus-Jakarta-Sans]
          text-text-main
        "
        >
          <p className="text-[24px] md:text-[32px] font-bold lg:leading-[60px]">
            <span className="text-purple-strong font-bold">OCN </span>
            En los medios
          </p>

          <div
            className="
            w-full 
            flex 
            justify-between 
            mt-[50px] 
            font-[Manrope] 
            font-medium 
            text-[15px]
            [&>*:not(:last-child)]:mb-[50px]
          "
          >
            <p className="w-[50%]">
              <FiCheckCircle className="inline-block text-purple-soft mr-1" />
              Alianzas con plataformas
            </p>

            <p className="w-[50%]">
              <FiCheckCircle className="inline-block text-purple-soft mr-1" />
              Innovación tecnológica
            </p>
          </div>

          <p className="mt-[30px] font-[Manrope] font-medium text-[15px]">
            <FiCheckCircle className="inline-block text-purple-soft mr-1" />
            Beneficios por suscripción
          </p>
        </div>
      </div>

      <LogoCompanies />
    </>
  );
}
