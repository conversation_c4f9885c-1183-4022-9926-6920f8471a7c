import Link from 'next/link';
// import { Img, Text, Flex, Icon } from '@chakra-ui/react';
import { FaInstagram, FaLinkedin /* FaTwitter, FaYoutube,  FaFacebook */ } from 'react-icons/fa';
import { BsTelephone } from 'react-icons/bs';
import ocn from '@/assets/footer/ocn-footer.png';
import { FiMail } from 'react-icons/fi';
import Image from 'next/image';
// import { customLogEvent } from "../middlewares/firebase";

// interface USFooterProps {
//   footerBgColor: 'footer.personal' | 'footer.main' | 'footer.platform';
// }

export default function USFooter() {
  const year = new Date().getFullYear();

  return (
    <footer
      className="
        w-full h-[max-content] md:h-[334px]
        flex flex-col
        items-center
        bg-[#1E1F24]
        text-white
        pb-[20px] lg:pb-0
        transition-all duration-1000 ease-in-out
      "
    >
      <div
        className="
          flex flex-col
          w-[85%] md:w-[90%] lg:w-[80%]
          mt-[50px] md:mt-[80px]
        "
      >
        <div
          className="
            flex flex-col md:flex-row
            transition-all duration-1000 ease-in-out
            items-center md:items-start
            mb-[10px]
            h-[max-content] md:h-[200px]
            space-y-[20px] md:space-y-0
          "
        >
          <Link prefetch={false} href="/us" /* onClick={() => window.scrollTo({ top: 0, left: 0 })} */>
            <Image
              src={ocn}
              width={1000}
              height={1000}
              className="w-[120px] h-[56px] object-contain"
              alt="logo-footer"
            />
          </Link>
          <div
            // flexWrap={{ base: "wrap", md: "initial" }}
            // ml={{ base: 0, md: '20px', lg: '200px' }}
            // w={{ base: '90%', md: '100%' }}
            // sx={{ '> *:not(:last-child)': { mr: { md: 10 }, mb: { base: 10, md: 0 } } }}
            /* mb={{ base: 10, md: 0 }}
            justifyContent={{ base: 'center', md: 'space-evenly', lg: 'space-around' }}
            align={{ base: 'center', md: 'start' }}
            py={{ base: 10, md: 0 }}
            fontFamily="Plus-Jakarta-Sans" */
            className="
              flex flex-col md:flex-row
              w-[90%] md:w-full
              space-y-10 md:space-y-0 md:space-x-10
              ml-0 md:ml-[20px] lg:ml-[200px]
              mb-10 md:mb-0
              justify-center md:justify-evenly lg:justify-around

            "
          >
            <div
              // flexDir={'column'}
              // sx={{ '> *:not(:last-child)': { mb: 2 } }}
              // textAlign={{ base: 'center', md: 'start' }}
              // fontSize="15px"
              className="flex flex-col space-y-2 text-center md:text-start text-[15px] "
            >
              <p className="cursor-pointer font-bold text-[18px] mb-2 font-[Manrope]">OCN</p>
              <Link
                prefetch={false}
                href="/us/about" /* onClick={() => window.scrollTo({ top: 0, left: 0 })} */
              >
                <p className="mt-2">About us</p>
              </Link>
              <Link prefetch={false} href="https://blog.onecarnow.com/us">
                <p>Blog</p>
              </Link>
              {/* <Link href="/noticias" onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <Text>Noticias</Text>
              </Link> */}
              {/* <Link _hover={{ textDecor: "none" }} target="_blank" href="https://mx.linkedin.com/company/onecarnow" >
                            <Text>Unete al equipo</Text>
                            </Link> */}
              {/* <Link href="/enviar-cv" onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <Text>Unete al equipo</Text>
              </Link> */}
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-start text-[15px]">
              <p className="font-bold text-[18px]">Resources</p>
              <Link
                prefetch={false}
                href="/us/faq" /* onClick={() => window.scrollTo({ top: 0, left: 0 })} */
              >
                <p className="mt-2">FAQ</p>
              </Link>
              {/* <Link href="/politica-de-privacidad" onClick={() => window.scrollTo({ top: 0, left: 0 })}>
                <Text>Aviso de privacidad</Text>
              </Link> */}
            </div>
            <div className="flex flex-col space-y-2 text-center md:text-start text-[15px]">
              <p className="font-bold text-[18px] mb-2">Contact us</p>
              <div /* alignItems={'center'} sx={{ '> *:not(:last-child)': { mr: 2 } }} mt={2} */
                className="flex text-center justify-center space-x-2 items-center "
              >
                <FiMail />
                <Link href="mailto:<EMAIL>" className="mb-1">
                  <EMAIL>
                </Link>
              </div>
              <div
                /* alignItems={'center'}
                justifyContent={{ base: 'center', md: 'initial' }}
                sx={{ '> *:not(:last-child)': { mr: 2 } }} */
                className="flex items-center justify-center mt-2 space-x-2 text-center md:justify-start"
              >
                {/* <Icon as={BsTelephone} /> */}
                <BsTelephone />
                <Link href="tel:+18663386522">+18663386522</Link>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col-reverse justify-center w-full md:flex-row md:justify-between">
          <p className="text-center mt-[50px] md:mt-0 font-[Plus-Jakarta-Sans] text-[13px] font-[500]">
            © Copyright {year} OCN
          </p>
          <div className="flex justify-center custom-mr-3">
            {/* <Link href="https://www.facebook.com/onecarnow" target="_blank">
              <Icon as={FaFacebook} w="24px" h={'24px'} />
            </Link> */}
            <Link href="https://www.instagram.com/onecarnow/" target="_blank" aria-label="Instagram">
              {/* <Icon as={FaInstagram} w="24px" h={'24px'} /> */}
              <FaInstagram className="w-[24px] h-[24px]" />
            </Link>
            {/* <Link href="https://mobile.twitter.com/onecarnow" target="_blank">
              <Icon as={FaTwitter} w="24px" h={'24px'} />
            </Link>
            <Link href="https://www.youtube.com/@onecarnow416" target="_blank">
              <Icon as={FaYoutube} w="24px" h={'24px'} />
            </Link> */}
            <Link href="https://www.linkedin.com/company/onecarnow/" target="_blank" aria-label="Linkedin">
              {/* <Icon as={FaLinkedin} w="24px" h={'24px'} /> */}
              <FaLinkedin className="w-[24px] h-[24px]" />
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
